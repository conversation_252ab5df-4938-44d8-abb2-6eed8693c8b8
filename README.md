# Keycloak Bodhi Extension

A production-ready Keycloak extension implementing **multi-tenant marketplace OAuth2 token exchange** with dynamic audience management using Keycloak's standard token exchange v2 (RFC 8693).

## 🎯 Core Problem Solved

**Multi-tenant marketplace scenario**: M apps × N resources = potentially M×N audience mapping combinations

**Our solution**: Dynamic scope-based audience resolution with O(N) complexity instead of O(M×N)

## 🚀 Key Features

### ✅ Standard Token Exchange V2 (RFC 8693)
- Uses Keycloak's built-in token exchange (no custom policies needed)
- Dynamic audience resolution through client scopes
- Production-ready, no preview features

### ✅ Multi-Tenant Architecture
- **App Clients**: Public clients for frontend applications
- **Resource Clients**: Confidential clients with full role hierarchy
- **Dynamic Audience**: On-demand scope-based resource access

### ✅ Performance Optimized
- Scope-based design scales efficiently
- No database explosion with static mappers
- Leverages Keycloak's built-in caching

## 🔧 Quick Start

```bash
# Start Keycloak with token exchange enabled
make dev-up

# Test with HTTPyac
httpyac httpyac-scripts/token-exchange-v2-aud.http --all
```

## 📚 Documentation

**Complete documentation is available in [ai-docs/](ai-docs/)**

### Quick Navigation
- **[System Overview](ai-docs/03-context/05-system-overview.md)** - Comprehensive system architecture
- **[Token Exchange V2 Support](ai-docs/01-features/token-exchange-v2-support.md)** - Migration and implementation details
- **[Domain Model](ai-docs/03-context/02-domain-model-token-exchange.md)** - Technical architecture details
- **[Documentation Index](ai-docs/README.md)** - Complete navigation guide

## 🎮 Demo Flow

### 1. Create Resource Server
```http
POST /realms/bodhi/bodhi/resources
{
  "name": "Inventory Service",
  "description": "Product inventory management",
  "redirect_uris": ["http://localhost:8080/callback"]
}
```

### 2. Create App Client
```http
POST /realms/bodhi/bodhi/apps
{
  "name": "Marketplace App",
  "description": "E-commerce marketplace",
  "redirect_uris": ["http://localhost:3000/callback"]
}
```

### 3. Request Access
```http
POST /realms/bodhi/bodhi/resources/request-access
{
  "app_client_id": "app-123"
}
```

### 4. Token Exchange
```http
POST /realms/bodhi/protocol/openid-connect/token
grant_type=urn:ietf:params:oauth:grant-type:token-exchange
&subject_token={app_user_token}
&audience=resource-456
&client_id=resource-456
&client_secret={resource_secret}
```

## 🔐 Security Features

- **RFC 8693 Compliant**: Standard OAuth2 token exchange
- **Consent-Based**: Users explicitly consent to resource access
- **Multi-Tenant Isolation**: Client-specific groups and roles
- **Audit Trail**: Complete OAuth2 flow logging

## 🧪 Testing

### Integration Tests
```bash
./mvnw surefire:test
```

### HTTPyac Scripts
```bash
httpyac httpyac-scripts/token-exchange-v2-aud.http --all
```

### Specific Test
```bash
./mvnw surefire:test -Dtest=TokenExchangeIntegrationTest
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   App Client    │    │   User Token    │    │ Resource Server │
│   (Public)      │───▶│   Exchange      │◀───│ (Confidential)  │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ OAuth2 + PKCE   │    │ Standard Token  │    │ Service Account │
│ Consent Flow    │    │ Exchange V2     │    │ Authentication  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📈 Performance

- **O(N) Complexity**: Scales with number of resources, not app×resource combinations
- **Dynamic Audience**: Only requested resources in token audience
- **Efficient Caching**: Leverages Keycloak's built-in scope resolution

## 🤝 Contributing

This project demonstrates advanced OAuth2 patterns for marketplace scenarios. See [ai-docs/](ai-docs/) for comprehensive documentation including:
- Development conventions
- Testing patterns
- API design guidelines
- Architecture decisions

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Note**: This implementation showcases a production-ready solution for complex multi-tenant marketplace OAuth2 scenarios using modern Keycloak features.