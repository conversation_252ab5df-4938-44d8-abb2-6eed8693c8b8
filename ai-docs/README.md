# Keycloak Bodhi Extension Documentation Index

## Overview

This documentation index provides comprehensive navigation for the Keycloak Bodhi Extension documentation system. The documentation is organized into logical sections to help you quickly find the specific information you need for developing, maintaining, and extending this Keycloak SPI implementation.

This extension implements a unique multi-tenant OAuth2 token exchange system with two distinct client types:
- **App/Frontend Clients**: Public clients (no client secrets) created via `POST /apps` endpoint with simplified admin-only role structure
- **Resource Server Clients**: Confidential clients (with client secrets) created via `POST /resources` endpoint with full 4-level role hierarchy

The system uses Keycloak's standard token exchange v2 (RFC 8693) with dynamic scope-based audience resolution, enabling secure cross-client authentication flows without custom policy implementations.

## Documentation Organization

The documentation is structured into the following main sections:
- **⚡ Features** - Feature specifications, implementation guides, and development stories
- **🔬 Research** - Technical research, analysis, and architectural investigations
- **📚 Context** - Project context, architecture, and development patterns

### Recent Documentation Updates

**Token Exchange V2 Migration**: Completed migration from custom AudienceMatchPolicy to standard Keycloak token exchange v2 (RFC 8693). This eliminates preview features, reduces complexity, and provides production-ready OAuth2 token exchange with dynamic scope-based audience resolution.

**System Overview**: Comprehensive system overview document covering the unique multi-tenant marketplace OAuth2 architecture with dynamic audience management for app-to-resource connections.

## Quick Navigation

### ⚡ [Features](01-features/) - Feature Specifications & Implementation Guides
Feature documentation organized by implementation status and development timeline

#### Architecture & Migration
- **[Token Exchange V2 Support](01-features/token-exchange-v2-support.md)** - Migration from custom AudienceMatchPolicy to standard Keycloak token exchange v2 (RFC 8693), including technical implementation details, API changes, and performance benefits
- **[Keycloak v2 Migration Analysis](01-features/keycloak-v2-migration-analysis.md)** - Comprehensive analysis of migrating from Keycloak v1 to v2 architecture, including breaking changes, migration strategies, and implementation roadmap

#### Quality Assurance & Testing
- **[Test Coverage Analysis](01-features/test-coverage.md)** - Comprehensive test coverage analysis covering unit tests, integration tests, and UI testing strategies with specific focus on OAuth flows, client registration, and role-based access control
- **[Test Cleanup Strategy](01-features/test-cleanup.md)** - Test cleanup and refactoring strategy to improve test reliability, reduce duplication, and enhance maintainability with focus on deterministic test execution and proper resource management
- **[Test User Setup Migration](01-features/migrate-test-user-setup.md)** - Migration guide for test user setup patterns, moving from manual configuration to automated seeding with proper role assignments and group memberships

#### Security & CI/CD
- **[Testcontainer Builds](01-features/20250719-testcontainer-builds.md)** - Automated obfuscated builds for integration testing using testcontainers, uses ProGuard, GitHub Actions CI/CD pipeline, and ghcr integration with branch-specific tagging strategy
- **[Production Docker Release System](01-features/docker-release-system.md)** - Comprehensive Docker release system with dual-track image strategy (bodhi-auth-server for production, bodhi-auth-testcontainer for testing), GHCR-first version management, automated multi-platform builds, and GitHub releases

#### Implemented Features
- **[App Client Management](01-features/app-client-management.md)** - OAuth2 public client creation endpoint with simplified configuration for frontend applications
  - Client-level admin role creation with automatic user assignment
  - Group-based user management (users-{client-id}/admins structure)
  - Authentication via client-bodhi-dev-console user tokens
  - Environment-aware client ID prefixes (app-/test-app-)
  - Integrated UI testing with Playwright automation
- **Resource Client Management** - Full-featured OAuth2 confidential client creation with service accounts, comprehensive role hierarchy (resource_user/resource_power_user/resource_manager/resource_admin), and standard token exchange v2 support
- **Multi-Tenant Token Exchange** - RFC 8693 compliant token exchange with dynamic scope-based audience resolution converting app client tokens to resource server tokens
- **User Group Management** - Role-based group assignment system with hierarchical permissions and client-specific isolation
- **Authentication & Authorization** - JWT-based authentication with service account validation, user token verification, and client-specific permission enforcement

### 🔬 [Research](02-research/) - Technical Research & Analysis
Research documentation for technical investigations and architectural decisions

#### Access Control & Security
- **[Access Control Analysis](02-research/access-control.md)** - Comprehensive analysis of access control patterns, role-based permissions, and security considerations for multi-tenant OAuth2 systems

### 📚 [Context](03-context/) - Project Context & Architecture
Foundational documentation covering project architecture, patterns, and development conventions

#### Core Architecture
- **[Project Architecture](03-context/01-project-architecture.md)** - High-level system architecture, component relationships, and design principles
- **[Domain Model & Token Exchange](03-context/02-domain-model-token-exchange.md)** - Detailed domain model covering multi-tenant OAuth2 architecture, client types, role systems, and token exchange flows
- **[System Overview](03-context/05-system-overview.md)** - Comprehensive system overview covering the unique multi-tenant marketplace OAuth2 architecture with dynamic audience management

#### Development Patterns
- **[Test Framework Patterns](03-context/03-test-framework-patterns.md)** - Testing patterns, frameworks, and best practices for integration testing, UI testing, and test data management
- **[Development Conventions - API](03-context/04-development-conventions-api.md)** - API design patterns, request/response models, error handling, and development conventions
- **[HTTPyac Conventions](03-context/httpyac-conventions.md)** - HTTPyac testing conventions, environment management, script organization, and testing patterns for OAuth2 flows
- **[Release Process](03-context/07-release-process.md)** - Comprehensive release process documentation including GHCR-first version management, multi-platform Docker builds, automated release workflows, and production vs testcontainer strategies

#### Security & Build
- **[Obfuscation Guide](03-context/06-obfuscation-guide.md)** - Java code obfuscation implementation using ProGuard for protecting intellectual property from reverse engineering in production Docker images

## Key Features & Capabilities

### 🎯 Multi-Tenant OAuth2 Architecture
- **Dynamic Audience Management**: Scope-based audience resolution for M×N app-to-resource connections
- **Standard Token Exchange**: RFC 8693 compliant token exchange using Keycloak's built-in features
- **Consent-Based Authorization**: Users explicitly consent to resource access through OAuth2 scopes
- **Performance Optimized**: O(N) complexity instead of O(M×N) with dynamic scope resolution

### 🔐 Security & Compliance
- **RFC 8693 Compliance**: Full OAuth 2.0 Token Exchange specification compliance
- **Scope-Based Access Control**: Granular consent-based access control with resource-specific scopes
- **Multi-Tenant Isolation**: Client-specific groups and roles with proper isolation
- **Audit Trail**: Complete OAuth2 flow logging and consent tracking

### 🚀 Production Ready
- **No Preview Features**: Uses stable Keycloak features instead of preview functionality
- **Scalable Architecture**: Handles marketplace-scale deployments efficiently
- **Standard Compliance**: Follows OAuth 2.0 Security Best Current Practice
- **Comprehensive Testing**: Integration tests, UI tests, and HTTPyac script validation

## Getting Started

### Quick Start
```bash
# Start Keycloak with token exchange enabled
make dev-up

# Test with HTTPyac
httpyac httpyac-scripts/token-exchange-v2-aud.http --all
```

### Documentation Navigation
1. **New to the project?** Start with [System Overview](03-context/05-system-overview.md)
2. **Understanding the architecture?** Read [Domain Model & Token Exchange](03-context/02-domain-model-token-exchange.md)
3. **Implementing features?** Check [Token Exchange V2 Support](01-features/token-exchange-v2-support.md)
4. **Setting up testing?** Review [Test Framework Patterns](03-context/03-test-framework-patterns.md)
5. **Testcontainer secure builds?** See [Testcontainer Builds](01-features/20250719-testcontainer-builds.md) and [Obfuscation Guide](03-context/06-obfuscation-guide.md)
6. **Production releases?** Check [Production Docker Release System](01-features/docker-release-system.md) and [Release Process](03-context/07-release-process.md)

## Contributing

This project demonstrates advanced OAuth2 patterns for marketplace scenarios. Contributions focusing on:
- Performance optimizations
- Additional security patterns
- Extended testing scenarios
- Documentation improvements

are welcome!

---

**Note**: This documentation covers a production-ready solution for complex multi-tenant marketplace OAuth2 scenarios using modern Keycloak features. The dynamic audience approach scales efficiently while maintaining security and user consent requirements. 