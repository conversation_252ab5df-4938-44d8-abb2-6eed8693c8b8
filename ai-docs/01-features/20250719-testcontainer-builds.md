# Feature: Obfuscated Builds for Integration Testing

**Epic**: CI/CD Pipeline Security Enhancement  
**Story ID**: BODHI-2025-007  
**Created**: 2025-01-19  
**Status**: Implementation Complete  

## Story

**As a** development team  
**We want** automated obfuscated builds with flexible branch-based tagging strategy  
**So that** we can use secure, reverse-engineering resistant Docker images for integration testing using testcontainer with proper version management for main, release, and feature branches while not exposing our Auth Server implementation in the process

## Background

Our Keycloak Bodhi SPI extension contains proprietary business logic that needs protection from reverse engineering. While our repository is private, we need obfuscated builds for integration testing environments where the Docker images may be more exposed.

## Requirements

### Functional Requirements

1. **Automated Obfuscated Builds**
   - GitHub Actions workflow triggers automatically only on push to `main` branch
   - Manual workflow dispatch supports any branch selection via "Use workflow from" functionality
   - Uses ProGuard for Java bytecode obfuscation
   - Publishes to GitHub Container Registry (ghcr.io) as `bodhi-auth-server`

2. **Dynamic Docker Image Tagging Strategy**
   - **main branch**: uses `main` tag for latest stable builds
   - **release branches** (release/vX.Y.Z): uses `vX.Y.Z` tag for version releases
   - **other branches**: uses short SHA (7-char) tag for feature/development branches
   - All builds also tagged with short SHA for traceability

3. **Build Pipeline Integration**
   - Complex shell logic in Makefile with `ci.*` targets
   - Environment variables for configuration
   - Security scanning integration
   - Build summary generation

### Non-Functional Requirements

1. **Security**
   - ProGuard obfuscation with aggressive settings
   - String encryption for sensitive constants
   - Multi-stage Docker builds for minimal attack surface
   - No log deobfuscation capabilities (not needed for integration testing)

2. **Performance**
   - Maven dependency caching
   - Docker layer optimization
   - Build time < 10 minutes

3. **Reliability**
   - Automated security scanning
   - Build status reporting
   - Error handling and validation

## Acceptance Criteria

### AC1: GitHub Actions Workflow
- [x] Workflow triggers automatically only on push to `main` branch
- [x] Ignores documentation-only changes
- [x] Supports manual workflow dispatch with flexible branch selection
- [x] Uses "Use workflow from" GitHub functionality for branch selection
- [x] Removed unnecessary Java setup (build happens in Docker)
- [x] Authenticates with GitHub Container Registry (ghcr.io)

### AC2: Obfuscated Build Process
- [x] Uses Dockerfile.testcontainer with ProGuard integration
- [x] Downloads ProGuard 7.4.2 automatically
- [x] Applies obfuscation configuration from `proguard-unified.pro`
- [x] Preserves Keycloak SPI interfaces and JAX-RS annotations
- [x] Generates obfuscated JAR (renamed to hide obfuscation)

### AC3: Dynamic Docker Image Management
- [x] Implements dynamic tagging based on branch type
- [x] main branch uses `main` tag for stable releases
- [x] release/vX.Y.Z branches use `vX.Y.Z` tag for version releases
- [x] Other branches use short SHA (7 characters) for development builds
- [x] All builds include SHA tag for traceability
- [x] Pushes both primary and SHA tags to GitHub Container Registry
- [x] Expected image size reduction by 40-60%

### AC4: Makefile Integration
- [x] `ci.setup-obfuscation` validates configuration files
- [x] `ci.build-testcontainer` builds Docker image with dynamic tagging
- [x] `ci.push-testcontainer` pushes both primary and SHA tags to registry
- [x] `ci.security-scan` runs vulnerability scanning on primary tag
- [x] Removed `ci.update-branch-tag` (integrated into build process)
- [x] All targets include proper error handling and validation

### AC5: Security Features
- [x] Class names obfuscated (except SPI interfaces)
- [x] Method and field names scrambled
- [x] String constants encrypted
- [x] Package structure flattened
- [x] Debug information removed
- [x] Multi-stage build removes build tools

### AC6: Integration Testing Support
- [x] Local testing support with docker-compose.yml
- [x] Test container integration examples provided
- [x] Integration tests implemented with JavaScript testcontainers
- [x] Keycloak startup verified with proper wait strategy
- [x] Health checks and endpoint testing implemented
- [x] Simplified build process without obfuscation for faster testing

## Technical Implementation

### Architecture

```
GitHub Push (main) / Manual Dispatch (any branch) → GitHub Actions → Makefile CI Targets → Docker Build → ProGuard → GitHub Container Registry (ghcr.io)
```

### Key Components

1. **GitHub Actions Workflow** (`.github/workflows/testcontainer.yml`)
   - Automatic trigger only on main branch pushes
   - Manual workflow dispatch with flexible branch selection
   - Dynamic tagging logic based on branch type
   - Docker-only build (no Java setup needed)
   - GitHub Container Registry authentication and publishing
   - Enhanced build summary with tagging strategy

2. **Makefile CI Targets** 
   - `ci.setup-obfuscation`: Environment validation
   - `ci.build-testcontainer`: Docker image building with dynamic tagging
   - `ci.push-testcontainer`: Image publishing to GitHub Container Registry
   - `ci.security-scan`: Vulnerability scanning with Trivy/Docker Scout
   - Removed `ci.update-branch-tag` (integrated into build process)

3. **ProGuard Configuration** (`proguard-unified.pro`)
   - Aggressive obfuscation with SPI interface preservation
   - String encryption and package flattening
   - JAR output renamed to hide obfuscation
   - Library JAR specifications for Keycloak

4. **Docker Configuration** (`Dockerfile.testcontainer`)
   - Multi-stage build: dependencies → build → obfuscate → optimize → runtime
   - ProGuard 7.4.2 integration
   - JAR renaming for security
   - Minimal runtime image with health checks

### Test Container Integration

When using obfuscated builds with test containers:

1. **Container Configuration**
   ```java
   @Testcontainers
   class BodhiAppIntegrationTest {
     @Container
     static GenericContainer<?> keycloak = new GenericContainer<>("ghcr.io/bodhisearch/bodhi-auth-server:main")
       .withExposedPorts(8080)
       .withEnv("KEYCLOAK_ADMIN", "admin")
       .withEnv("KEYCLOAK_ADMIN_PASSWORD", "admin")
       .withCommand("start-dev");
   }
   ```

2. **Health Check Verification**
   ```java
   @Test
   void testKeycloakHealth() {
     String healthUrl = "http://localhost:" + keycloak.getMappedPort(8080) + "/health/ready";
     // Verify health endpoint responds correctly
   }
   ```

3. **SPI Endpoint Testing**
   ```java
   @Test
   void testObfuscatedBodhiSPI() {
     String baseUrl = "http://localhost:" + keycloak.getMappedPort(8080);
     // Test Bodhi SPI endpoints work with obfuscated code
   }
   ```

### Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `REGISTRY` | Docker registry URL | `ghcr.io` |
| `IMAGE_NAME` | Docker image name | `bodhisearch/bodhi-auth-server` |
| `GIT_SHA` | Git commit SHA | `abc1234567890...` |
| `GIT_BRANCH` | Git branch name | `main`, `release/v1.0.20250720`, `feature/xyz` |
| `DOCKER_TAG` | Primary Docker tag | `main`, `v1.0.20250720`, `abc1234` |
| `SHORT_SHA` | Short commit SHA | `abc1234` |

## Definition of Done

- [x] All acceptance criteria met (implementation complete)
- [x] GitHub Actions workflow created and updated for simplified builds
- [x] Makefile CI targets implemented with error handling
- [x] Dockerfile.testcontainer created with simplified build process
- [x] Simplified build process without obfuscation for faster testing
- [x] JavaScript testcontainer integration tests implemented
- [x] Documentation updated in ai-docs (github-testcontainers.md)
- [x] ENTRYPOINT/CMD pattern implemented for user customization
- [x] Container ready strategy optimized with .well-known endpoint
- [x] Admin user/password constants renamed for consistency
- [x] Health endpoints enabled and tested
- [x] Integration tests demonstrate full functionality

## Risk Assessment

### High Risk
- **ProGuard Configuration Errors**: Incorrect keep rules could break SPI functionality
  - *Mitigation*: Comprehensive testing with obfuscated images
  - *Detection*: Automated integration tests

### Medium Risk
- **Docker Build Failures**: ProGuard download or execution issues
  - *Mitigation*: Robust error handling in Makefile targets
  - *Detection*: GitHub Actions build notifications

### Low Risk
- **Image Size Increase**: Obfuscation might increase JAR size
  - *Mitigation*: Monitor image sizes, optimize ProGuard settings
  - *Detection*: Build metrics tracking

## Dependencies

- ProGuard 7.4.2 (external dependency)
- GitHub Actions runner with Docker support

## Success Metrics

- Build success rate: >95%
- Image size reduction: 40-60%
- Build time: <10 minutes
- Security scan pass rate: 100% (no critical vulnerabilities)
- Integration test pass rate: 100%

## Multi-Platform Build Support

### Architecture Support

Testcontainer images now support multi-platform builds for enhanced compatibility:

- **linux/amd64**: Intel/AMD 64-bit architecture (traditional x86_64)
- **linux/arm64**: ARM 64-bit architecture (Apple Silicon M1/M2, AWS Graviton, etc.)

### Platform Selection

Docker automatically selects the appropriate image variant based on the host platform:

```bash
# On Intel Mac or x86_64 Linux - pulls amd64 variant
docker pull ghcr.io/bodhisearch/bodhi-auth-testcontainer:main

# On Apple Silicon Mac or ARM64 Linux - pulls arm64 variant  
docker pull ghcr.io/bodhisearch/bodhi-auth-testcontainer:main

# Force specific platform for testing cross-platform compatibility
docker pull --platform linux/amd64 ghcr.io/bodhisearch/bodhi-auth-testcontainer:main
docker pull --platform linux/arm64 ghcr.io/bodhisearch/bodhi-auth-testcontainer:main
```

### Build Process Optimization

Multi-platform builds use Docker Buildx with optimized cross-compilation:

```dockerfile
# Build stage uses host platform for faster compilation
FROM --platform=$BUILDPLATFORM openjdk:21-jdk-slim AS deps

# Runtime stage creates platform-specific images
FROM quay.io/keycloak/keycloak:26.2.5
# Keycloak base image natively supports multi-platform
```

### Benefits for Testing

- **Native Performance**: No emulation overhead on ARM64 development machines
- **Cross-Platform Testing**: Verify compatibility across different architectures
- **Cloud Integration**: Efficient testing on ARM-based CI runners and cloud instances
- **Developer Experience**: Seamless experience on Apple Silicon Macs

### Technical Implementation

```makefile
# Multi-platform build with automatic registry push
docker buildx build \
    -f Dockerfile.testcontainer \
    --platform linux/amd64,linux/arm64 \
    --build-arg BUILD_DATE="$$BUILD_DATE" \
    -t "$$DOCKER_REGISTRY/$$IMAGE_NAME:$$DOCKER_TAG" \
    --push \
    .
```

## Future Enhancements

1. **Advanced Security**: Enhanced vulnerability scanning with blocking thresholds
2. **Build Optimization**: Parallel builds, better caching strategies  
3. **Security Monitoring**: Runtime integrity checks, tamper detection
4. **Additional Platforms**: Support for more architectures (linux/arm/v7, etc.) as needed
5. **Performance Metrics**: Build time tracking and cross-platform performance analysis

---

**Status**: Implementation Complete  
**Last Updated**: 2025-01-22

## Changes Made

### 2025-01-22 Updates
- **Removed ProGuard obfuscation** from testcontainer builds to focus on functionality testing and faster build times
- **Simplified Dockerfile** by removing redundant COPY operations in builder stage
- **Updated GitHub Actions workflow** to remove obfuscation setup step
- **Implemented JavaScript testcontainer tests** with proper wait strategies and health checks
- **Added ENTRYPOINT/CMD pattern** allowing users to override default configuration
- **Renamed constants** from `KEYCLOAK_ADMIN_*` to `ADMIN_*` for consistency
- **Optimized container ready strategy** using `.well-known/openid-configuration` endpoint
- **Updated documentation** in `github-testcontainers.md` to reflect simplified approach