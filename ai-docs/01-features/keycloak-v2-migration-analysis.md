# Keycloak v26 Migration Analysis: AudienceMatchPolicy to Token Exchange v2

## Executive Summary

This document analyzes the current Keycloak v23 implementation that uses a custom `AudienceMatchPolicyProvider` and outlines the migration strategy to Keycloak v26 with standard Token Exchange v2. The primary goal is to remove the custom policy provider and leverage the built-in token exchange functionality while maintaining all existing functional capabilities.

## Current Implementation Analysis

### 1. AudienceMatchPolicyProvider (Custom Policy)

**Location**: `src/main/java/com/bodhisearch/AudienceMatchPolicyProvider.java`

**Current Functionality**:
- Validates token exchange requests by checking Authorization header
- Verifies Bearer tokens for audience matching
- Ensures the client_id in the token matches the expected audience
- Used as a custom policy in realm-management authorization settings

**Key Issues**:
- Custom implementation that duplicates standard OAuth2 functionality
- Requires maintenance and testing of custom code
- May not be compatible with future Keycloak versions
- Adds complexity to the authorization flow

### 2. Token Exchange Implementation

**Current Setup** (Legacy v1):
- Uses custom `audience-match-policy` in realm-management authorization settings
- Requires Authorization header with Bear<PERSON> token for validation
- Implemented in `BaseTest.exchangeToken()` method
- Relies on custom policy for audience validation

**Migration Target** (Standard v2):
- Leverage Keycloak's built-in Token Exchange v2
- Enable "Standard token exchange" setting on clients
- Remove dependency on custom policy provider
- Use standard token exchange parameters and flow

### 3. Test Structure Issues

#### Current Problems:
1. **Heavy Static Data Dependency**: Tests rely on `bodhi-realm-generated.json` and `bodhi-realm-setup.ftl`
2. **Mixed Approach**: Some tests create dynamic clients (`AppFlowTest`) while others use static ones (`KeycloakScopeTest`)
3. **Static User Dependencies**: Pre-defined users like "<EMAIL>", "<EMAIL>" in realm config
4. **Inconsistent Setup**: Some tests use `registerClientAndReturnTokenPair()` while others use pre-configured clients

#### Good Patterns (Already Implemented):
- `AppFlowTest.testMakeResourceAdmin()` - Creates dynamic client and users
- `AppFlowTest.testGrantUserRoleUser()` - Dynamic user role assignment
- `RegisterResourceTest.testRegisterResource()` - Dynamic client creation with full verification

## Token Exchange v2 Migration Strategy

### 1. Remove AudienceMatchPolicyProvider

**Files to Remove**:
- `src/main/java/com/bodhisearch/AudienceMatchPolicyProvider.java`
- `src/main/java/com/bodhisearch/AudienceMatchPolicyProviderFactory.java`
- `src/main/java/com/bodhisearch/AudienceMatchRepresentation.java`
- `src/main/resources/META-INF/services/org.keycloak.authorization.policy.provider.PolicyProviderFactory`

### 2. Update BodhiResourceProvider

**Required Changes**:
- Remove custom policy creation in `newResourceInternal()` method
- Enable standard token exchange for clients
- Update authorization settings to use standard permissions
- Remove dependency on `audience-match-policy`

### 3. Update Token Exchange Configuration

**Changes in `BodhiResourceProvider.newResourceInternal()`**:
```java
// Remove this section (lines ~175-185):
AuthorizationProvider authorization = session.getProvider(AuthorizationProvider.class);
PolicyStore policyStore = authorization.getStoreFactory().getPolicyStore();
// ... policy deletion code ...
Policy allowExchangePolicy = policyStore.findByName(resourceServer, "audience-match-policy");
Policy policy = clientMgmt.exchangeToPermission(client);
policy.addAssociatedPolicy(allowExchangePolicy);

// Replace with standard token exchange setup:
client.setAttribute("token-exchange.standard", "true");
```

### 4. Update Test Framework

**BaseTest Changes**:
- Remove Authorization header requirement from `exchangeToken()` method  
- Update to use standard token exchange parameters
- Remove dependency on Bearer token validation

## Test Refactoring Task List

### Phase 1: Infrastructure Updates

#### Task 1.1: Update BaseTest for Token Exchange v2
- **File**: `src/test/java/com/bodhisearch/BaseTest.java`
- **Changes**:
  - Remove `resourceTokenAsBearer` parameter from `exchangeToken()` method
  - Update token exchange request to use standard v2 parameters
  - Remove Authorization header setup
  - Test with Keycloak v26 container

#### Task 1.2: Update Keycloak Container Version
- **File**: `src/test/java/com/bodhisearch/BaseTest.java`
- **Changes**:
  - Update from `keycloak:23.0.7` to `keycloak:26.x`
  - Remove `admin-fine-grained-authz` feature flag (enabled by default in v26)
  - Keep `token-exchange` feature flag for now, remove once confirmed v2 is default

### Phase 2: Test Migration to Dynamic Setup

#### Task 2.1: Migrate KeycloakScopeTest to Dynamic Setup
- **File**: `src/test/java/com/bodhisearch/KeycloakScopeTest.java`
- **Current Issue**: Uses static `RESOURCE_ABCD` client and pre-defined users
- **Target**: Create dynamic clients and users for each test
- **Changes**:
  - Replace static client usage with `registerClientAndReturnTokenPair()`
  - Create test users dynamically and assign to appropriate groups
  - Remove dependency on `bodhi-realm-generated.json` users
- **Status**: Pending

#### Task 2.2: Migrate AudienceMatchPolicyProviderTest
- **File**: `src/test/java/com/bodhisearch/AudienceMatchPolicyProviderTest.java`
- **Current Issue**: Tests custom policy provider that will be removed
- **Target**: Convert to functional token exchange tests
- **Changes**:
  - Rename to `TokenExchangeTest.java`
  - Remove custom policy testing methods
  - Focus on standard token exchange functionality
  - Test audience parameter validation (now handled by v2)
  - Test scope restrictions and permissions
- **Status**: Pending

#### Task 2.3: Migrate Static Client Dependencies
- **Files**: Multiple test files using static client constants
- **Current Issue**: Tests depend on pre-configured clients like `RESOURCE_ABCD`, `CLIENT_LMNO`
- **Target**: Dynamic client creation for each test
- **Changes**:
  - `BodhiResourceProviderTest`: Replace static client usage
    - **Completed**:
      - `testAddUserToGroupByOtherClientAdmin()` updated to test correct authorization behavior
      - Test now creates dynamic users and verifies that a user without resource-admin role cannot add users to groups
  - `ManageResourceTest`: Create dynamic clients
  - `RealmSetupTest`: Use dynamic setup instead of static config
- **Status**: Partially Completed

### Phase 3: Remove Static Configuration

#### Task 3.1: Remove Realm Configuration Files
- **Files**: 
  - `src/test/resources/import-files/bodhi-realm-setup.ftl`
  - `src/test/resources/import-files/bodhi-realm-generated.json`
- **Changes**:
  - Remove static realm configuration
  - Remove Keycloak Config CLI import in `BaseTest.importConfigs()`
  - Remove `RealmConfigGenerator` dependency

#### Task 3.2: Remove Static User Dependencies
- **Files**: `src/test/java/com/bodhisearch/templates/RealmConfigGenerator.java`
- **Changes**:
  - Remove static user definitions
  - Update tests to create users dynamically
  - Remove group and role pre-configuration

### Phase 4: Source Code Migration

#### Task 4.1: Update BodhiResourceProvider
- **File**: `src/main/java/com/bodhisearch/BodhiResourceProvider.java`
- **Changes**:
  - Remove custom policy creation in `newResourceInternal()` method
  - Enable standard token exchange for new clients
  - Update authorization settings
  - Remove dependency on `audience-match-policy`

#### Task 4.2: Remove Custom Policy Provider
- **Files**: All AudienceMatchPolicy* files
- **Changes**:
  - Remove custom policy provider classes
  - Remove service provider configuration
  - Update build configuration if needed

### Phase 5: Test Validation and Cleanup

#### Task 5.1: Validate Functional Equivalence
- **Objective**: Ensure all existing functionality works with Token Exchange v2
- **Tests**:
  - Token exchange between clients
  - Audience validation
  - Scope restrictions
  - Permission enforcement
  - Offline token exchange

#### Task 5.2: Performance and Security Validation
- **Objective**: Verify performance and security are maintained or improved
- **Tests**:
  - Token exchange response times
  - Security boundary enforcement
  - Error handling and edge cases

## Risk Assessment

### High Risk Items
1. **Token Exchange Behavior Changes**: Standard v2 may have different validation rules
2. **Permission Model Changes**: Authorization setup may need adjustment
3. **Test Dependencies**: Heavy reliance on static data makes migration complex

### Medium Risk Items
1. **Client Configuration**: Need to enable standard token exchange for all clients
2. **Scope Handling**: Ensure scope restrictions work correctly with v2
3. **Error Handling**: Error messages and codes may change

### Low Risk Items
1. **Test Framework**: Most test utilities can be preserved
2. **User Management**: User and group management should remain unchanged
3. **Basic Authentication**: Standard OAuth2 flows should work without changes

## Success Criteria

1. **Functional Parity**: All current token exchange functionality works with v2
2. **Test Coverage**: All tests pass with dynamic setup (no static configuration)
3. **Code Simplification**: Removal of custom policy provider reduces complexity
4. **Maintainability**: Tests are more maintainable with dynamic setup
5. **Performance**: Token exchange performance is maintained or improved

## Implementation Priority

1. **Phase 1** (Critical): Infrastructure updates and v26 compatibility
2. **Phase 2** (High): Test migration to dynamic setup
3. **Phase 4** (High): Source code migration and policy removal
4. **Phase 3** (Medium): Static configuration cleanup
5. **Phase 5** (Medium): Validation and performance testing

## Timeline Estimate

- **Phase 1**: 2-3 days
- **Phase 2**: 5-7 days  
- **Phase 3**: 2-3 days
- **Phase 4**: 3-4 days
- **Phase 5**: 2-3 days

**Total Estimated Effort**: 14-20 days

## Conclusion

The migration from custom AudienceMatchPolicyProvider to Token Exchange v2 will significantly simplify the codebase while maintaining all functional capabilities. The key challenge is migrating from static test configuration to dynamic setup, which will make tests more maintainable and less brittle. The removal of custom policy code reduces maintenance burden and improves compatibility with future Keycloak versions.
