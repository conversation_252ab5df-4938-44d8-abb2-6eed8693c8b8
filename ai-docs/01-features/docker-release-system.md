# Feature: Production Docker Release System

**Epic**: CI/CD Pipeline Enhancement
**Story ID**: BODHI-2025-008
**Created**: 2025-01-20
**Status**: Implementation Complete

## Story

**As a** DevOps team
**We want** automated production Docker image builds with proper naming conventions and release workflows
**So that** we can distinguish between testcontainer and production images, enable proper release management, and provide production-ready Docker images with comprehensive release automation

## Background

Our Keycloak Bodhi SPI extension currently has a testcontainer build system for integration testing. We need a separate production release system that:

1. **Distinguishes image types**: Clear naming convention to separate testcontainer vs production images
2. **Release automation**: Automated builds triggered by release tags with proper GitHub releases
3. **Production optimization**: Production-specific configurations (PostgreSQL, clustering, metrics)
4. **Version management**: Semantic versioning with proper tagging strategies

## Requirements

### Functional Requirements

1. **Docker Image Naming Convention**
   - **Production images**: `ghcr.io/bodhisearch/bodhi-auth-server` - Production-ready Keycloak extension
   - **Testcontainer images**: `ghcr.io/bodhisearch/bodhi-auth-testcontainer` - Simplified builds for testing
   - Clear distinction between image purposes and configurations

2. **Production Release Workflow**
   - Triggered by `release/v*` tags and branches
   - Manual workflow dispatch support for specific release tags
   - Uses production `Dockerfile` with PostgreSQL, clustering, and metrics
   - Creates GitHub releases with comprehensive release notes
   - Follows semantic versioning (vX.Y.Z format)

3. **Testcontainer Workflow Updates**
   - Support for `release/testcontainer-v*` tags for testcontainer-specific releases
   - Maintains existing `main` branch triggering
   - Legacy support for `release/v*` pattern
   - Uses testcontainer `Dockerfile.testcontainer` with in-memory database

4. **Release Management Integration**
   - Makefile targets for both production and testcontainer builds
   - Environment variable validation and error handling
   - Security scanning integration
   - Build information and summary generation

### Non-Functional Requirements

1. **Security**
   - Automated security scanning for both image types
   - Production-specific security configurations
   - GitHub Container Registry authentication
   - Proper permissions and access control

2. **Performance**
   - Production-optimized JVM settings and clustering support
   - Testcontainer-optimized for fast startup and testing
   - Multi-stage Docker builds for minimal image sizes
   - Build time optimization with proper caching

3. **Reliability**
   - Comprehensive error handling and validation
   - Build status reporting and summaries
   - Automated release creation and documentation
   - Rollback capabilities through tagged images

## Acceptance Criteria

### AC1: Docker Image Naming Convention
- [x] Production images use `keycloak-bodhi-ext` naming for clear identification
- [x] Testcontainer images use `bodhi-auth-server` naming for testing context
- [x] Both image types pushed to GitHub Container Registry (ghcr.io)
- [x] Proper image tagging with version and SHA tags
- [x] Clear documentation of naming convention and purpose

### AC2: Production Release Workflow
- [x] GitHub Actions workflow `.github/workflows/release.yml` created
- [x] Triggers on `release/v*` tags and branches with documentation path exclusions
- [x] Manual workflow dispatch with release tag input validation
- [x] Version extraction from `release/vX.Y.Z` format with error handling
- [x] Uses production `Dockerfile` with PostgreSQL and clustering features
- [x] Creates comprehensive GitHub releases with Docker image information

### AC3: Makefile Integration
- [x] `ci.build-release` target for production Docker image builds
- [x] `ci.push-release` target for production image publishing
- [x] `ci.release-info` target for production build information
- [x] Environment variable validation and error handling
- [x] Consistent patterns with existing testcontainer targets
- [x] Proper logging and status reporting

### AC4: Testcontainer Workflow Updates
- [x] Updated `.github/workflows/testcontainer.yml` to support new tagging
- [x] Support for `release/testcontainer-v*` tags for testcontainer releases
- [x] Clean version tagging (uses `1.0.0` instead of `testcontainer-v1.0.0`) following Docker conventions
- [x] Updated tagging logic and build summary documentation
- [x] Maintains existing `main` branch triggering functionality

### AC5: Release Automation Features
- [x] Automated GitHub release creation with comprehensive release notes
- [x] Docker image information included in release documentation
- [x] Build summaries with image naming convention explanation
- [x] Security scanning integration with continue-on-error handling
- [x] Production features documentation (PostgreSQL, clustering, metrics)

### AC6: Error Handling and Validation
- [x] Release tag format validation with clear error messages
- [x] Environment variable validation in Makefile targets
- [x] Docker build and push error handling
- [x] Security scan failure handling (non-blocking)
- [x] Comprehensive logging throughout build process

## Technical Implementation

### Architecture

```
Release Tags (release/v*) → GitHub Actions → Makefile CI Targets → Production Docker Build → GitHub Container Registry + GitHub Releases
Testcontainer Tags (release/testcontainer-v*) → GitHub Actions → Makefile CI Targets → Testcontainer Docker Build → GitHub Container Registry
```

### Key Components

1. **Production Release Workflow** (`.github/workflows/release.yml`)
   - Trigger on `release/v*` tags and branches
   - Manual dispatch with tag validation
   - Version extraction and Docker tag generation
   - Production Docker build using `Dockerfile`
   - GitHub release creation with comprehensive documentation
   - Security scanning and build summaries

2. **Updated Testcontainer Workflow** (`.github/workflows/testcontainer.yml`)
   - Additional trigger for `release/testcontainer-v*` tags
   - Updated tagging logic for testcontainer-specific releases
   - Legacy support for existing `release/v*` pattern
   - Enhanced build summary with new tagging strategy

3. **Makefile Production Targets**
   - `ci.build-release`: Production Docker image building
   - `ci.push-release`: Production image publishing
   - `ci.release-info`: Production build information
   - Environment validation and error handling
   - Consistent logging and status reporting

4. **Docker Image Configuration**
   - **Production** (`Dockerfile`): PostgreSQL, clustering, metrics, production JVM settings
   - **Testcontainer** (`Dockerfile.testcontainer`): In-memory database, fast startup, testing optimizations

### Image Naming Strategy

| Image Type | Registry Path | Purpose | Dockerfile | Database |
|------------|---------------|---------|------------|----------|
| **Production** | `ghcr.io/bodhisearch/bodhi-auth-server` | Production deployments | `Dockerfile` | PostgreSQL |
| **Testcontainer** | `ghcr.io/bodhisearch/bodhi-auth-testcontainer` | Integration testing | `Dockerfile.testcontainer` | In-memory H2 |

### Tagging Conventions

| Trigger Pattern | Docker Tag | Image Type | Example |
|-----------------|------------|------------|---------|
| `release/v1.0.0` | `v1.0.0` | Production | `bodhi-auth-server:v1.0.0` |
| `release/testcontainer-v1.0.0` | `1.0.0` | Testcontainer | `bodhi-auth-testcontainer:1.0.0` |
| `main` branch | `main` | Testcontainer | `bodhi-auth-testcontainer:main` |
| Other branches | `{sha}` | Testcontainer | `bodhi-auth-testcontainer:abc1234` |

## Usage Examples

### Creating a Production Release

```bash
# Create and push production release tag
git tag release/v1.0.0
git push origin release/v1.0.0

# This triggers:
# 1. Production Docker build using Dockerfile
# 2. Image push to ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0
# 3. GitHub release creation with comprehensive documentation
```

### Creating a Testcontainer Release

```bash
# Create and push testcontainer release tag
git tag release/testcontainer-v1.0.0
git push origin release/testcontainer-v1.0.0

# This triggers:
# 1. Testcontainer Docker build using Dockerfile.testcontainer
# 2. Image push to ghcr.io/bodhisearch/bodhi-auth-testcontainer:1.0.0
```

### Using Docker Images

```bash
# Production deployment
docker pull ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0

# Integration testing
docker pull ghcr.io/bodhisearch/bodhi-auth-testcontainer:main
```

## Implementation Status

### ✅ Completed Components
- [x] Makefile production CI targets (`ci.build-release`, `ci.push-release`, `ci.release-info`)
- [x] Production release workflow (`.github/workflows/release.yml`)
- [x] Updated testcontainer workflow with new tagging support
- [x] Docker image naming convention implementation
- [x] GitHub release automation with comprehensive documentation
- [x] Security scanning integration
- [x] Error handling and validation throughout build process
- [x] Build summaries and documentation

### 🔄 Integration Points
- Makefile CI targets integrate with existing patterns
- GitHub Actions workflows follow established conventions
- Docker builds use existing Dockerfile configurations
- Release automation follows semantic versioning standards

## Risk Assessment

### High Risk
- **Release Tag Conflicts**: Incorrect tagging could trigger wrong workflows
  - *Mitigation*: Clear tagging conventions and validation in workflows
  - *Detection*: Automated tag format validation with error messages

### Medium Risk
- **Docker Registry Permissions**: GitHub Container Registry access issues
  - *Mitigation*: Proper GITHUB_TOKEN permissions and authentication
  - *Detection*: Build failure notifications and error logging

### Low Risk
- **Build Time Increase**: Additional workflow complexity
  - *Mitigation*: Parallel builds and proper caching strategies
  - *Detection*: Build time monitoring and optimization

## Dependencies

- GitHub Actions with Docker support
- GitHub Container Registry access
- Existing Dockerfile configurations
- Makefile CI infrastructure

## Success Metrics

- Build success rate: >95% for both image types
- Release automation: 100% automated GitHub release creation
- Image availability: <5 minutes from tag push to registry availability
- Documentation completeness: All releases include comprehensive information
- Tag validation: 100% validation of release tag formats

## Release Automation Features

### GHCR-First Version Management

The project uses **GitHub Container Registry (GHCR) as the single source of truth** for version management:

```bash
# Check current versions from GHCR
make check-latest-versions

# Create next production release (reads from GHCR)
make release-server

# Create next testcontainer release (reads from GHCR)
make release-testcontainer
```

**GHCR-First Logic**:
1. **Query GHCR API**: Check for existing published packages
2. **Handle "Package not found"**: If no package exists, start with version 0.0.1
3. **Parse existing versions**: Extract semantic versions from published tags
4. **Increment patch version**: Automatically increment to next patch version (X.Y.Z → X.Y.Z+1)
5. **Create release tag**: Generate and push appropriate release tag

**Version Detection Flow**:
```bash
# Production releases
gh api "/orgs/{org}/packages/container/bodhi-auth-server/versions"
# → If 404 "Package not found": start with v0.0.1
# → If found: parse latest vX.Y.Z tag and increment

# Testcontainer releases  
gh api "/orgs/{org}/packages/container/bodhi-auth-testcontainer/versions"
# → If 404 "Package not found": start with 0.0.1
# → If found: parse latest X.Y.Z tag and increment
```

**Benefits**:
- **Single Source of Truth**: GHCR registry contains the authoritative version history
- **No Git Tag Dependencies**: Versions are determined by actual published packages
- **Consistent Across Environments**: Same logic works regardless of local git state
- **Handles Missing Packages**: Gracefully starts versioning when no packages exist

**Safety Features**:
- Branch status verification against remote
- Existing tag detection with user confirmation
- Interactive prompts for safety checks
- Comprehensive error handling for API failures

## Multi-Platform Build Support

### Architecture Support

Both production and testcontainer images now support multi-platform builds:

- **linux/amd64**: Intel/AMD 64-bit architecture (traditional x86_64)
- **linux/arm64**: ARM 64-bit architecture (Apple Silicon, AWS Graviton, etc.)

### Platform Selection

Docker automatically selects the appropriate image variant based on the host platform:

```bash
# On Intel Mac or x86_64 Linux - pulls amd64 variant
docker pull ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0

# On Apple Silicon Mac or ARM64 Linux - pulls arm64 variant  
docker pull ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0

# Force specific platform if needed
docker pull --platform linux/amd64 ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0
docker pull --platform linux/arm64 ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0
```

### Build Process

Multi-platform builds use Docker Buildx with the following process:

1. **Build Stage**: Uses `--platform=$BUILDPLATFORM` for build tools (faster cross-compilation)
2. **Runtime Stage**: Creates separate images for each target platform
3. **Manifest Creation**: Docker creates a multi-platform manifest pointing to platform-specific images
4. **Registry Push**: All platform variants are pushed simultaneously using `--push` flag

### Technical Implementation

```makefile
# Multi-platform build with automatic push
docker buildx build \
    -f Dockerfile \
    --platform linux/amd64,linux/arm64 \
    --build-arg BUILD_DATE="$$BUILD_DATE" \
    -t "$$DOCKER_REGISTRY/$$IMAGE_NAME:$$DOCKER_TAG" \
    --push \
    .
```

### Benefits

- **Apple Silicon Support**: Native ARM64 performance on M1/M2 Macs
- **Cloud ARM Instances**: Cost-effective ARM-based cloud deployments (AWS Graviton, etc.)
- **Performance**: No emulation overhead - native execution on both architectures
- **Compatibility**: Single image reference works across all supported platforms
- **Error Resolution**: Eliminates platform mismatch warnings when running on ARM64 hosts

### Error Resolution

Multi-platform builds resolve common Docker platform issues:

**Before (Single Platform)**:
```
WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8)
```

**After (Multi-Platform)**:
- Docker automatically selects the correct platform variant
- No warnings or emulation overhead
- Native performance on both AMD64 and ARM64 architectures

## Future Enhancements

1. **Advanced Release Notes**: Automated changelog generation from commits
2. **Deployment Integration**: Automated deployment triggers for production releases
3. **Image Scanning**: Enhanced security scanning with vulnerability reporting
4. **Release Metrics**: Build time tracking and optimization analytics
5. **Major/Minor Versioning**: Support for major and minor version bumps beyond patch releases
6. **Additional Platforms**: Support for more architectures (linux/arm/v7, etc.) as needed

---

**Note**: This implementation provides a comprehensive Docker release system that clearly separates production and testing concerns while maintaining automation and reliability standards for enterprise-grade deployments. 