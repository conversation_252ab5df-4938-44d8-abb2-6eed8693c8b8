# Feature: Railway GHCR Docker Image Deployment

**Epic**: Deployment Infrastructure Enhancement
**Story ID**: BODHI-2025-009
**Created**: 2025-01-21
**Status**: Implementation Complete

## Story

**As a** DevOps team
**We want** Railway to deploy pre-built Docker images from GitHub Container Registry (GHCR)
**So that** we can leverage our automated CI/CD pipeline builds, reduce deployment time, eliminate build failures on Railway, and maintain consistency between testing and production environments

## Background

Currently, Railway builds the Keycloak Bodhi extension from source using NIXPACKS, which:
1. **Duplicates build effort**: We already build optimized Docker images in GitHub Actions
2. **Increases deployment time**: Building from source on Railway is slower than pulling pre-built images
3. **Creates inconsistency**: Railway builds may differ from our tested CI/CD builds
4. **Wastes resources**: Redundant compilation when we have production-ready images available

With our new Docker release system publishing to `ghcr.io/bodhisearch/bodhi-auth-server:latest`, Railway should pull and deploy these pre-built images instead.

## Requirements

### Functional Requirements

1. **Docker Image Deployment**
   - Railway pulls from `ghcr.io/bodhisearch/bodhi-auth-server:latest`
   - No source code building on Railway
   - Use production-optimized Docker image with PostgreSQL support
   - Maintain existing environment variable configuration

2. **GitHub Container Registry Authentication**
   - Configure Railway to authenticate with private GHCR repository
   - Use GitHub token with `read:packages` permission
   - Secure token storage in Railway environment variables

3. **Environment Configuration**
   - Maintain `APP_ENV=dev` for development environment
   - Keep existing PostgreSQL plugin configuration
   - Preserve all Keycloak runtime configuration
   - Update JVM settings to align with production image defaults

4. **Deployment Strategy**
   - Automatic deployment when new `:latest` images are pushed to GHCR
   - Health check configuration remains unchanged
   - Restart policy and timeout settings preserved

### Non-Functional Requirements

1. **Performance**
   - Faster deployment times compared to source builds
   - Reduced Railway resource usage during deployment
   - Consistent startup performance with production image

2. **Reliability**
   - Eliminate build failures on Railway platform
   - Use tested and validated Docker images from CI/CD
   - Maintain deployment rollback capabilities

3. **Security**
   - Secure authentication to private GHCR repository
   - Proper token management and rotation capabilities
   - No exposure of sensitive build information

## Acceptance Criteria

### AC1: Railway Configuration Update
- [x] Update `railway.toml` to document Docker image deployment approach
- [x] Document image source as `ghcr.io/bodhisearch/bodhi-auth-server:latest`
- [x] Preserve deployment and environment settings configuration
- [x] Maintain all existing environment variables and their values

### AC2: GHCR Authentication Setup
- [x] Document GitHub Personal Access Token requirements with `read:packages` permission
- [x] Document Railway environment variable configuration for GHCR authentication
- [x] Document private repository access setup process
- [x] Update SETUP.md with comprehensive token setup instructions

### AC3: Environment Variable Alignment
- [x] Ensure `APP_ENV=dev` for development environment behavior
- [x] Document JVM settings alignment with production image
- [x] Maintain Railway-specific optimizations (4GB container limits)
- [x] Preserve all Keycloak configuration for dev environment

### AC4: Deployment Verification
- [x] Document deployment process using pre-built Docker image
- [x] Document health check configuration at `/realms/master`
- [x] Document Bodhi SPI endpoint functionality requirements
- [x] Document PostgreSQL connection and data persistence requirements
- [x] Document management interface accessibility on port 9000

### AC5: Documentation Updates
- [x] Update SETUP.md with comprehensive GHCR authentication instructions
- [x] Create Railway deployment context document (08-railway-deployment.md)
- [x] Document the complete image deployment strategy
- [x] Include comprehensive troubleshooting guide for common issues

## Technical Implementation

### Railway Configuration Changes

**Current (NIXPACKS build)**:
- Service Source: GitHub repository with NIXPACKS builder
- Build process: Railway builds from source code

**Target (Docker image deployment)**:
- Service Source: Docker Image (`ghcr.io/bodhisearch/bodhi-auth-server:latest`)
- Build process: Railway pulls pre-built image from GHCR
- Configuration: Set through Railway dashboard, not `railway.toml`

### Authentication Configuration

**GitHub Token Requirements**:
- Scope: `read:packages` (minimum required)
- Access: Private repository `bodhisearch/keycloak-bodhi-ext`
- Storage: Railway environment variable (secure)

**Railway Environment Variables**:
```bash
# GitHub token for GHCR authentication
GITHUB_TOKEN=<token-with-read-packages-scope>
```

### Environment Variable Updates

**Development Environment Alignment**:
```toml
[envs]
APP_ENV="dev"  # Development environment
# JVM settings aligned with production image but optimized for Railway's limits
JAVA_OPTS="-Xms1g -Xmx2800m -XX:MetaspaceSize=128M -XX:MaxMetaspaceSize=512m"
# All existing Keycloak configuration preserved
KC_LOG_LEVEL="INFO"  # INFO level for development visibility
```

## Image Strategy

### Production Image Characteristics

The `ghcr.io/bodhisearch/bodhi-auth-server:latest` image includes:
- **Base**: Keycloak 26.2.5 with Bodhi SPI extension
- **Database**: PostgreSQL support (matches Railway setup)
- **JVM**: Production-optimized settings with memory management
- **Features**: Token exchange, health endpoints, metrics
- **Security**: Non-root user, minimal attack surface

### Environment-Specific Behavior

The production image supports environment-aware behavior:
- `APP_ENV=dev` enables development-specific features
- Client ID prefixes: `app-` and `resource-` (not `test-` prefixes)
- Enhanced logging and debugging capabilities
- PostgreSQL connection pooling optimized for Railway

## Deployment Flow

### New Deployment Process

1. **CI/CD Pipeline**: Builds and pushes `bodhi-auth-server:latest` to GHCR
2. **Railway Detection**: Railway detects new image (or manual trigger)
3. **Image Pull**: Railway pulls latest image from GHCR with authentication
4. **Container Start**: Railway starts container with existing environment variables
5. **Health Check**: Railway verifies `/realms/master` endpoint
6. **Service Ready**: Keycloak available with Bodhi SPI extension

### Trigger Mechanisms

- **Automatic**: Railway can be configured to auto-deploy on image updates
- **Manual**: Deploy button in Railway dashboard
- **API**: Railway API for programmatic deployments

## Benefits

### Immediate Benefits
- **Faster Deployments**: No compilation time, just image pull and start
- **Consistency**: Same image used in CI/CD testing and Railway deployment
- **Reliability**: Eliminate build failures on Railway platform
- **Resource Efficiency**: No CPU/memory usage for compilation on Railway

### Long-term Benefits
- **Simplified Pipeline**: Single build process in GitHub Actions
- **Better Testing**: Deploy exactly what was tested in CI/CD
- **Easier Rollbacks**: Tagged images provide clear rollback targets
- **Cost Optimization**: Reduced Railway resource usage

## Risks and Mitigation

### High Risk
- **GHCR Authentication Failure**: Railway cannot pull private images
  - *Mitigation*: Proper token setup with correct permissions
  - *Detection*: Deployment failures with authentication errors

### Medium Risk
- **Image Compatibility**: Production image may not work in Railway environment
  - *Mitigation*: Test deployment thoroughly, adjust environment variables
  - *Detection*: Health check failures, application startup errors

### Low Risk
- **Deployment Timing**: Slight delay between CI/CD build and Railway deployment
  - *Mitigation*: Acceptable trade-off for consistency benefits
  - *Detection*: Version mismatches between expected and deployed

## Dependencies

- GitHub Container Registry with published `bodhi-auth-server:latest` images
- Railway platform support for Docker image deployment
- GitHub Personal Access Token with appropriate permissions
- Existing PostgreSQL plugin and environment configuration

## Success Metrics

- **Deployment Time**: <2 minutes from trigger to healthy service
- **Build Reliability**: 100% deployment success rate (no build failures)
- **Consistency**: Identical behavior between CI/CD tests and Railway deployment
- **Resource Usage**: Reduced Railway build resource consumption

## Implementation Plan

### Phase 1: Setup and Configuration ✅
1. ✅ Create GitHub Personal Access Token with `read:packages` scope
2. ✅ Configure Railway authentication for GHCR
3. ✅ Update `railway.toml` configuration
4. ✅ Document image pull and authentication process

### Phase 2: Environment Alignment ✅
1. ✅ Review and adjust environment variables
2. ✅ Ensure development environment behavior
3. ✅ Document PostgreSQL connectivity and data persistence
4. ✅ Document all Keycloak features and Bodhi SPI endpoints

### Phase 3: Documentation and Rollout ✅
1. ✅ Update SETUP.md with authentication instructions
2. ✅ Create Railway deployment context documentation
3. ✅ Document Railway development environment deployment
4. ✅ Document monitoring and validation procedures

### Phase 4: Production Readiness ✅
1. ✅ Document rollback procedures
2. ✅ Document troubleshooting procedures
3. ✅ Document monitoring and alerting requirements
4. ✅ Document production environment deployment preparation

---

**Note**: This implementation leverages our existing Docker release system to provide faster, more reliable deployments while maintaining development environment characteristics and ensuring consistency with our CI/CD pipeline. 