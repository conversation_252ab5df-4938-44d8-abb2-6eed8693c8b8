# Token Exchange V2 Support

## Feature Overview

This feature specification documents the migration from custom AudienceMatchPolicy-based token exchange to Keycloak's standard token exchange v2 implementation (RFC 8693 compliant). This represents a significant architectural shift from preview features to production-ready standard OAuth2 token exchange.

## Background & Motivation

### Previous Implementation (v1)
- **Custom Policy**: Used `AudienceMatchPolicyProvider` for audience validation
- **Preview Features**: Relied on Keycloak's admin fine-grained authorization preview features
- **Complex Setup**: Required custom SPI implementation and complex policy configuration
- **Maintenance Burden**: Custom code required ongoing maintenance and updates

### New Implementation (v2)
- **Standard Token Exchange**: Uses Keycloak's built-in RFC 8693 compliant token exchange
- **Simplified Configuration**: Uses standard `standard.token.exchange.enabled` attribute
- **Scope-Based Audience**: Dynamic audience resolution through client scopes
- **Production Ready**: No preview features, fully supported by Keycloak

## Technical Implementation

### Core Changes

#### 1. Removed Custom Policy Components
**Deleted Files:**
- `src/main/java/com/bodhisearch/AudienceMatchPolicyProvider.java`
- `src/main/java/com/bodhisearch/AudienceMatchPolicyProviderFactory.java`
- `src/main/java/com/bodhisearch/AudienceMatchRepresentation.java`
- `src/test/java/com/bodhisearch/AudienceMatchPolicyProviderTest.java`
- `src/main/resources/META-INF/services/org.keycloak.authorization.policy.provider.PolicyProviderFactory`

**Rationale**: These custom components are no longer needed as Keycloak v2 provides standard token exchange capabilities.

#### 2. Updated Client Configuration
**Before (v1):**
```java
// Complex policy setup with admin permissions
AdminPermissionManagement permissions = AdminPermissions.management(session, realm);
ClientPermissionManagement clientMgmt = permissions.clients();
clientMgmt.setPermissionsEnabled(client, true);
// ... complex policy creation and association
```

**After (v2):**
```java
// Simple standard token exchange enablement
client.setAttribute("standard.token.exchange.enabled", "true");
```

#### 3. Scope-Based Audience Resolution
**New Approach:**
- Each resource client automatically gets a corresponding client scope (`scope_{client-id}`)
- App clients request specific scopes to access resources
- Audience is dynamically resolved based on requested scopes
- No static audience mappers needed

**Implementation:**
```java
private ClientScopeModel createResourceScope(RealmModel realm, ClientModel resourceClient) {
  String scopeName = "scope_" + resourceClient.getClientId();
  ClientScopeModel resourceScope = realm.addClientScope(scopeName);
  
  // Add audience mapper to include resource client in audience
  ProtocolMapperModel audienceMapper = new ProtocolMapperModel();
  audienceMapper.setProtocolMapper("oidc-audience-mapper");
  config.put("included.client.audience", resourceClient.getClientId());
  // ...
}
```

#### 4. New Request-Access Endpoint
**New Feature:** `POST /resources/request-access`
- Allows app clients to request access to specific resource servers
- Adds resource scope as optional scope to app client
- Enables dynamic consent-based access control

### API Changes

#### Updated Endpoints
- **Changed:** `/clients` → `/resources` (for resource server creation)
- **New:** `/resources/request-access` (for app-to-resource access requests)
- **Removed:** Custom audience management endpoints

#### Request/Response Models
**Unified ClientRequest:**
```java
public static class ClientRequest {
  @JsonProperty("name")
  public String name;
  @JsonProperty("description") 
  public String description;
  @JsonProperty("redirect_uris")
  public List<String> redirectUris;
}
```

**Enhanced Response:**
```java
public static class ResourceClientResponse {
  @JsonProperty("client_id")
  public final String clientId;
  @JsonProperty("client_secret")
  public final String clientSecret;
  @JsonProperty("scope")
  public final String scope; // NEW: Include scope name
}
```

## Token Exchange Flow

### Standard OAuth2 Token Exchange (RFC 8693)

#### 1. App Client Authentication
```http
GET /auth?client_id=app-123&scope=openid scope_resource-456
```

#### 2. Token Exchange Request
```http
POST /token
Content-Type: application/x-www-form-urlencoded

grant_type=urn:ietf:params:oauth:grant-type:token-exchange
&subject_token={app_user_token}
&audience=resource-456
&client_id=resource-456
&client_secret={resource_secret}
```

#### 3. Response
```json
{
  "access_token": "eyJ...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "scope": "scope_resource-456",
  "audience": "resource-456"
}
```

## Migration Strategy

### Phase 1: Remove Preview Features
- ✅ Remove admin fine-grained authorization dependencies
- ✅ Delete custom policy provider implementations
- ✅ Update Docker configuration to remove preview feature flags

### Phase 2: Implement Standard Token Exchange
- ✅ Enable standard token exchange on resource clients
- ✅ Implement scope-based audience resolution
- ✅ Create automatic scope generation during client creation

### Phase 3: Update APIs and Documentation
- ✅ Rename endpoints for clarity (`/clients` → `/resources`)
- ✅ Add new request-access functionality
- ✅ Update test suites for new flow

## Testing Strategy

### Integration Tests
- **Token Exchange Validation**: Verify proper audience inclusion in exchanged tokens
- **Scope Management**: Test automatic scope creation and assignment
- **Error Handling**: Validate proper error responses for invalid requests

### HTTPyac Scripts
- **Dynamic Audience Demo**: `token-exchange-v2-aud.http`
- **Complete Flow Testing**: End-to-end OAuth2 flows with token exchange
- **Error Scenario Testing**: Invalid client configurations and requests

## Performance Benefits

### Scalability Improvements
- **Reduced Database Queries**: No complex policy lookups
- **Simplified Configuration**: Standard Keycloak mechanisms
- **Better Caching**: Leverages Keycloak's built-in caching

### Maintenance Benefits
- **No Custom Code**: Reduced maintenance burden
- **Standard Compliance**: RFC 8693 compliant implementation
- **Future-Proof**: Uses stable Keycloak features

## Security Considerations

### Enhanced Security
- **Standard Validation**: Uses Keycloak's proven token exchange validation
- **Scope-Based Access**: Granular consent-based access control
- **Audit Trail**: Standard Keycloak audit logging

### Compliance
- **RFC 8693**: Full compliance with OAuth 2.0 Token Exchange specification
- **OIDC Standards**: Proper OpenID Connect scope handling
- **Security Best Practices**: Follows OAuth 2.0 Security Best Current Practice

## Configuration Changes

### Docker Compose Updates
**Removed:**
```yaml
- --features=preview,admin-fine-grained-authz:v1
```

**Added:**
```yaml
# Standard token exchange is enabled by default in Keycloak 26.2+
# No additional feature flags needed
```

### Realm Configuration
**Automatic Setup:**
- Client scopes created automatically during resource client creation
- Audience mappers configured automatically
- No manual policy configuration required

## Future Enhancements

### Potential Improvements
1. **Batch Scope Management**: Support for bulk scope operations
2. **Advanced Consent UI**: Enhanced consent screens with resource descriptions
3. **Monitoring & Analytics**: Token exchange usage metrics
4. **Custom Scope Validation**: Additional validation rules for specific use cases

### Backwards Compatibility
- **API Versioning**: Consider versioned endpoints for future changes
- **Migration Tools**: Automated migration scripts for existing deployments
- **Documentation**: Comprehensive migration guides

## Conclusion

The migration to token exchange v2 represents a significant improvement in:
- **Simplicity**: Reduced complexity through standard implementations
- **Reliability**: Production-ready features instead of preview functionality
- **Performance**: Better scalability and reduced maintenance overhead
- **Compliance**: Full RFC 8693 compliance with standard OAuth2 flows

This change positions the system for long-term maintainability while providing enhanced security and performance characteristics. 