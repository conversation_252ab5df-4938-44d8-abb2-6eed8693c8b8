# Dynamic User Setup Migration Analysis

## Overview

This document analyzes all test files in the project to identify which tests need to be refactored to create their own dynamic user setup instead of relying on static users from the Keycloak Config CLI realm configuration.

## Current Static User Dependencies

### Static Users Defined in RealmConfigGenerator.java
Currently, the following static users are created via Keycloak Config CLI:

| Username | First Name | Last Name | Group Assignment |
|----------|------------|-----------|------------------|
| `<EMAIL>` | New | User | (none) |
| `<EMAIL>` | Test | User | `/users-resource-abcd/users` |
| `<EMAIL>` | Power | User | `/users-resource-abcd/power-users` |
| `<EMAIL>` | User | Manager | `/users-resource-abcd/managers` |
| `<EMAIL>` | User | Admin | `/users-resource-abcd/admins` |
| `<EMAIL>` | User | Other | (none) |
| `<EMAIL>` | Other | User | `/users-resource-wxyz/users` |
| `<EMAIL>` | Other | Manager | `/users-resource-wxyz/managers` |
| `<EMAIL>` | User | Admin | `/users-resource-wxyz/admins` |
| `<EMAIL>` | Some | User | (none) |
| `<EMAIL>` | Some | User | (none) |
| `<EMAIL>` | Test | User | (none) |
| `<EMAIL>` | Test | User | (none) |

### Static Client Dependencies
Currently using static clients:
- `RESOURCE_ABCD` (`resource-abcd`)
- `RESOURCE_WXYZ` (`resource-wxyz`) 
- `CLIENT_LMNO` (`client-lmno`)
- `RESOURCE_EMPTY` (`resource-empty`)
- `resource-make-first-admin`
- `resource-missing-group`
- `resource-missing-token-exchange-permission`

## Test Analysis and Migration Status

### ✅ Already Using Dynamic Setup

#### 1. ManageResourceTest.java
- **Status**: ✅ **Already Dynamic**
- **Current Approach**: Creates dynamic clients via `registerClient()`
- **Users**: Uses only service account tokens, no user credentials
- **Migration Required**: None

#### 2. AppFlowTest.java
- **Status**: ✅ **Complete - Fully Dynamic**
- **Current Approach**: 
  - ✅ Creates dynamic clients via `registerClientAndReturnClientPair()`
  - ✅ Creates dynamic users via `createUser()` with proper setup/teardown
- **Migration Required**: ✅ **COMPLETED**

#### 3. RealmSetupTest.java
- **Status**: ✅ **Complete - Fully Dynamic**
- **Current Approach**:
  - ✅ Creates dynamic clients via `registerClient()`
  - ✅ Creates dynamic users via `createUser()` with proper setup/teardown
- **Migration Required**: ✅ **COMPLETED**

#### 4. RegisterResourceTest.java
- **Status**: ✅ **Complete - Fully Dynamic**
- **Current Approach**:
  - ✅ Creates dynamic clients via `registerClient()`
  - ✅ Creates dynamic public clients via `createPublicClientForUser()` for user authentication
  - ✅ Creates dynamic users via `createUser()` with proper setup/teardown
- **Migration Required**: ✅ **COMPLETED**

### ✅ Already Using Dynamic Setup

#### 5. BodhiResourceProviderTest.java
- **Status**: ✅ **Complete - Fully Dynamic**
- **Current Approach**:
  - ✅ Creates dynamic clients via `registerClientAndReturnClientPair()`
  - ✅ Creates dynamic users via `createUser()` with proper setup/teardown
  - ✅ Creates specialized clients via `createClientWithoutGroups()` for testing edge cases
  - ✅ All tests pass with dynamic setup
- **Migration Required**: ✅ **COMPLETED**

### ❌ Heavy Static Dependencies (Next Priority)

#### 6. KeycloakScopeTest.java
- **Status**: ✅ **Complete - Fully Dynamic**
- **Current Approach**:
  - ✅ Creates dynamic client via `registerClientAndReturnClientPair()`
  - ✅ Creates dynamic users via `createUserWithRoles()` with appropriate role assignments
  - ✅ Maintains parameterized test structure with 9 different user scenarios
  - ✅ Proper setup/teardown with dynamic user cleanup
- **Migration Required**: ✅ **COMPLETED**

#### 7. AudienceMatchPolicyProviderTest.java
- **Status**: ❌ **Fully Static**
- **Static Dependencies**:
  - Static clients: `CLIENT_LMNO`, `RESOURCE_ABCD`, `resource-missing-token-exchange-permission`
  - Static users: `<EMAIL>` used in multiple tests
- **Migration Required**: High - Complete rewrite needed
- **Complexity**: Medium - Token exchange testing with specific client configurations

#### 8. BaseTest.java Helper Methods
- **Status**: ❌ **Static Utility Methods**
- **Static Dependencies**:
  - Helper methods that reference static users: `getOtherUserToken()`, `getOtherAdminToken()`, `getResourceAdminUserToken()`
  - Static client references: `RESOURCE_ABCD`, `RESOURCE_WXYZ`, etc.
- **Migration Required**: Medium - Update or remove static helper methods

### 📋 Special Cases

#### 9. CliDebugTest.java
- **Status**: 🔍 **Debug Only**
- **Dependencies**: Uses separate debug realm configuration
- **Migration Required**: None (Debug test, can be excluded)

## Migration Priority Matrix

### Priority 1 (High Impact, Current Focus)
1. **AudienceMatchPolicyProviderTest.java** - Token exchange testing (Only remaining test)

### Priority 2 (Cleanup) - **COMPLETED**
✅ **BaseTest.java** - Static dependencies removed, dynamic utilities implemented

### Priority 3 (Low Impact, Low Effort) - **COMPLETED**
✅ **RegisterResourceTest.java** - **COMPLETED**
✅ **AppFlowTest.java** - **COMPLETED**
✅ **RealmSetupTest.java** - **COMPLETED**
✅ **BodhiResourceProviderTest.java** - **COMPLETED**
✅ **KeycloakScopeTest.java** - **COMPLETED**

## Current Progress Summary

### Completed Migrations: 7/8 tests (87.5%)
- ✅ ManageResourceTest.java (Already dynamic)
- ✅ AppFlowTest.java (Fully migrated)
- ✅ RealmSetupTest.java (Fully migrated)
- ✅ RegisterResourceTest.java (Fully migrated)
- ✅ BodhiResourceProviderTest.java (Fully migrated)
- ✅ KeycloakScopeTest.java (Fully migrated)
- ✅ CliDebugTest.java (Debug only - excluded from migration)

### In Progress: 0/8 tests (0%)
- None

### Not Started: 1/8 tests (12.5%)
- ❌ AudienceMatchPolicyProviderTest.java (Medium complexity)

### Overall Progress: 87.5% Complete

## Immediate Next Steps

### 1. AudienceMatchPolicyProviderTest.java Migration (Only Remaining Task)
**Migration Strategy:**
- Replace static `CLIENT_LMNO` and `RESOURCE_ABCD` with dynamic clients
- Create dynamic users on-demand for token exchange scenarios
- Maintain token exchange permission testing logic
- Handle edge cases like `resource-missing-token-exchange-permission`

### 2. Final Cleanup (After Migration Complete)
**Cleanup Strategy:**
- Remove static realm configuration files (`bodhi-realm-setup.ftl`, `bodhi-realm-generated.json`)
- Remove template classes (`RealmConfigGenerator.java`, `User.java`, `Group.java`)
- Remove any remaining static constants from BaseTest if not needed

## Required Utility Methods for Dynamic Setup

To support remaining migrations, these helper methods are needed in `BaseTest.java`:

```java
// User management utilities
protected void addUserToClientGroup(String userId, String clientId, String groupName);
protected void assignRolesToUser(String userId, String clientId, List<String> roleNames);

// Specialized client creation
protected ClientPair createClientWithTokenExchangePermission(String targetClient);
protected ClientPair createClientWithoutTokenExchangePermission();

// Test data generators for complex scenarios
protected TestUser createUserWithRoles(String clientId, List<String> roleNames);
protected TestUser createUserInGroups(String clientId, List<String> groupNames);
```

## Migration Strategy per Remaining Test File

### BodhiResourceProviderTest.java - Fix Remaining Issues
**Current**: 2 failing tests out of 14 total tests.

**Required Fixes**:
1. **testAddUserToGroupAlreadyMember**: Pre-add user to "users" group in setup
2. **testAddUserToGroupByOtherClientAdmin**: Fix authorization validation logic

### KeycloakScopeTest.java Migration Plan
**Current**: Tests user roles with 9 different static users across different role levels.

**Target**: 
- Create dynamic client for each test run
- Generate users dynamically with specific role assignments
- Convert static userScopeProvider to dynamic data generation
- Maintain parameterized test structure with dynamic users

### AudienceMatchPolicyProviderTest.java Migration Plan
**Current**: Tests token exchange with static client and user setup.

**Target**:
- Create dynamic clients for both source and target in token exchange
- Create test users on-demand for token exchange scenarios
- Remove dependency on static `CLIENT_LMNO` configuration
- Maintain token exchange permission testing

## Files to be Modified/Removed

### Files to Remove After Migration
- `src/test/resources/import-files/bodhi-realm-setup.ftl`
- `src/test/resources/import-files/bodhi-realm-generated.json`
- `src/test/java/com/bodhisearch/templates/RealmConfigGenerator.java`
- `src/test/java/com/bodhisearch/templates/User.java`
- `src/test/java/com/bodhisearch/templates/Group.java`

### Files to Modify
- `src/test/java/com/bodhisearch/BaseTest.java` - Remove static imports, add dynamic utilities
- `src/test/java/com/bodhisearch/BodhiResourceProviderTest.java` - Fix remaining 2 tests
- `src/test/java/com/bodhisearch/KeycloakScopeTest.java` - Complete migration to dynamic setup
- `src/test/java/com/bodhisearch/AudienceMatchPolicyProviderTest.java` - Complete migration to dynamic setup

### BaseTest.java Changes Required
1. Remove static user helper methods (`getResourceAdminUserToken()`, `getOtherUserToken()`, `getOtherAdminToken()`)
2. Remove static client constants (keep during transition period)
3. Add dynamic user creation utilities with role/group assignment capabilities
4. Update container setup to work with fully dynamic approach

## Success Criteria

### Functional Requirements
- All existing test functionality must be preserved
- Tests must be isolated (no shared state between tests)
- Test execution time should not significantly increase
- All tests must pass consistently

### Technical Requirements
- No static user dependencies in any test file
- All user/client creation must be dynamic
- Proper cleanup in teardown methods
- Clear separation of test data creation from test logic

### Final Goal
- Zero dependency on Keycloak Config CLI imports
- All tests create their own isolated test environment
- Easy to understand and maintain test setup
- Consistent patterns across all test files

## Time Estimates
- **AudienceMatchPolicyProviderTest.java migration**: 1.5 days
- **Final cleanup**: 0.5 days

**Total Remaining**: 2 days
**Total Project**: 8.5 days (87.5% complete)
