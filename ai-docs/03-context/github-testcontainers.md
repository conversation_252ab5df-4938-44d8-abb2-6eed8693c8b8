# GitHub Actions Testcontainer Workflow

This document describes the current implementation of the GitHub Actions workflow for building Docker images for integration testing with testcontainers.

## Workflow Overview

The testcontainer workflow builds Docker images of the Keycloak Bodhi SPI extension for use in integration testing environments. It implements a dynamic tagging strategy based on branch types and publishes to GitHub Container Registry (ghcr.io).

**Note**: Obfuscation has been removed from the testcontainer build to focus on functionality testing and faster build times. The simplified approach prioritizes quick container startup and reliable testing over code protection.

## Current Implementation

### Trigger Configuration

**Automatic Triggers:**
- Push to `main` branch only
- Excludes documentation changes (*.md, docs/**, ai-docs/**)

**Manual Triggers:**
- `workflow_dispatch` with branch selection
- Uses GitHub's "Use workflow from" functionality
- Supports any branch via string input parameter

### Dynamic Tagging Strategy

The workflow implements branch-based tagging logic:

```bash
# main branch
DOCKER_TAG="main"

# release branches (release/vX.Y.Z)
if [[ "$BRANCH_NAME" =~ ^release/v([0-9]+\.[0-9]+\.[0-9]+.*)$ ]]; then
  VERSION_TAG="${BASH_REMATCH[1]}"
  DOCKER_TAG="v$VERSION_TAG"  # e.g., release/v1.0.20250720 → v1.0.20250720
fi

# other branches
DOCKER_TAG="$SHORT_SHA"  # 7-character SHA
```

**Tag Examples:**
- `main` branch → `ghcr.io/bodhisearch/bodhi-auth-server:main`
- `release/v1.0.20250720` → `ghcr.io/bodhisearch/bodhi-auth-server:v1.0.20250720`
- `feature/auth-fix` → `ghcr.io/bodhisearch/bodhi-auth-server:abc1234`

### Build Process

1. **Environment Setup**
   - Checkout specified branch (workflow_dispatch) or current branch (push)
   - Set dynamic branch name and Docker tag
   - Configure GitHub Container Registry authentication

2. **Simplified Build**
   - Run `make ci.build-testcontainer` with dynamic tagging
   - Build standard JAR without obfuscation for faster testing
   - Multi-stage Docker build with optimized layers

3. **Image Publishing**
   - Push primary tag (main/version/SHA) via `make ci.push-testcontainer`
   - Push SHA tag for traceability
   - Run security scanning on primary tag

4. **Build Summary**
   - Generate detailed build report
   - Include tagging strategy explanation
   - Report both primary and SHA tags

### Environment Variables

The workflow provides these environment variables to Makefile targets:

| Variable | Source | Purpose | Example |
|----------|--------|---------|---------|
| `BRANCH_NAME` | Workflow logic | Branch being built | `main`, `release/v1.0.20250720` |
| `DOCKER_TAG` | Tagging logic | Primary Docker tag | `main`, `v1.0.20250720`, `abc1234` |
| `SHORT_SHA` | GitHub context | 7-char commit SHA | `abc1234` |
| `DOCKER_REGISTRY` | Workflow env | Registry URL | `ghcr.io` |
| `IMAGE_NAME` | Repository name | Image name | `bodhisearch/bodhi-auth-server` |
| `GIT_SHA` | GitHub context | Full commit SHA | `abc1234567890abcdef...` |
| `GIT_BRANCH` | Branch name | Git branch reference | Same as `BRANCH_NAME` |

## Makefile Integration

### CI Targets

**`ci.build-testcontainer`**
- Builds Docker image with both primary and SHA tags
- Requires: `DOCKER_REGISTRY`, `IMAGE_NAME`, `GIT_SHA`, `DOCKER_TAG`, `SHORT_SHA`
- Uses Dockerfile.testcontainer with simplified build process
- Includes build metadata (date, commit, branch)

**`ci.push-testcontainer`**
- Pushes both primary and SHA tags to registry
- Requires: `DOCKER_REGISTRY`, `IMAGE_NAME`, `DOCKER_TAG`, `SHORT_SHA`
- Provides detailed push confirmation

**`ci.security-scan`**
- Runs vulnerability scanning on primary tag
- Requires: `IMAGE_TAG` (full image reference)
- Continues on error (non-blocking)

### Removed Features

- **ProGuard obfuscation**: Removed for faster builds and simpler debugging
- **`ci.setup-obfuscation`**: No longer needed without obfuscation
- **`ci.update-branch-tag`**: Eliminated as tags are now set during build phase

## Registry Configuration

**GitHub Container Registry (ghcr.io)**
- Authentication via `GITHUB_TOKEN`
- Repository: `ghcr.io/bodhisearch/bodhi-auth-server`
- Public visibility for integration testing
- Automatic cleanup policies can be configured

## Integration Testing Usage

### Testcontainer Configuration

```java
@Testcontainers
class IntegrationTest {
  @Container
  static GenericContainer<?> keycloak = new GenericContainer<>("ghcr.io/bodhisearch/bodhi-auth-server:main")
    .withExposedPorts(8080, 9000)
    .withEnvironment("KC_BOOTSTRAP_ADMIN_USERNAME", "admin")
    .withEnvironment("KC_BOOTSTRAP_ADMIN_PASSWORD", "admin123")
    .withWaitStrategy(Wait.forHttp("/realms/master/.well-known/openid-configuration", 8080).forStatusCode(200))
    .withStartupTimeout(Duration.ofMinutes(2));
}
```

### JavaScript Testcontainers Example

```javascript
import { GenericContainer, Wait } from 'testcontainers';

const ADMIN_USER = 'admin';
const ADMIN_PASSWORD = 'admin123';

const container = await new GenericContainer('ghcr.io/bodhisearch/bodhi-auth-server:main')
  .withEnvironment({
    'KC_BOOTSTRAP_ADMIN_USERNAME': ADMIN_USER,
    'KC_BOOTSTRAP_ADMIN_PASSWORD': ADMIN_PASSWORD,
  })
  .withExposedPorts(8080, 9000) // Main port and management port
  .withWaitStrategy(Wait.forHttp('/realms/master/.well-known/openid-configuration', 8080).forStatusCode(200))
  .withStartupTimeout(120000) // 120 seconds timeout
  .start();
```

### Version-Specific Testing

```java
// Use specific release version
new GenericContainer<>("ghcr.io/bodhisearch/bodhi-auth-server:v1.0.20250720")

// Use latest main build
new GenericContainer<>("ghcr.io/bodhisearch/bodhi-auth-server:main")

// Use specific commit for reproducible tests
new GenericContainer<>("ghcr.io/bodhisearch/bodhi-auth-server:abc1234")
```

## Container Features

### Optimized Configuration
- **In-memory H2 database** for fast startup/teardown
- **Optimized JVM settings** for container environments
- **Health endpoints** available on port 9000
- **INFO level logging** for debugging test failures
- **ENTRYPOINT/CMD separation** allowing users to override options

### User-Configurable Options
Users can override default configuration by providing custom command options:

```java
.withCommand("start", "--optimized",
  "--hostname=localhost",
  "--hostname-strict=false", 
  "--http-enabled=true",
  "--log-level=DEBUG")  // Override default INFO level
```

### Performance Characteristics
- **Container startup**: ~20-30 seconds (optimized for testcontainers)
- **Memory allocation**: 4GB total, 2.8GB heap for good performance
- **Database**: In-memory H2 for fastest I/O
- **Caching**: Local-only for minimal overhead

## Security Features

### Multi-Stage Docker Build
- Separate build and runtime stages
- Minimal runtime image (Keycloak base)
- Build tools removed from final image
- Standard JAR packaging

### Vulnerability Scanning
- Automated security scanning via Trivy/Docker Scout
- Non-blocking (continues on scan failures)
- Results available in workflow logs

## Workflow Files

The following files implement this workflow:

- **`.github/workflows/testcontainer.yml`** - Main workflow definition
- **`Makefile`** - CI targets (ci.build-testcontainer, ci.push-testcontainer, ci.security-scan)
- **`Dockerfile.testcontainer`** - Multi-stage build with simplified process
- **`js-testcontainers/test.js`** - JavaScript testcontainer integration example

## Monitoring and Troubleshooting

### Build Status
- GitHub Actions provides build status and logs
- Build summary includes tag information and container features
- Failed builds block image publishing

### Common Issues
1. **Extension Loading**: Verify JAR file is correctly copied to `/opt/keycloak/providers/`
2. **Registry Authentication**: Verify `GITHUB_TOKEN` permissions
3. **Tag Format Issues**: Ensure release branch naming follows `release/vX.Y.Z` pattern
4. **Container Startup**: Check health endpoint and wait strategy configuration

### Debugging
- All Makefile targets include verbose logging
- Environment variables are validated before use
- Docker build logs include extension loading progress
- Push operations confirm successful tag uploads
- Container logs available via `docker logs` for debugging

## Future Enhancements

### Planned Improvements
- Multi-platform builds (ARM64 support)
- Build caching optimization
- Advanced health check configurations
- Runtime integrity checks

### Configuration Options
- Configurable startup timeouts
- Custom security scanning rules
- Branch-specific build configurations
- Automated cleanup policies 