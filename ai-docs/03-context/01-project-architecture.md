# Project Architecture and Structure

## Overview
This is a Keycloak SPI (Service Provider Interface) extension that implements a multi-tenant OAuth2 token exchange system with client-level role-based access control. The project provides custom authorization policies and resource management APIs for Keycloak with two distinct client types: app clients (public) and resource clients (confidential).

## Project Structure

```
keycloak-bodhi-ext/
├── src/main/java/com/bodhisearch/           # Core SPI implementations
├── src/main/resources/META-INF/services/    # SPI registration files
├── src/test/java/com/bodhisearch/           # Comprehensive test suite
├── src/test/resources/                      # Test configurations and templates
├── tools/                                   # External tools (keycloak-config-cli)
├── realm-setup.json                         # Base realm configuration
└── pom.xml                                  # Maven build configuration
```

## Core SPI Implementations

### 1. BodhiResourceProvider (RealmResourceProvider)
**File**: `src/main/java/com/bodhisearch/BodhiResourceProvider.java`
**Registration**: `src/main/resources/META-INF/services/org.keycloak.services.resource.RealmResourceProviderFactory`

**Purpose**: Provides REST endpoints for client management and user group operations
**Provider ID**: `bodhi`
**Base URL Pattern**: `{keycloak-url}/realms/{realm}/bodhi/`

**Key Endpoints**:
- `POST /clients` - Register new OAuth resource server clients (confidential clients)
- `POST /apps` - Register new OAuth app clients (public clients for frontend applications)
- `POST /clients/make-resource-admin` - Assign admin role to users
- `POST /clients/add-user-to-group` - Add users to client-specific groups
- `GET /clients/has-resource-admin` - Check if client has admin users

### 2. AudienceMatchPolicyProvider (PolicyProvider)
**File**: `src/main/java/com/bodhisearch/AudienceMatchPolicyProvider.java`
**Registration**: `src/main/resources/META-INF/services/org.keycloak.authorization.policy.provider.PolicyProviderFactory`

**Purpose**: Custom authorization policy for token exchange validation
**Policy Name**: `audience-match-policy`

**Validation Logic**:
- Extracts Bearer token from Authorization header
- Validates token signature and issuer
- Ensures token audience matches the requested exchange target
- Grants/denies token exchange based on validation results

## Factory Classes

### BodhiResourceProviderFactory
**File**: `src/main/java/com/bodhisearch/BodhiResourceProviderFactory.java`
- Creates BodhiResourceProvider instances
- Configures provider ID as "bodhi"
- Handles provider lifecycle management

### AudienceMatchPolicyProviderFactory
**File**: `src/main/java/com/bodhisearch/AudienceMatchPolicyProviderFactory.java`
- Creates AudienceMatchPolicyProvider instances
- Configures policy type as "audience-match"
- Provides policy configuration metadata

## Data Models

### AudienceMatchRepresentation
**File**: `src/main/java/com/bodhisearch/AudienceMatchRepresentation.java`
- Data transfer object for audience match policy configuration
- Used in policy creation and management

### Request/Response Models (in BodhiResourceProvider)
- `ResourceRequest` - Client registration request payload
- `ErrorResponse` - Standardized error response format
- Various internal data classes for API communication

## Build Configuration

### Maven Dependencies (pom.xml)
**Keycloak Version**: 23.0.7
**Java Version**: 17

**Core Dependencies**:
- `keycloak-server-spi` - Core SPI interfaces
- `keycloak-server-spi-private` - Internal SPI access
- `keycloak-core` - Keycloak core functionality
- `keycloak-services` - Keycloak service layer

**Test Dependencies**:
- `testcontainers-keycloak` - Keycloak test containers
- `rest-assured` - REST API testing
- `junit-jupiter` - JUnit 5 testing framework
- `playwright` - Browser automation for integration tests
- `freemarker` - Template generation for test configurations

### Build Plugins
- `maven-surefire-plugin` - Test execution with pattern matching
- `exec-maven-plugin` - Playwright browser installation
- `maven-jar-plugin` - JAR packaging with manifest entries

## Environment Configuration

### Environment Variables
- `APP_ENV=test` - Enables test-specific features in providers
- `CI=true` - Enables headless browser testing for Playwright
- Used to control client naming prefixes and access grant settings

### Test Environment Features
- Direct access grants enabled for testing
- Test prefix for client IDs when `live_test=true` query parameter
- App clients: `app-{id}` (production) vs `test-app-{id}` (test)
- Resource clients: `resource-{id}` (production) vs `test-resource-{id}` (test)
- Enhanced logging and debugging capabilities

## Deployment Artifacts

### JAR Structure
- Main classes in default package structure
- SPI registration files in META-INF/services
- No external configuration files required
- Self-contained deployment unit

### Keycloak Integration
- Deployed as provider JAR in Keycloak providers directory
- Requires Keycloak features: `admin-fine-grained-authz`, `token-exchange`
- Automatically registered via SPI discovery mechanism

## Key Architectural Patterns

### 1. SPI-Based Extension
- Follows Keycloak's standard SPI pattern
- Clean separation between factory and provider implementations
- Proper lifecycle management and resource cleanup

### 2. Environment-Aware Configuration
- Runtime behavior modification based on APP_ENV
- Test-specific features without code duplication
- Query parameter-based feature toggles

### 3. Comprehensive Error Handling
- Structured error responses with consistent format
- Detailed logging for debugging and monitoring
- Graceful degradation for edge cases

### 4. Security-First Design
- Token validation at multiple levels
- Authorization checks for all operations
- Secure defaults with explicit permission grants

## Client Types and Management

### 1. App Clients (Public Clients)
**Endpoint**: `POST /realms/{realm}/bodhi/apps`
**Purpose**: Frontend application OAuth clients with simplified configuration
**Authentication**: User tokens from client-bodhi-dev-console
**Configuration**:
- Public client (no client secret)
- Standard flow enabled (Authorization Code)
- Direct access grants disabled
- Service accounts disabled
- Single admin role with automatic user assignment

### 2. Resource Clients (Confidential Clients)
**Endpoint**: `POST /realms/{realm}/bodhi/clients`
**Purpose**: Backend API services requiring authorized access
**Authentication**: Service account tokens
**Configuration**:
- Confidential client (with client secret)
- Service account enabled
- Token exchange enabled
- Full 4-level role hierarchy (resource_user/resource_power_user/resource_manager/resource_admin)
- Comprehensive group structure with role mappings
