# System Overview: Keycloak Bodhi Extension

## Core Innovation: Dynamic Audience Management for Multi-Tenant Marketplaces

The Keycloak Bodhi Extension implements a **unique multi-tenant marketplace OAuth2 token exchange system** with dynamic audience management for app-to-resource connections using Keycloak's standard token exchange v2 features.

## 🎯 Unique Domain Problem

This project solves a complex marketplace scenario where:

### The Challenge
- **M apps × N resources** = potentially M×N audience mapping combinations
- **On-demand audience configuration**: When App A wants to connect to Resource R, the audience mapper must be configured dynamically
- **Consent-based authorization**: Users must explicitly consent to which resource APIs their tokens will access
- **Performance constraints**: Avoid database explosion with static pre-configured mappers
- **Security requirements**: Prevent privilege leakage and ensure proper access control

### Real-World Scenario
```
Marketplace with:
- 100 App Clients (public, no secrets)
- 50 Resource Servers (confidential, with secrets)
- Potential: 5,000 audience mapping combinations
- Dynamic: Only create mappings when actually needed
- Consent: Show users exactly which resources they're granting access to
```

## 🚀 Modern Solution: Keycloak 26.2+ Standard Token Exchange

### Key Innovation: Scope-Based Dynamic Audience Resolution

Instead of static audience mappers, we use:

1. **Client Scopes as Resource Identifiers**
   ```
   scope_resource-inventory    → Can access inventory API
   scope_resource-payment      → Can access payment API
   scope_resource-analytics    → Can access analytics API
   ```

2. **Dynamic Audience via OAuth Scope Parameter**
   ```http
   # App A requests access to Resource R dynamically
   GET /auth?scope=openid profile scope_resource-inventory
   ```

3. **Consent Screen with Resource Context**
   ```
   App "MyMarketplace" wants to:
   ✓ Access your basic profile
   ✓ Access inventory APIs from InventoryService
   ✓ Access payment APIs from PaymentGateway
   ```

### Architecture Components

#### 1. **App Clients (Public)**
- **Type**: Public clients (no client secrets)
- **Prefix**: `app-` (production) / `test-app-` (testing)
- **Authentication**: Authorization code flow with PKCE
- **Roles**: Simplified admin-only structure
- **Group Structure**: `users-{client-id}/admins`

#### 2. **Resource Server Clients (Confidential)**
- **Type**: Confidential clients (with client secrets)
- **Prefix**: `resource-` (production) / `test-resource-` (testing)
- **Authentication**: Service account + client credentials
- **Roles**: 4-level hierarchy (user/power_user/manager/admin)
- **Group Structure**: `users-{client-id}/[users|power-users|managers|admins]`
- **Token Exchange**: `standard.token.exchange.enabled=true`

#### 3. **Dynamic Audience Resolution**
```java
// In BodhiResourceProvider.java
client.setAttribute("standard.token.exchange.enabled", "true");

// Client scopes represent resource access
// Audience is resolved dynamically based on requested scopes
// No static mappers needed!
```

### 🔧 Technical Implementation

#### Token Exchange V2 Flow
```http
# 1. App gets user token with specific resource scope
GET /auth?client_id=app-123&scope=openid scope_resource-inventory

# 2. Resource server exchanges for properly scoped token
POST /token
grant_type=urn:ietf:params:oauth:grant-type:token-exchange
&subject_token={app_token}
&audience=resource-inventory-server
&scope=scope_resource-inventory
```

#### Key Features
- ✅ **No database explosion**: Scopes are lightweight, reusable
- ✅ **On-demand audience**: Only requested resources in token audience
- ✅ **Consent-based**: Users see exactly what resources they're granting
- ✅ **Performance optimized**: Scope-based resolution vs. static mappers
- ✅ **Security**: Proper audience validation per RFC 8693

### 🏗️ Project Structure

```
src/main/java/com/bodhisearch/
├── BodhiResourceProvider.java          # Main SPI implementation

src/test/java/com/bodhisearch/
├── integration/                        # Integration tests
│   ├── TokenExchangeIntegrationTest.java
│   └── AppRegistrationIntegrationTest.java
└── util/                               # Test utilities
    ├── BodhiProviderClient.java        # Bodhi SPI client
    └── KeycloakAdminClient.java        # Keycloak admin operations

httpyac-scripts/                                # HTTPyac testing scripts
├── token-exchange-v2-aud.http         # Dynamic audience demo
├── app-clients.http                    # App client management
└── resource-clients.http              # Resource client management
```

## 🎮 Demo & Testing

### Quick Start
```bash
# Start Keycloak with token exchange enabled
make dev-up

# Test with HTTPyac
httpyac httpyac-scripts/token-exchange-v2-aud.http --all
```

### What the Demo Shows
1. **Dynamic client creation** (app + resource)
2. **Scope-based audience configuration** (no static mappers!)
3. **OAuth2 authorization flow** with resource-specific consent
4. **Token exchange v2** with proper audience validation
5. **Complete marketplace flow** from app authentication to resource access

## 🔬 Technical Deep Dive

### Core Innovation: Scope-to-Audience Resolution

Traditional approach (problematic):
```java
// Static mapper for each app-resource pair
audienceMapper.setConfig("included.client.audience", "resource-server-123");
// Result: M×N mappers in database
```

Our approach (scalable):
```java
// Dynamic resolution via client scopes
// Scope "scope_resource-inventory" automatically includes 
// "resource-inventory-server" in audience
// Result: Only N scopes for N resources
```

### Environment-Aware Client Prefixes
- **Production**: `app-` / `resource-`
- **Testing**: `test-app-` / `test-resource-`
- **Development**: Configurable via `APP_ENV` and `live_test` parameter

### Security Model
- **App Clients**: Public, consent-required, simplified roles
- **Resource Clients**: Confidential, service accounts, full role hierarchy
- **Token Exchange**: Standard RFC 8693 validation
- **Consent**: Resource-specific scope descriptions

## 🧪 Testing Strategy

### Integration Tests
- **Token Exchange Validation**: Ensure proper audience inclusion
- **Multi-tenant Isolation**: Verify client separation
- **OAuth Flow Testing**: Complete authorization flows
- **Error Handling**: Proper error responses and logging

### HTTPyac Scripts
- **Dynamic Audience Demo**: `token-exchange-v2-aud.http`
- **Client Management**: App and resource client operations
- **Token Exchange V2**: RFC 8693 compliant flows

## 🚀 Production Considerations

### Scalability
- **Scope-based design**: O(N) complexity instead of O(M×N)
- **Dynamic audience**: Only active connections consume resources
- **Efficient caching**: Keycloak's built-in scope resolution caching

### Security
- **Principle of least privilege**: Users only consent to needed resources
- **Audit trail**: Full consent and token exchange logging
- **Revocation support**: Proper token lifecycle management

### Monitoring
- **Metrics**: Track scope usage, token exchange rates
- **Alerting**: Monitor unusual audience patterns
- **Compliance**: Full audit logs for marketplace transactions

## 📚 References

- [RFC 8693: OAuth 2.0 Token Exchange](https://datatracker.ietf.org/doc/html/rfc8693)
- [Keycloak 26.2+ Token Exchange Documentation](https://www.keycloak.org/securing-apps/token-exchange)
- [OAuth 2.0 Security Best Current Practice](https://datatracker.ietf.org/doc/html/draft-ietf-oauth-security-topics)

## 🤝 Contributing

This project demonstrates advanced OAuth2 patterns for marketplace scenarios. Contributions focusing on:
- Performance optimizations
- Additional security patterns
- Extended testing scenarios
- Documentation improvements

are welcome!

---

**Note**: This implementation showcases a production-ready solution for complex multi-tenant marketplace OAuth2 scenarios using modern Keycloak features. The dynamic audience approach scales efficiently while maintaining security and user consent requirements. 