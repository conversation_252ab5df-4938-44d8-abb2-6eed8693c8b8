# Keycloak Production Deployment Context

## Overview

This document describes the current production-ready Keycloak deployment configuration optimized for Railway.app platform. The implementation follows official Keycloak production guidelines and includes security hardening, performance optimizations, and monitoring capabilities.

**Last Updated**: January 2025  
**Keycloak Version**: 26.2.5  
**Target Platform**: Railway.app (single-node deployment)

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Railway.app   │    │   Keycloak       │    │   PostgreSQL    │
│   Edge Proxy    │───▶│   Container      │───▶│   Database      │
│   (TLS Term.)   │    │   (Port 8080)    │    │   (Managed)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   Management     │
                       │   Interface      │
                       │   (Port 9000)    │
                       └──────────────────┘
```

## Implementation Status

### ✅ Completed Optimizations

| Category | Optimization | Status | Impact |
|----------|-------------|--------|---------|
| **Security** | Reverse proxy headers (`--proxy-headers=xforwarded`) | ✅ | Critical - Railway edge TLS |
| **Security** | Trusted proxy addresses configured | ✅ | Medium - IP filtering |
| **Security** | Admin console access restricted | ✅ | High - Attack surface reduction |
| **Performance** | Request queue limiting (`--http-max-queued-requests=1000`) | ✅ | High - Load shedding |
| **Performance** | Database connection pool optimization | ✅ | High - Resource efficiency |
| **Performance** | Cache optimization for single-node | ✅ | Medium - Memory management |
| **Performance** | JVM container optimizations | ✅ | Medium - Resource utilization |
| **Monitoring** | Health checks enabled (`/health`) | ✅ | High - Operational visibility |
| **Monitoring** | Metrics enabled (`/metrics`) | ✅ | High - Performance monitoring |
| **Logging** | Production log levels configured | ✅ | Medium - Operational clarity |

## Configuration Details

### Core Production Settings

**Dockerfile CMD Configuration:**
```dockerfile
CMD ["start", "--optimized", \
     "--hostname-strict=false", \
     "--hostname-backchannel-dynamic=true", \
     "--proxy-headers=xforwarded", \
     "--http-enabled=true", \
     "--log-level=INFO", \
     "--log-console-color=false", \
     "--http-max-queued-requests=1000", \
     "--db-pool-initial-size=5", \
     "--db-pool-max-size=20", \
     "--db-pool-min-size=5"]
```

**Build-time Optimizations:**
```dockerfile
RUN /opt/keycloak/bin/kc.sh build \
    --db=postgres \
    --http-relative-path=/ \
    --features=token-exchange \
    --health-enabled=true \
    --metrics-enabled=true \
    --transaction-xa-enabled=false
```

### Environment Variables (Railway)

```bash
# Core configuration
KC_HOSTNAME=https://keycloak-bodhi-ext-production.up.railway.app
KC_PROXY_HEADERS=xforwarded
KC_HTTP_MAX_QUEUED_REQUESTS=1000

# Database optimization
KC_DB_POOL_INITIAL_SIZE=5
KC_DB_POOL_MAX_SIZE=20
KC_DB_POOL_MIN_SIZE=5

# Cache optimization (single-node)
KC_CACHE=ispn
KC_CACHE_STACK=jdbc-ping
KC_CACHE_EMBEDDED_REALMS_MAX_COUNT=5000
KC_CACHE_EMBEDDED_USERS_MAX_COUNT=10000
KC_CACHE_EMBEDDED_SESSIONS_MAX_COUNT=5000
KC_CACHE_EMBEDDED_CLIENT_SESSIONS_MAX_COUNT=5000
KC_CACHE_EMBEDDED_OFFLINE_SESSIONS_MAX_COUNT=1000
KC_CACHE_EMBEDDED_OFFLINE_CLIENT_SESSIONS_MAX_COUNT=1000
KC_CACHE_EMBEDDED_AUTHORIZATION_MAX_COUNT=2000
KC_CACHE_EMBEDDED_KEYS_MAX_COUNT=500

# Logging and monitoring
KC_LOG_LEVEL=INFO

# Container optimizations
JAVA_OPTS_APPEND="-XX:+UseContainerSupport -XX:InitialRAMPercentage=25 -XX:MaxRAMPercentage=75 -XX:+UseG1GC -XX:MaxGCPauseMillis=100"

# Security
KC_PROXY_TRUSTED_ADDRESSES="10.0.0.0/8,**********/12,***********/16"
```

### Port Configuration

| Port | Purpose | Exposure | Description |
|------|---------|----------|-------------|
| 8080 | Main HTTP | Public | Authentication endpoints, admin console |
| 9000 | Management | Internal | Health checks, metrics |
| 5005 | Debug | Development | JVM debug port (dev only) |

## Performance Characteristics

### Startup Performance
- **Cold start time**: ~28 seconds
- **Database migration**: Automatic on first run
- **Cache initialization**: JDBC_PING discovery (~2 seconds)
- **Memory footprint**: Optimized for container limits

### Runtime Performance
- **Request handling**: Up to 1000 queued requests
- **Database connections**: 5-20 connections (auto-scaling)
- **Cache efficiency**: Optimized for single-node deployment
- **GC performance**: G1GC with 100ms pause target

## Monitoring and Observability

### Health Checks
- **Endpoint**: `http://localhost:9000/health`
- **Response**: JSON with database connection status
- **Usage**: Container health checks, load balancer probes

```json
{
    "status": "UP",
    "checks": [
        {
            "name": "Keycloak database connections async health check",
            "status": "UP"
        }
    ]
}
```

### Metrics
- **Endpoint**: `http://localhost:9000/metrics`
- **Format**: Prometheus format
- **Metrics count**: ~1,645 metrics
- **Categories**: JVM, database, cache, JGroups, HTTP

**Key Metrics Available:**
- JVM memory and GC statistics
- Database connection pool metrics
- Cache hit/miss ratios
- HTTP request metrics
- JGroups cluster metrics

## Security Configuration

### Network Security
- **TLS Termination**: Railway edge proxy
- **Internal Communication**: HTTP (behind proxy)
- **Trusted Proxies**: Private network ranges configured
- **Admin Access**: Restricted (not exposed publicly in production)

### Path Exposure
Following Keycloak security recommendations:

| Path | Exposed | Reason |
|------|---------|---------|
| `/` | No | Unnecessary exposure |
| `/admin/` | No | Security - admin interface |
| `/realms/` | Yes | Required for OIDC/SAML |
| `/resources/` | Yes | Static assets |
| `/health` | No | Internal monitoring only |
| `/metrics` | No | Internal monitoring only |

## Database Configuration

### Connection Pool Settings
- **Initial connections**: 5
- **Maximum connections**: 20
- **Minimum connections**: 5
- **Database**: PostgreSQL (Railway managed)

### Migration Strategy
- **Automatic migrations**: Enabled
- **Changelog**: Liquibase with `META-INF/jpa-changelog-master.xml`
- **First run**: Creates all required tables
- **Subsequent runs**: Applies incremental changes

## Cache Configuration

### Cache Strategy
**Single-node optimized** - No clustering overhead

### Cache Limits
- **Realms**: 5,000 entries (metadata caching)
- **Users**: 10,000 entries (user data caching)
- **Sessions**: 5,000 entries (active user sessions)
- **Client Sessions**: 5,000 entries (application sessions)
- **Offline Sessions**: 1,000 entries (offline tokens)
- **Authorization**: 2,000 entries (permissions/policies)
- **Keys**: 500 entries (external public keys)

### Cache Types
- **Local caches**: realms, users, authorization, keys
- **Distributed caches**: sessions, clientSessions, offlineSessions
- **Replicated cache**: work (invalidation messages)

## Testing and Validation

### Test Script
- **File**: `test-docker-compose-release.sh`
- **Duration**: 120 seconds startup timeout
- **Validation**: HTTP endpoints, health checks, log analysis
- **Output**: Structured analysis with success/failure determination

### Test Results
```
✅ Main endpoint: HTTP/1.1 200 OK
✅ Health check: UP  
✅ Metrics: 1,645 metrics available
✅ Startup time: ~28 seconds
✅ All optimizations applied successfully
```

## Deployment Process

### Local Testing
1. Run `./test-docker-compose-release.sh`
2. Verify all endpoints respond correctly
3. Check logs for any configuration issues
4. Validate performance metrics

### Railway Deployment
1. Push changes to main branch
2. Railway auto-deploys from GitHub
3. Environment variables configured in Railway dashboard
4. Health checks validate deployment success

## Troubleshooting

### Common Issues

**1. JSON Logging Issue**
- **Problem**: Container outputs "json" repeatedly
- **Cause**: `KC_LOG_CONSOLE_FORMAT=json` conflicts with optimized build
- **Solution**: Remove JSON format setting, use default structured logging

**2. Health/Metrics Not Available**
- **Problem**: Build-time options with `--optimized`
- **Solution**: Enable in build phase, expose port 9000

**3. Cache Configuration Errors**
- **Problem**: Kubernetes cache stack in Docker environment
- **Cause**: `--cache-stack=kubernetes` hardcoded
- **Solution**: Use `jdbc-ping` stack for Docker deployment

### Performance Tuning

**Memory Issues:**
- Adjust `MaxRAMPercentage` in `JAVA_OPTS_APPEND`
- Modify cache limits based on usage patterns
- Monitor GC metrics via `/metrics` endpoint

**Database Performance:**
- Adjust connection pool sizes based on load
- Monitor connection utilization
- Consider read replicas for high-traffic scenarios

## Future Enhancements

### Potential Optimizations
1. **JSON Structured Logging**: Resolve compatibility issues
2. **Advanced Cache Tuning**: Environment-specific cache sizes
3. **Resource Limits**: Container resource optimization
4. **Security Hardening**: Additional security headers
5. **Performance Monitoring**: APM integration

### Railway-Specific Improvements
1. **Auto-scaling**: Configure based on metrics
2. **Resource Allocation**: Optimize for Railway pricing tiers
3. **Backup Strategy**: Database backup automation
4. **Multi-region**: Consider multi-region deployment

## References

### Official Documentation
- [Keycloak Production Configuration](https://www.keycloak.org/server/configuration-production)
- [Keycloak Reverse Proxy Guide](https://www.keycloak.org/server/reverseproxy)
- [Keycloak Container Guide](https://www.keycloak.org/server/containers)
- [Keycloak Caching Guide](https://www.keycloak.org/server/caching)

### Implementation Files
- `Dockerfile` - Container build configuration
- `docker-compose-release.yml` - Local testing environment
- `test-docker-compose-release.sh` - Validation script
- `cache-ispn-jdbc-ping.xml` - Cache configuration
- `railway.toml` - Railway deployment configuration

### Related Context Documents
- `ai-docs/03-context/railway-deployment.md` - Railway platform specifics
- `ai-docs/03-context/security-configuration.md` - Security implementation details
- `ai-docs/03-context/monitoring-setup.md` - Observability configuration

---

**Status**: Production Ready ✅  
**Next Review**: Quarterly or after major Keycloak updates  
**Maintenance**: Monitor performance metrics and adjust as needed 