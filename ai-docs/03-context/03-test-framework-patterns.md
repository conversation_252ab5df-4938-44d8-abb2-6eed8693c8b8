# Test Framework and Patterns

## Test Architecture Overview

The project follows a comprehensive testing strategy with multiple test types and extensive coverage. All tests extend from `BaseTest` which provides Keycloak testcontainer setup and common utilities.

### Test Categories

#### 1. Unit Tests
- **AudienceMatchPolicyProviderTest**: Token exchange policy validation
- **BodhiResourceProviderTest**: Resource provider endpoint testing (includes app client tests)
- **KeycloakConfigurationTest**: Keycloak-specific configuration validation

#### 2. Integration Tests  
- **BodhiIntegrationTest**: End-to-end workflow testing
- **RealmSeedingTest**: Realm configuration and template generation
- **BaseIntegrationTest**: Browser-based integration test foundation
- **AppRegistrationIntegrationTest**: Full app client workflow testing with UI automation
- **OAuthFlowIntegrationTest**: Complete OAuth flow testing with Playwright

#### 3. Configuration Tests
- **KeycloakConfigurationTest**: Scope and claim validation
- **RealmSeedingTest**: Template generation and import validation

#### 4. UI Integration Tests (Playwright-based)
- **AppRegistrationIntegrationTest**: End-to-end app client registration testing
- **OAuthFlowIntegrationTest**: Complete OAuth authentication flow testing
- **Browser-based validation**: Real browser testing of authentication flows

## BaseTest Foundation

### Core Setup (`src/test/java/com/bodhisearch/BaseTest.java`)

**Key Features**:
- **Testcontainers Integration**: Isolated Keycloak instance per test run
- **Automatic Realm Setup**: Dynamic realm generation and import via `RealmConfigGenerator`
- **Provider Loading**: Extension classes loaded from `target/classes`
- **Environment Configuration**: `APP_ENV=test` enables test-specific features

**Container Configuration**:
```java
@Container
public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:26.2.5")
    // .withFeaturesEnabled("admin-fine-grained-authz", "token-exchange")
    .withProviderClassesFrom("target/classes")
    .withEnv("APP_ENV", "test");
```

### Shared Test Infrastructure
- **Client Instances**: `BodhiProviderClient`, `KeycloakAdminClient`
- **URL Building**: Standardized URL construction for endpoints
- **Token Management**: Helper methods for token acquisition and validation

## Test Utility Classes

### 1. BodhiProviderClient (`src/test/java/com/bodhisearch/util/BodhiProviderClient.java`)
**Purpose**: Wrapper for Bodhi extension endpoints

**Key Methods**:
- `registerClient(String redirectUri)` - Resource client registration
- `registerApp(String token, AppRequest request)` - App client registration  
- `makeResourceAdmin(String token, String userEmail)` - Admin assignment
- `addUserToGroup(String token, String userEmail, String group)` - Group management

### 2. KeycloakAdminClient (`src/test/java/com/bodhisearch/util/KeycloakAdminClient.java`)
**Purpose**: Wrapper for Keycloak Admin API operations

**Key Methods**:
- `getUserTokenPairWith(ClientPair client, String username, String password, List<String> scopes)`
- `exchangeTokenResponse(String clientId, String subjectToken, String audience, String authHeader)`
- `getTokenForClient(ClientPair client)` - Service account tokens

### 3. Data Transfer Objects
- **ClientPair**: `{clientId, clientSecret}`
- **TokenPair**: `{access, refresh}`
- **TestUser**: `{email, password, firstName, lastName}`

### 4. KeycloakTestUtils (`src/test/java/com/bodhisearch/util/KeycloakTestUtils.java`)
**Purpose**: Low-level test utilities
- REST-assured configuration via `given()`
- Realm import using `importFile()` with keycloak-config-cli

## Test Configuration Generation

### RealmConfigGenerator (`src/test/java/com/bodhisearch/templates/RealmConfigGenerator.java`)

**Purpose**: Dynamic realm configuration generation using FreeMarker templates

**Key Methods**:
- `generate(String filename)` - Standard test configuration with seed user
- `generateForIntegrationTests(String filename, int port)` - Integration test configuration
- `generateWithDataModel(String filename, Map<String, Object> dataModel)` - Custom configuration

**Template Data Model Structure**:
- `resources`: Resource server IDs (e.g., `["resource-seed"]`)
- `clients`: Additional client configurations
- `users`: Pre-created users (empty for integration tests)
- `clientRoles`: Standard role definitions (`CLIENT_ROLES`)
- `userGroups`: Group structure with role mappings (`GROUPS`)

## Test Patterns and Conventions

### 1. Test Organization Pattern
```java
public class ExampleTest extends BaseTest {
  // ========================================
  // SUCCESSFUL OPERATION TESTS
  // ========================================
  
  @Test
  public void testSuccessfulOperation() { /* implementation */ }
  
  // ========================================
  // ERROR CONDITION TESTS  
  // ========================================
  
  @Test
  public void testErrorCondition() { /* implementation */ }
}
```

### 2. Client Creation Pattern
**Resource Clients**: `registerClientAndReturnClientPair()` - Returns confidential client with service account
**App Clients**: `createPublicClientForUser()` - Returns public client for OAuth flows
**Service Tokens**: `getTokenForClient(clientPair)` - Gets service account token

### 3. User Management Pattern
**User Creation**: `createUserWithRole(clientId, "resource_user")` - Creates user with specific role
**Admin Assignment**: `makeResourceAdmin(serviceToken, adminUser.email)` - Assigns admin role
**Token Acquisition**: `getUserTokenPairWith(clientPair, user.email, user.password, scopes)`

### 4. Token Exchange Testing Pattern
**Setup**: Create app client, resource client, and test user
**Flow**: Get user token → Get resource service token → Exchange → Validate
**Validation**: Status codes, token content, JWT claims via `decodeToken()`

### 5. Assertion Patterns
**HTTP Responses**: `response.then().statusCode(200).body("error", nullValue())`
**Token Content**: `assertThat(clientId, in(token.getAudience()))`
**JWT Claims**: `DecodedJWT jwt = JWT.decode(tokenString); assertEquals("RS256", jwt.getAlgorithm())`

## Integration Test Patterns

### Browser-Based Testing (BaseIntegrationTest)

#### Core Setup (`src/test/java/com/bodhisearch/integration/BaseIntegrationTest.java`)
**Components**:
- **NodeServer**: Serves test HTML/JS from `src/test/resources/test-app`
- **Playwright**: Browser automation with CI headless support
- **Realm Generation**: Integration-specific configuration via `generateForIntegrationTests()`

**Environment Detection**:
```java
boolean isHeadless = System.getenv("CI") != null;  // Headless mode for CI
```

### Playwright UI Test Utilities (UITestUtils)

#### OAuth Flow Automation (`src/test/java/com/bodhisearch/util/UITestUtils.java`)
**Key Methods**:
- `createUserThroughUI()` - Complete user registration via OAuth
- `performOAuthLogin()` - Login for existing users
- `verifyTokenClaims()` - JWT token validation

**Flow Pattern**: Navigate → Configure client → Start OAuth → Handle auth → Extract token

### App Client Integration Testing

#### Complete Workflow Testing (`AppRegistrationIntegrationTest`)
**Pattern**: 
1. Setup shared user and access token via OAuth flow
2. Navigate to API testing page (`/api.html`)
3. Configure and send app registration request
4. Validate response via UI and Admin API
5. Verify client configuration, roles, and groups

**Admin API Validation**: Uses `keycloak.getKeycloakAdminClient()` to verify:
- Client configuration matches expectations
- Role creation and assignment
- Group structure and user membership

### Environment-Aware Testing

#### CI/CD Integration
- **Headless Detection**: `System.getenv("CI")` for headless browser mode
- **Test App Server**: Dynamic port allocation for parallel test execution
- **Realm Configuration**: Port-specific redirect URIs for isolation

#### Test Data Generation
**Integration Tests**: No pre-created users, dynamic port-based redirect URIs
**Unit Tests**: Pre-seeded users and clients for faster execution

## Test Coverage Strategy

### 1. Comprehensive Scenario Coverage
- **Happy Path**: Standard flows with valid inputs
- **Error Cases**: Invalid tokens, missing permissions, malformed requests
- **Edge Cases**: Empty parameters, boundary conditions
- **Security**: Unauthorized access attempts, token manipulation
- **UI Flows**: Complete browser-based OAuth authentication flows
- **Cross-Client Integration**: App client to resource server token exchange

### 2. Multi-Level Testing
- **Unit Level**: Individual component behavior (mocked dependencies)
- **Integration Level**: Component interaction via HTTP APIs
- **End-to-End**: Complete user workflows via browser automation
- **Configuration Level**: Keycloak setup validation
- **UI Level**: Browser-based user interface testing

### 3. Test Data Management
- **Dynamic Generation**: Realm configs generated per test run
- **Isolation**: Each test creates its own clients and users
- **Cleanup**: Testcontainers provide automatic cleanup
- **Repeatability**: Deterministic test data generation
- **Environment-Aware**: Different configurations for CI vs local development

### 4. Browser Testing Strategy
- **Playwright Integration**: Modern browser automation with reliable selectors
- **Headless CI**: Automatic headless mode detection for CI environments
- **Cross-Browser Support**: Chromium-based testing with extensibility
- **Real OAuth Flows**: Testing complete authentication workflows
- **Token Validation**: Extracting and validating JWT tokens from UI

## Build Integration

### Maven Configuration
**Dependencies**: Playwright (`1.49.0`), Testcontainers, REST-assured
**Plugin**: `exec-maven-plugin` for Playwright browser installation
**Test Execution**: `maven-surefire-plugin` with pattern matching

### CI Configuration (Makefile)
```makefile
ci.setup:
  ./mvnw dependency:go-offline
  ./mvnw -q exec:java -Dexec.mainClass=com.microsoft.playwright.CLI \
    -Dexec.classpathScope=test -Dexec.args="install chromium --with-deps"
```

## Development Testing Practices

### 1. Test-First Development
- Write tests before implementing features
- Use tests to define expected behavior
- Include UI tests for user-facing features

### 2. Comprehensive Coverage
- Test all public methods and endpoints
- Cover error conditions and edge cases
- Validate security and authorization logic
- Test complete user workflows via UI automation

### 3. Maintainable Test Code
- Use descriptive test names and comments
- Follow consistent patterns across test classes
- Extract common operations to utility methods
- Keep tests focused and independent
- Use Page Object Model for UI tests where appropriate

### 4. Environment-Aware Testing
- Support both headless and headed browser modes
- Handle CI vs local development differences
- Provide debugging capabilities for test failures
- Ensure tests are reliable across different environments
