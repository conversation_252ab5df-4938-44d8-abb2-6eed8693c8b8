# Keycloak Realm Import System

## Overview

The Keycloak realm import system provides environment-specific realm configurations using `keycloak-config-cli`, implementing a pattern similar to HTTPyac environments but adapted for keycloak-config-cli's sequential file processing capabilities.

## Architecture Breakthrough

### Multi-File Import Discovery ✅

**Key Finding**: keycloak-config-cli v6.4.0 supports comma-separated file paths in `--import.files.locations` parameter, enabling a HTTPyac-like pattern with common + environment-specific configurations.

### File Structure
```
realm-import-files/                    # Moved to project root
├── README.md                         # Comprehensive usage documentation
├── import_realm.py                   # Python automation script with environment defaults
├── common.json                       # Shared realm configuration
├── test-env.json                     # Test environment client config
├── local-env.json                    # Local development client config
├── dev-env.json                      # Development environment client config
├── main-env.json                     # Main/staging environment client config
└── prod-env.json                     # Production environment client config
```

## Implementation Strategy

### Sequential Processing Pattern

keycloak-config-cli processes multiple files **sequentially**, allowing for a two-stage import:

1. **Stage 1 (`common.json`)**: Creates realm with shared elements
   - Client scopes (roles, scope_token_user, scope_token_power_user, etc.)
   - Basic realm settings (enabled, registrationEmailAsUsername, etc.)
   - Default scope mappings

2. **Stage 2 (`{env}-env.json`)**: Adds/updates environment-specific elements
   - Client configurations (client-bodhi-dev-console, account)
   - Environment-specific settings (registrationAllowed, verifyEmail, directAccessGrantsEnabled)

### Multi-File Import Command
```bash
# Working multi-file import pattern
java -jar tools/keycloak-config-cli-26.1.0.jar \
  --import.files.locations=common.json,test-env.json \
  --keycloak.url=http://localhost:8080 \
  --keycloak.user=admin \
  --keycloak.password=admin
```

## Environment Configuration Matrix

| Environment | Registration | Email Verification | Direct Access Grants | Service Accounts |
|-------------|--------------|-------------------|---------------------|------------------|
| **test**    | ✅ true      | ❌ false          | ✅ true             | ✅ true          |
| **local**   | ✅ true      | ❌ false          | ❌ false            | ❌ false         |
| **dev**     | ✅ true      | ❌ false          | ❌ false            | ❌ false         |
| **main**    | ✅ true      | ❌ false          | ❌ false            | ❌ false         |
| **prod**    | ✅ true      | ✅ **true**       | ❌ false            | ❌ false         |

## Usage Patterns

### Option 1: Makefile Targets (Recommended)
```bash
# Use environment defaults
make import.local                                        # → http://localhost:8080
make import.dev                                          # → https://dev-id.getbodhi.app
make import.prod                                         # → https://id.getbodhi.app

# Override URL
make import.dev KEYCLOAK_URL=http://localhost:8080       # Override dev to use local

# Custom credentials
make import.prod KEYCLOAK_USER=admin KEYCLOAK_PASSWORD=secret123
```

### Option 2: Direct Python Script
```bash
# Use environment defaults
python3 realm-import-files/import_realm.py local         # → http://localhost:8080
python3 realm-import-files/import_realm.py dev           # → https://dev-id.getbodhi.app

# Override parameters
python3 realm-import-files/import_realm.py dev --url http://localhost:8080
python3 realm-import-files/import_realm.py prod --user admin --password secret123
```

### Option 3: Manual Multi-File Import
```bash
java -jar tools/keycloak-config-cli-26.1.0.jar \
  --import.files.locations=realm-import-files/common.json,realm-import-files/test-env.json \
  --keycloak.url=http://localhost:8080 \
  --keycloak.user=admin \
  --keycloak.password=admin
```

## Testing Infrastructure

### Docker-based Testing
```bash
# Start ephemeral Keycloak server
docker run --rm -d -p 8080:8080 \
  -e KC_BOOTSTRAP_ADMIN_USERNAME=admin \
  -e KC_BOOTSTRAP_ADMIN_PASSWORD=admin \
  -e KC_HOSTNAME=http://localhost:8080 \
  --name test-keycloak \
  ghcr.io/bodhisearch/bodhi-auth-testcontainer:latest

# Test multi-file import
sleep 15
make import.local

# Cleanup
docker stop test-keycloak
```

### Verification Process
1. **Single file import test**: ✅ Verified working
2. **Multi-file import test**: ✅ Verified working
3. **Environment-specific settings**: ✅ Verified different client configurations
4. **Import script automation**: ✅ Verified all environment scripts

## Tool Capabilities

### keycloak-config-cli v6.4.0 Features
- **Multi-file import**: Comma-separated file paths support ✅
- **Sequential processing**: Files processed in specified order
- **Remote state management**: Tracks managed resources with `--import.remote-state.enabled=true`
- **Environment variable substitution**: Supports `${ENV_VAR}` syntax
- **Validation**: Can validate configurations before import
- **Exit codes**: Proper success/failure reporting for CI/CD integration

### Parameter Evolution
- **Current**: `--import.files.locations` (supports multiple files)
- **Deprecated**: `--import.path` (single file only, removed in recent versions)

## Key Advantages

### Over Previous Approach
1. **Reduced duplication**: Common elements defined once in `common.json`
2. **HTTPyac-like pattern**: Similar environment structure and usage
3. **Maintainable**: Changes to shared configuration require single file update
4. **Environment-specific**: Tailored client configurations per environment

### Integration Benefits
1. **Automated scripts**: Shell scripts for easy deployment
2. **CI/CD ready**: Proper exit codes and error handling
3. **Flexible usage**: Multiple import methods supported
4. **Docker compatible**: Works with containerized Keycloak instances

## Implementation Details

### Common Configuration (`common.json`)
Contains shared realm elements:
- Client scopes: `roles`, `scope_token_user`, `scope_token_power_user`, `scope_user_user`, `scope_user_power_user`
- Basic realm settings: `enabled`, `registrationEmailAsUsername`, `rememberMe`, `loginWithEmailAllowed`
- Default scope mappings and client scope assignments

### Environment-Specific Configuration (`{env}-env.json`)
Contains environment-tailored elements:
- `client-bodhi-dev-console` with environment-specific capabilities
- `account` client (disabled)
- Environment settings: `registrationAllowed`, `verifyEmail`

### Import Scripts
Each environment has a dedicated script:
- Automatic path resolution using `$SCRIPT_DIR`
- Default parameter values for common scenarios
- Success/failure reporting with appropriate exit codes
- Consistent user experience across environments

## Deployment Integration

### CI/CD Pipeline Integration
```bash
# Example deployment script
make import.${ENVIRONMENT} KEYCLOAK_USER=${KEYCLOAK_ADMIN_USER} KEYCLOAK_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD}
```

### GitHub Actions Integration
```yaml
- name: Import Keycloak Configuration
  run: |
    make import.${{ matrix.environment }} \
      KEYCLOAK_USER=${{ secrets.KEYCLOAK_ADMIN_USER }} \
      KEYCLOAK_PASSWORD=${{ secrets.KEYCLOAK_ADMIN_PASSWORD }}
```

### Makefile Integration
The system includes built-in Makefile targets:
```makefile
# Available targets (already integrated)
make import.local     # → http://localhost:8080
make import.test      # → https://test-id.getbodhi.app  
make import.dev       # → https://dev-id.getbodhi.app
make import.main      # → https://main-id.getbodhi.app
make import.prod      # → https://id.getbodhi.app
```

## Best Practices

### Configuration Management
1. **Use multi-file approach**: Leverage common.json + environment-specific files
2. **Order matters**: Always import common.json before environment-specific files
3. **Validate before import**: Test configurations in development environment
4. **Version control**: Track all configuration changes
5. **Environment isolation**: Use environment-specific credentials and URLs

### Security Considerations
1. **Secure credentials**: Use environment variables or secure vaults for admin credentials
2. **Client secrets**: Change default "change-me" secrets in production
3. **Access control**: Restrict access to import tools and configurations
4. **Audit trail**: Log all realm import operations

## Related Files

### Primary Implementation Files
- `tools/keycloak-config-cli-26.1.0.jar` - Import tool (v6.4.0)
- `httpyac-scripts/realm-import-files/common.json` - Shared configuration
- `httpyac-scripts/realm-import-files/{env}-env.json` - Environment-specific configurations
- `httpyac-scripts/realm-import-files/import-{env}.sh` - Import automation scripts
- `httpyac-scripts/realm-import-files/README.md` - Usage documentation

### Related Systems
- HTTPyac environment system (`httpyac-scripts/env/`) - Similar pattern for API testing
- Keycloak SPI extension (`src/main/java/com/bodhisearch/`) - Runtime realm customization
- Container deployment system - Integration point for automated realm setup

### Associated Files (tracked in ai-docs/03-context/README.md)
- `realm-import-files/` - Realm import configuration files and Python automation script
- `tools/keycloak-config-cli-26.1.0.jar` - Import tool binary
- `Makefile` - Contains import.{env} targets for easy realm import

## Future Enhancements

### Potential Improvements
1. **Environment variable templates**: Add support for environment-specific variable substitution
2. **Validation scripts**: Pre-import configuration validation
3. **Rollback capability**: Realm configuration backup and restore
4. **Integration testing**: Automated testing of imported configurations
5. **Template expansion**: Additional environment-specific customization options 