# Context Documentation

This folder contains foundational documentation covering project architecture, patterns, and development conventions for the Keycloak Bodhi Extension.

## Documentation Structure

### Core Architecture
- **[01-project-architecture.md](01-project-architecture.md)** - High-level system architecture, component relationships, and design principles
- **[02-domain-model-token-exchange.md](02-domain-model-token-exchange.md)** - Detailed domain model covering multi-tenant OAuth2 architecture, client types, role systems, and token exchange flows
- **[05-system-overview.md](05-system-overview.md)** - Comprehensive system overview covering the unique multi-tenant marketplace OAuth2 architecture with dynamic audience management

### Development Patterns
- **[03-test-framework-patterns.md](03-test-framework-patterns.md)** - Testing patterns, frameworks, and best practices for integration testing, UI testing, and test data management
- **[04-development-conventions-api.md](04-development-conventions-api.md)** - API design patterns, request/response models, error handling, and development conventions
- **[httpyac-conventions.md](httpyac-conventions.md)** - HTTPyac testing conventions, environment management, script organization, and testing patterns for OAuth2 flows

### Security & Build
- **[06-obfuscation-guide.md](06-obfuscation-guide.md)** - Java code obfuscation implementation using ProGuard for protecting intellectual property from reverse engineering in production Docker images
- **[07-release-process.md](07-release-process.md)** - Comprehensive release process documentation including multi-platform Docker image management, automated builds, tagging strategies, and production vs testcontainer workflows
- **[github-testcontainers.md](github-testcontainers.md)** - GitHub Actions workflow for building multi-platform Docker images with dynamic tagging strategy for integration testing

## Key Concepts

### Multi-Tenant OAuth2 Architecture
The system implements a unique multi-tenant marketplace OAuth2 token exchange system with:
- **App/Frontend Clients**: Public clients (no client secrets) with simplified admin-only role structure
- **Resource Server Clients**: Confidential clients with full 4-level role hierarchy and standard token exchange v2 support
- **Dynamic Audience Management**: Scope-based audience resolution for M×N app-to-resource connections

### Token Exchange V2
The system uses Keycloak's standard token exchange v2 (RFC 8693) with:
- **Standard Configuration**: Uses `standard.token.exchange.enabled=true` attribute
- **Dynamic Scope Resolution**: Client scopes automatically resolve to proper audiences
- **No Custom Policies**: Eliminates need for custom AudienceMatchPolicy implementations
- **Production Ready**: Uses stable Keycloak features instead of preview functionality

### Security & Compliance
- **RFC 8693 Compliance**: Full OAuth 2.0 Token Exchange specification compliance
- **Scope-Based Access Control**: Granular consent-based access control with resource-specific scopes
- **Multi-Tenant Isolation**: Client-specific groups and roles with proper isolation
- **Audit Trail**: Complete OAuth2 flow logging and consent tracking

## Getting Started

### New to the Project?
1. Start with [System Overview](05-system-overview.md) for a comprehensive understanding
2. Read [Project Architecture](01-project-architecture.md) for technical details
3. Review [Domain Model & Token Exchange](02-domain-model-token-exchange.md) for implementation patterns

### Understanding Implementation?
1. Check [Development Conventions - API](04-development-conventions-api.md) for API patterns
2. Review [Test Framework Patterns](03-test-framework-patterns.md) for testing approaches
3. See [GitHub Testcontainers](github-testcontainers.md) for CI/CD and Docker build processes

## File Tracking

This section tracks the files associated with each context document to facilitate updates when implementations change.

### GitHub Testcontainers Context
**Document**: [github-testcontainers.md](github-testcontainers.md)
**Associated Files**:
- `.github/workflows/testcontainer.yml` - Main workflow definition
- `Makefile` (ci.* targets) - CI build targets
- `Dockerfile.testcontainer` - Multi-stage Docker build
- `proguard-unified.pro` - ProGuard configuration
- `obfuscation-dictionary.txt` - Obfuscation dictionary

### Obfuscation Guide Context
**Document**: [06-obfuscation-guide.md](06-obfuscation-guide.md)
**Associated Files**:
- `proguard-unified.pro` - ProGuard configuration
- `obfuscation-dictionary.txt` - Custom obfuscation dictionary
- `Dockerfile.testcontainer` - Docker build with ProGuard
- `pom.production.xml` - Production Maven configuration

### Railway Deployment Context
**Document**: [08-railway-deployment.md](08-railway-deployment.md)
**Associated Files**:
- `railway.toml` - Railway deployment configuration
- `SETUP.md` - GitHub token setup and authentication instructions
- `.github/workflows/release.yml` - Production image builds
- `Dockerfile` - Production Docker image configuration

### Release Process Context
**Document**: [07-release-process.md](07-release-process.md)
**Associated Files**:
- `.github/workflows/release.yml` - Production release workflow
- `.github/workflows/testcontainer.yml` - Testcontainer build workflow
- `Makefile` (ci.build-release, ci.push-release targets) - Production CI targets
- `Dockerfile` - Production Docker build configuration
- `Dockerfile.testcontainer` - Testcontainer Docker build configuration
3. Consult [HTTPyac Conventions](httpyac-conventions.md) for testing scripts
4. See [Obfuscation Guide](06-obfuscation-guide.md) for code protection and secure builds
5. Review [Release Process](07-release-process.md) for Docker image management and release workflows

## Document Maintenance

These context documents are **living documents** that reflect current development patterns and project architecture. They should be updated when:
- Patterns, conventions, or architecture change
- New development practices are adopted
- Testing frameworks or approaches evolve
- API design patterns are modified

## Related Documentation

- **Features**: [../01-features/](../01-features/) - Feature specifications and implementation guides
- **Research**: [../02-research/](../02-research/) - Technical research and architectural investigations
- **Main Index**: [../README.md](../README.md) - Complete documentation navigation

---

*This context documentation provides foundational knowledge for understanding and contributing to the Keycloak Bodhi Extension project.* 