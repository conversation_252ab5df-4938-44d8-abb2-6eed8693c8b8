# Development Conventions and API Documentation

## BodhiResourceProvider API Endpoints

### Base URL Pattern
```
{keycloak-url}/realms/{realm}/bodhi/
```

### 1. Client Registration Endpoint

#### `POST /clients`
**Purpose**: Register new OAuth resource server clients with automatic setup

**Request Body**:
```json
{
  "redirect_uris": ["http://bodhiapp.localhost/app/callback"]
}
```

**Response** (201 Created):
```json
{
  "client_id": "resource-abcd1234",
  "client_secret": "generated-secret",
  "public_key": "base64-encoded-public-key",
  "kid": "key-identifier", 
  "alg": "RS256"
}
```

**Automatic Setup Performed**:
1. Creates confidential client with service account enabled
2. Generates four client roles (resource_user, resource_power_user, resource_manager, resource_admin)
3. Creates group structure (users-{client-id} with subgroups)
4. Configures token exchange permissions
5. Sets up authorization policies

**Environment-Specific Behavior**:
- **Test Environment** (`APP_ENV=test`): Direct access grants enabled
- **Live Test** (`?live_test=true`): Client ID prefixed with "test-resource-"
- **Production**: Standard "resource-" prefix

### 2. App Client Registration Endpoint

#### `POST /apps`
**Purpose**: Register new OAuth app clients (public clients for frontend applications)

**Authentication**: Bearer token (user token from client-bodhi-dev-console required)

**Request Body**:
```json
{
  "name": "My Application",
  "description": "A frontend application description",
  "redirect_uris": [
    "http://localhost:3000/callback",
    "https://myapp.example.com/auth/callback"
  ]
}
```

**Response** (201 Created):
```json
{
  "client_id": "app-********-abcd-efgh-ijkl-************"
}
```

**Automatic Setup Performed**:
1. Creates public client (no client secret)
2. Creates single admin client role
3. Creates simplified group structure (users-{client-id}/admins)
4. Automatically assigns requesting user to admins group
5. Configures standard OAuth flow for frontend applications

**Client Configuration**:
- **Client Type**: Public (no client secret)
- **Protocol**: `openid-connect`
- **Standard Flow**: Enabled (Authorization Code flow)
- **Direct Access Grants**: Disabled (password flow not allowed)
- **Service Accounts**: Disabled (no machine-to-machine authentication)
- **Consent Required**: Disabled
- **Full Scope Allowed**: Disabled
- **Web Origins**: `+` (allow all origins for the configured redirect URIs)

**Environment-Specific Behavior**:
- **Test Environment** (`APP_ENV=test`): Client ID prefixed with "test-app-"
- **Live Test** (`?live_test=true`): Client ID prefixed with "test-app-"
- **Production**: Standard "app-" prefix

**Authentication Requirements**:
- Requires valid user access token issued by `client-bodhi-dev-console`
- Service account tokens are rejected
- Token must be from enabled client

### 3. Admin Assignment Endpoint

#### `POST /clients/make-resource-admin`
**Purpose**: Assign admin role to a user for the requesting client

**Authentication**: Bearer token (service account token required)

**Request Body**:
```json
{
  "user_email": "<EMAIL>"
}
```

**Response** (201 Created):
```json
{
  "message": "User added to admin group successfully"
}
```

**Validation**:
- Validates service account token from Authorization header
- Extracts client ID from token's `iss_for` claim
- Ensures target user exists in realm
- Adds user to `users-{client-id}/admins` group

### 4. Group Management Endpoint

#### `POST /clients/add-user-to-group`
**Purpose**: Add user to specific group within client scope

**Authentication**: Bearer token (service account token required)

**Request Body**:
```json
{
  "user_email": "<EMAIL>",
  "group": "managers"  // users, power-users, managers, admins
}
```

**Response** (201 Created):
```json
{
  "message": "User added to group successfully"
}
```

**Group Validation**:
- Accepts: "users", "power-users", "managers", "admins"
- Automatically maps to full group path: `/users-{client-id}/{group}`
- Validates group exists before assignment

### 5. Admin Check Endpoint

#### `GET /clients/has-resource-admin`
**Purpose**: Check if the requesting client has any admin users

**Authentication**: Bearer token (service account token required)

**Response** (200 OK):
```json
{
  "has_admin": true
}
```

**Logic**:
- Extracts client ID from service account token
- Checks if `users-{client-id}/admins` group has any members
- Returns boolean result

## Development Conventions

### 1. Naming Conventions

#### Client Naming
- **Resource Servers**: `resource-{identifier}` (e.g., `resource-abcd`)
- **Test Resource Servers**: `test-resource-{identifier}` (when live_test=true)
- **App Clients**: `app-{identifier}` (e.g., `app-********-abcd-efgh-ijkl-************`)
- **Test App Clients**: `test-app-{identifier}` (when APP_ENV=test or live_test=true)

#### Group Naming
- **Pattern**: `users-{client-id}`
- **Resource Client Subgroups**: `users`, `power-users`, `managers`, `admins`
- **App Client Subgroups**: `admins` only
- **Full Paths**: `/users-{client-id}/{subgroup}`

#### Role Naming
- **Resource Client Roles**: `resource_user`, `resource_power_user`, `resource_manager`, `resource_admin`
- **App Client Roles**: `admin` only
- **Scope**: Client-level roles only

### 2. Error Handling Patterns

#### Standardized Error Response (`BodhiResourceProvider.java`)
**Pattern**: `ErrorResponse` class with consistent JSON structure
**Usage**: All endpoints return `{"error": "message"}` for error conditions

#### Common Error Responses
- **400 Bad Request**: `{"error": "Invalid request parameters"}`
- **401 Unauthorized**: `{"error": "invalid session"}` | `{"error": "unauthorized client"}`
- **403 Forbidden**: `{"error": "Insufficient permissions"}`
- **500 Internal Server Error**: `{"error": "Internal server error occurred"}`

#### Exception Handling Pattern
**Implementation**: Try-catch blocks in all public endpoint methods
**Logging**: Full exception details logged for debugging
**Response**: Consistent error response format with appropriate HTTP status codes

### 3. Authentication and Authorization Patterns

#### Service Account Token Validation (Resource Clients)
**Method**: `checkForServiceAccount(AuthenticationManager.AuthResult authResult)`
**Location**: `BodhiResourceProvider.java`
**Validates**:
- Token presence and validity
- Service account token requirement
- Client ID and authorized party matching
- Client existence and enablement

#### User Token Validation (App Clients)
**Method**: `checkForDevConsoleUserToken(AuthenticationManager.AuthResult authResult)`
**Location**: `BodhiResourceProvider.java`
**Validates**:
- Token presence and validity
- User token requirement (rejects service account tokens)
- Client authorization (`client-bodhi-dev-console` only)
- Client existence and enablement

### 4. Resource Management Patterns

#### App Client Creation Pattern
**Location**: `BodhiResourceProvider.registerApp()` method
**Key Steps**:
1. Generate client ID with environment-aware prefix
2. Configure public client settings (no client secret)
3. Set OAuth flow permissions (standard flow only)
4. Create admin role and group structure
5. Assign requesting user to admin group

#### Resource Client Creation Pattern
**Location**: `BodhiResourceProvider.registerClient()` method
**Key Steps**:
1. Generate client ID and secret
2. Configure confidential client with service account
3. Create four-tier role hierarchy
4. Create group structure with role mappings
5. Set up token exchange permissions

#### Role Creation Pattern
**App Clients**: Single `admin` role
**Resource Clients**: Four-tier hierarchy (`resource_user` → `resource_power_user` → `resource_manager` → `resource_admin`)

#### Group Creation Pattern
**App Clients**: `users-{client-id}/admins` (simplified structure)
**Resource Clients**: `users-{client-id}/{users|power-users|managers|admins}` (full hierarchy)
**Role Assignment**: Cumulative role inheritance (higher groups include lower group roles)

### 5. Testing Conventions

#### Test Method Naming
**Pattern**: `test{Action}{Condition}` (e.g., `testTokenExchangeWithValidAudience`)
**Organization**: Group tests by category with comment separators

#### Test Data Creation
**Helper Methods**: Use consistent helper methods for test data
- `registerClientAndReturnClientPair()` - Resource client creation
- `createUserWithRole(clientId, role)` - User creation with role assignment
- `getTokenForClient(clientPair)` - Service account token acquisition

#### Test Organization Pattern
**Structure**: Separate successful operations from error conditions
**Comments**: Use comment separators for test categories
**Consistency**: Follow same patterns across all test classes

### 6. Logging Conventions

#### Logger Declaration
**Pattern**: `private static final Logger logger = LoggerFactory.getLogger(ClassName.class);`
**Location**: Class-level declaration in all components

#### Logging Levels
**Info**: Normal operations (`logger.info("Client {} created successfully", clientId)`)
**Error**: Exceptions and failures (`logger.error("Failed to create client", exception)`)
**Debug**: Detailed troubleshooting (test environment only)

### 7. Configuration Management

#### Environment-Aware Configuration
**Environment Detection**: `System.getenv("APP_ENV")`
**Feature Toggles**: Query parameter-based (`?live_test=true`)
**Prefix Management**: Environment-specific client ID prefixes

#### Configuration Patterns
**Test Environment**: Direct access grants enabled, test prefixes
**Live Test**: Test prefixes with production-like settings
**Production**: Standard prefixes, security-focused settings

### 8. Security Best Practices

#### Token Validation
**Requirements**: Always validate token presence, format, and validity
**Headers**: Proper Authorization header format validation
**Rejection**: Clear error messages for invalid tokens

#### Input Validation
**Parameters**: Validate all input parameters before processing
**Required Fields**: Check for required field presence
**Format Validation**: Validate URI formats and other structured data

#### Permission Checks
**Authorization**: Verify permissions before operations
**Scope Validation**: Ensure token scope matches operation requirements
**Client Isolation**: Prevent cross-client unauthorized access

## Code Quality Standards

### 1. Method Organization
- Public API methods first
- Internal implementation methods second
- Helper/utility methods last
- Clear separation with comments

### 2. Exception Handling
- Catch specific exceptions when possible
- Log all exceptions with context
- Return appropriate HTTP status codes
- Provide meaningful error messages

### 3. Resource Management
- Always close resources in try-with-resources or finally blocks
- Use Keycloak session management properly
- Clean up temporary objects

### 4. Documentation
- Document all public methods with JavaDoc
- Include parameter descriptions and return value explanations
- Document complex business logic with inline comments
- Maintain up-to-date API documentation

## Implementation References

### Key Files
- **Main Implementation**: `src/main/java/com/bodhisearch/BodhiResourceProvider.java`
- **Test Coverage**: `src/test/java/com/bodhisearch/BodhiResourceProviderTest.java`
- **Integration Tests**: `src/test/java/com/bodhisearch/integration/AppRegistrationIntegrationTest.java`
- **Utilities**: `src/test/java/com/bodhisearch/util/BodhiProviderClient.java`

### Configuration Files
- **Service Registration**: `src/main/resources/META-INF/services/org.keycloak.services.resource.RealmResourceProviderFactory`
- **Realm Templates**: `src/test/resources/import-files/bodhi-realm-setup.ftl`
- **Test Configuration**: `src/test/resources/logback-test.xml`
