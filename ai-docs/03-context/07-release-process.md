# Release Process and Docker Image Management

This document describes the comprehensive release process for the Keycloak Bodhi Extension, including Docker image management, automated builds, and release workflows.

## Overview

The project implements a dual-track Docker image strategy that separates testing and production concerns through distinct image naming, build processes, and release workflows. This approach ensures clear separation between development/testing artifacts and production-ready releases.

## Docker Image Strategy

### Image Types and Naming Convention

| Image Type | Registry Path | Purpose | Dockerfile | Database | JVM Config |
|------------|---------------|---------|------------|----------|------------|
| **Production** | `ghcr.io/bodhisearch/bodhi-auth-server` | Production deployments | `Dockerfile` | PostgreSQL | Production cluster |
| **Testcontainer** | `ghcr.io/bodhisearch/bodhi-auth-testcontainer` | Integration testing | `Dockerfile.testcontainer` | In-memory H2 | Fast startup |

### Naming Rationale

- **Production**: `bodhi-auth-server` clearly identifies this as the Bodhi authentication server for production use
- **Testcontainer**: `bodhi-auth-testcontainer` clearly identifies this as the testcontainer variant for testing scenarios
- Both use the same registry (`ghcr.io`) but different image names for clear separation

### Tagging Conventions

- **Production releases**: Use `v` prefix (e.g., `v1.0.0`) to follow semantic versioning conventions
- **Testcontainer releases**: Use clean version numbers (e.g., `1.0.0`) since the image name already indicates purpose
- **Development builds**: Use branch-specific tags (`main`) or commit SHA for feature branches

## Release Workflows

### Production Release Workflow

**File**: `.github/workflows/release.yml`

**Triggers**:
- Push to `release/v*` tags (e.g., `release/v1.0.0`)
- Push to `release/v*` branches
- Manual workflow dispatch with tag input

**Process**:
1. **Version Extraction**: Parses `release/vX.Y.Z` format with validation
2. **Production Build**: Uses `Dockerfile` with PostgreSQL and clustering features
3. **Image Publishing**: Pushes to `ghcr.io/bodhisearch/bodhi-auth-server:vX.Y.Z`
4. **GitHub Release**: Creates comprehensive release with Docker image information
5. **Security Scanning**: Runs vulnerability scans (non-blocking)

**Key Features**:
- Semantic versioning enforcement
- Production-optimized JVM settings
- Cluster-ready configuration
- Comprehensive release documentation
- Error handling and validation

### Testcontainer Workflow

**File**: `.github/workflows/testcontainer.yml`

**Triggers**:
- Push to `main` branch
- Push to `release/testcontainer-v*` tags (e.g., `release/testcontainer-v1.0.0`)
- Legacy support for `release/v*` tags
- Manual workflow dispatch

**Process**:
1. **Dynamic Tagging**: Branch-based tag generation
2. **Testcontainer Build**: Uses `Dockerfile.testcontainer` with in-memory database
3. **Image Publishing**: Pushes to `ghcr.io/bodhisearch/bodhi-auth-testcontainer`
4. **Build Summary**: Generates detailed build information

**Tagging Strategy**:
- `main` branch → `main` tag
- `release/testcontainer-vX.Y.Z` → `X.Y.Z` tag

- Other branches → short SHA tag

## Makefile Integration

### Production Targets

```makefile
ci.build-release    # Build production Docker image
ci.push-release     # Push production image to registry
ci.release-info     # Display production build information
```

### Testcontainer Targets

```makefile
ci.build-testcontainer  # Build testcontainer Docker image
ci.push-testcontainer   # Push testcontainer image to registry
ci.build-info          # Display testcontainer build information
```

### Environment Variables

All CI targets require these environment variables:

| Variable | Purpose | Example |
|----------|---------|---------|
| `DOCKER_REGISTRY` | Container registry URL | `ghcr.io` |
| `IMAGE_NAME` | Image name without registry | `bodhisearch/bodhi-auth-server` |
| `GIT_SHA` | Full commit SHA | `abc1234567890abcdef...` |
| `GIT_BRANCH` | Git branch reference | `release/v1.0.0` |
| `DOCKER_TAG` | Primary Docker tag | `v1.0.0` |
| `SHORT_SHA` | 7-character commit SHA | `abc1234` |

## Release Process Workflows

### GHCR-First Version Management

The release process uses **GitHub Container Registry (GHCR) as the single source of truth** for determining the next version to release. This ensures consistency and eliminates dependencies on local git state.

#### Version Detection Logic

```bash
# Check current published versions
make check-latest-versions
# Queries GHCR API for both production and testcontainer packages
# Shows latest semantic versions from published Docker images

# Production package query
gh api "/orgs/{org}/packages/container/bodhi-auth-server/versions"
# → Returns published versions with tags like v1.0.0, v1.0.1, etc.
# → If 404 "Package not found": no package exists yet

# Testcontainer package query  
gh api "/orgs/{org}/packages/container/bodhi-auth-testcontainer/versions"
# → Returns published versions with tags like 1.0.0, 1.0.1, etc.
# → If 404 "Package not found": no package exists yet
```

### Creating a Production Release

1. **Automated Version Detection**:
   ```bash
   # Automatically determines next version from GHCR
   make release-server
   ```

2. **Version Logic**:
   - **First Release**: If no `bodhi-auth-server` package exists in GHCR → starts with `v0.0.1`
   - **Subsequent Releases**: Finds latest `vX.Y.Z` tag in GHCR → increments to `vX.Y.(Z+1)`
   - **Tag Creation**: Creates `release/vX.Y.Z` git tag and pushes to trigger workflow

3. **Automated Process**:
   - GitHub Actions builds production Docker image with multi-platform support
   - Image pushed to `ghcr.io/bodhisearch/bodhi-auth-server:vX.Y.Z`
   - GitHub release created with comprehensive documentation
   - Security scanning performed

4. **Verification**:
   ```bash
   # Verify image availability (may take a few minutes for workflow to complete)
   docker pull ghcr.io/bodhisearch/bodhi-auth-server:v0.0.1
   
   # Check GitHub release page for documentation
   # Review security scan results
   ```

### Creating a Testcontainer Release

1. **Automated Version Detection**:
   ```bash
   # Automatically determines next version from GHCR
   make release-testcontainer
   ```

2. **Version Logic**:
   - **First Release**: If no `bodhi-auth-testcontainer` package exists in GHCR → starts with `0.0.1`
   - **Subsequent Releases**: Finds latest `X.Y.Z` tag in GHCR → increments to `X.Y.(Z+1)`
   - **Tag Creation**: Creates `release/testcontainer-vX.Y.Z` git tag and pushes to trigger workflow

3. **Automated Process**:
   - GitHub Actions builds testcontainer Docker image with multi-platform support
   - Image pushed to `ghcr.io/bodhisearch/bodhi-auth-testcontainer:X.Y.Z`
   - Build summary generated

### Manual Release Triggers

Both workflows support manual triggering through GitHub Actions UI:

1. **Production Release**:
   - Go to Actions → Production Release Builds
   - Click "Run workflow"
   - Enter release tag (e.g., `release/v1.0.0`)

2. **Testcontainer Build**:
   - Go to Actions → Testcontainer Builds
   - Click "Run workflow"
   - Enter branch name to build

## Image Configuration Differences

### Production Image (`Dockerfile`)

**Optimizations**:
- PostgreSQL database support
- Production cluster configuration
- Advanced JVM settings for high-throughput workloads
- Metrics and health endpoints enabled
- Security-focused runtime configuration

**JVM Settings**:
```bash
# Aggressive heap allocation for production
JAVA_OPTS_KC_HEAP="-XX:InitialRAMPercentage=50 -XX:MaxRAMPercentage=80"

# Production cluster optimizations
JAVA_OPTS_APPEND="-server -XX:+UseG1GC -XX:MaxGCPauseMillis=200 ..."
```

**Runtime Configuration**:
- Hostname flexibility for load balancers
- Proxy headers support
- Cache stack for Kubernetes clustering
- WARN log level for production

### Testcontainer Image (`Dockerfile.testcontainer`)

**Optimizations**:
- In-memory H2 database for fast startup
- Development-friendly configuration
- Reduced memory footprint
- Simplified JVM settings

**JVM Settings**:
```bash
# Balanced memory for good performance
JAVA_OPTS_KC_HEAP="-XX:InitialRAMPercentage=50 -XX:MaxRAMPercentage=70"

# Fast startup optimizations
JAVA_OPTS_APPEND="-server -XX:+UseG1GC -XX:+UseStringDeduplication ..."
```

**Runtime Configuration**:
- Localhost hostname for testing
- HTTP enabled for simplicity
- INFO log level for debugging
- Development environment settings

## Security and Compliance

### Security Scanning

Both workflows integrate security scanning:

- **Trivy**: Vulnerability scanning for HIGH and CRITICAL issues
- **Docker Scout**: Additional security analysis when available
- **Non-blocking**: Scans continue on error to prevent build failures
- **Reporting**: Results available in workflow summaries

### Access Control

- **GitHub Container Registry**: Uses `GITHUB_TOKEN` for authentication
- **Permissions**: Workflows have `contents: read, packages: write`
- **Private Registry**: Images are private by default, requiring authentication

### Build Security

- **Multi-stage Builds**: Minimize attack surface by excluding build tools
- **Non-root User**: Production image runs as non-root user (UID 1000)
- **Minimal Base**: Uses official Keycloak base images
- **Build Metadata**: Includes commit SHA and build date for traceability

## Monitoring and Troubleshooting

### Build Monitoring

**Success Indicators**:
- GitHub Actions workflow completion
- Docker images available in registry
- GitHub releases created (production only)
- Security scans pass or have acceptable findings

**Failure Points**:
- Tag format validation errors
- Docker build failures
- Registry push failures
- Security scan critical findings

### Troubleshooting Guide

1. **Tag Format Errors**:
   ```
   Error: Invalid release tag format. Expected: release/vX.Y.Z
   ```
   - Ensure tags follow `release/v1.0.0` format
   - Check for typos in version numbers

2. **Docker Build Failures**:
   - Check Dockerfile syntax
   - Verify base image availability
   - Review Maven build logs

3. **Registry Push Failures**:
   - Verify GITHUB_TOKEN permissions
   - Check registry authentication
   - Ensure sufficient storage quota

4. **Environment Variable Issues**:
   - Verify all required variables are set
   - Check Makefile target requirements
   - Review workflow environment configuration

### Debugging Commands

```bash
# Local build testing
make ci.build-release DOCKER_REGISTRY=ghcr.io IMAGE_NAME=test/bodhi-auth-server GIT_SHA=$(git rev-parse HEAD) DOCKER_TAG=test SHORT_SHA=$(git rev-parse --short HEAD) GIT_BRANCH=$(git branch --show-current)

# Local image testing
docker run --rm -p 8080:8080 ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0

# Registry inspection
docker inspect ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0
```

## Integration with Development Workflow

### Branch Strategy

The release process integrates with a GitFlow-inspired branching strategy:

- **main**: Stable development branch, triggers testcontainer builds
- **release/v***: Production release branches/tags
- **release/testcontainer-v***: Testcontainer-specific releases
- **feature/***: Development branches, trigger SHA-tagged builds

### Continuous Integration

- **Pull Requests**: Trigger standard build and test workflows
- **Main Branch**: Triggers testcontainer builds for integration testing
- **Release Tags**: Trigger production or testcontainer builds based on naming

### Development Testing

```bash
# Test with latest main build
docker pull ghcr.io/bodhisearch/bodhi-auth-testcontainer:main

# Test with specific SHA
docker pull ghcr.io/bodhisearch/bodhi-auth-testcontainer:abc1234

# Test production release
docker pull ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0
```

## Multi-Platform Build Architecture

### Supported Platforms

Both production and testcontainer images support multi-platform builds:

- **linux/amd64**: Intel/AMD 64-bit (x86_64) - Traditional server architecture
- **linux/arm64**: ARM 64-bit (aarch64) - Apple Silicon, AWS Graviton, etc.

### Build Process

Multi-platform builds use Docker Buildx with an optimized cross-compilation strategy:

```dockerfile
# Build stage uses host platform for faster compilation
FROM --platform=$BUILDPLATFORM openjdk:21-jdk-slim AS deps

# Runtime stage creates platform-specific images
FROM quay.io/keycloak/keycloak:26.2.5
# Keycloak base image supports multi-platform
```

### Platform Selection Logic

Docker automatically selects the appropriate image variant:

1. **Automatic Selection**: Docker client detects host platform and pulls matching variant
2. **Manual Override**: Use `--platform` flag to force specific architecture
3. **Manifest Inspection**: Single image reference contains platform-specific variants

```bash
# Automatic platform selection
docker pull ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0

# Manual platform selection
docker pull --platform linux/amd64 ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0
docker pull --platform linux/arm64 ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0

# Inspect available platforms
docker manifest inspect ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0
```

### Build Optimization

Multi-platform builds are optimized for performance:

1. **Cross-Compilation**: Build tools run on host platform (`$BUILDPLATFORM`)
2. **Parallel Builds**: Platform variants built simultaneously
3. **Shared Layers**: Common layers cached across platforms
4. **Integrated Push**: All variants pushed atomically with `--push` flag

### Error Resolution

**Platform Mismatch Warning**:
```
WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8)
```
- **Cause**: Pulling image built for different architecture
- **Impact**: Container runs through emulation (slower performance)
- **Solution**: Use multi-platform images or specify `--platform` flag

**Port Conflict Error**:
```
docker: Error response from daemon: driver failed programming external connectivity on endpoint ... Bind for 0.0.0.0:8080 failed: port is already allocated
```
- **Cause**: Another container using the same port
- **Solution**: Stop conflicting container or use different port mapping

## Future Enhancements

### Planned Improvements

1. **Advanced Security**: Enhanced vulnerability scanning with blocking thresholds
2. **Deployment Integration**: Automated deployment triggers for production releases
3. **Release Analytics**: Build time tracking and optimization metrics
4. **Automated Changelog**: Generate release notes from commit history
5. **Additional Platforms**: Support for more architectures (linux/arm/v7, etc.) as needed

### Configuration Extensibility

The current system is designed for extensibility:

- **Additional Triggers**: Easy to add new tag patterns or branch triggers
- **Custom Validation**: Extensible tag format validation
- **Build Customization**: Flexible Makefile targets for different build types
- **Registry Support**: Configurable for multiple container registries

---

**Note**: This release process provides enterprise-grade automation while maintaining clear separation between testing and production concerns. The dual-image strategy ensures optimal configurations for each use case while providing comprehensive automation and monitoring capabilities. 