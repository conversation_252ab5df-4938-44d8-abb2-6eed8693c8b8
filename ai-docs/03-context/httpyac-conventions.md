# HTTPBook Scripts Conventions

## Purpose and Goals

HTTPBook scripts in this project serve as **developer-focused debugging tools** for the Keycloak Bodhi Extension APIs. They are designed for:

- Quick API testing and debugging during development
- Manual response inspection in VS Code
- Multi-step API workflows for issue identification
- Developer-friendly environment variable management
- Rapid iteration on API endpoints without writing full tests

**NOT intended for:** Comprehensive testing, automated validation, or production use.

## Core Conventions

### Environment Management

#### HTTPyac Environment System
Following the [official HTTPyac documentation](https://httpyac.github.io/guide/environments.html), environments are managed using the dotenv system with organized file structure:

**Environment File Structure:**
```
httpyac-scripts/
├── env/
│   ├── .env          # Global variables (shared across all environments)
│   ├── local.env     # Local development overrides
│   ├── dev.env       # Development environment overrides
│   ├── main.env      # Main environment overrides
│   ├── test.env      # Test environment overrides
│   └── prod.env      # Production environment overrides
└── *.http            # HTTPyac script files
```

**Environment Usage:**
```bash
# Default environment (local)
httpyac send httpyac-scripts/app-clients.http --all

# Specific environments
httpyac send httpyac-scripts/app-clients.http --env dev --all
httpyac send httpyac-scripts/token-exchange.http --env main --all
httpyac send httpyac-scripts/resource-clients.http --env prod --all
```

**Environment Variable Priority:**
1. **Environment-specific files** (`env/{environment}.env`) - highest priority
2. **Global variables** (`env/.env`) - medium priority  
3. **Fallback values** (in common.http) - lowest priority

**Environment Configuration Rules:**
- Global variables go in `env/.env` (shared across all environments)
- Environment-specific overrides go in `env/{environment}.env`
- Use standardized credentials: `@email.com` format with `pass` password
- Keep environment files minimal - only include what differs per environment
- Test all environments using `./test-environments.sh`

### Variable Management and Scoping

#### HTTPyac Variable Scoping Rules
HTTPyac has specific variable scoping rules that must be followed:

1. **Environment Variables** (from `provideVariables` hook) - highest priority
2. **File Global Variables** (defined in regions without names using `###`) - medium priority
3. **Request Variables** (defined in named regions) - lowest priority

#### Global Variable Region Pattern
**Always start scripts with a global variable region:**
```http
###
# Global Variables and Configuration
@keycloak={{$dotenv KEYCLOAK_URL}}
@realm={{$dotenv REALM}}
@provider_id={{$dotenv PROVIDER_ID}}

# Fallback values for development
@keycloak=http://localhost:8080
@realm=bodhi
@provider_id=bodhi
```

**Rules:**
- Use `###` without a name for global variable regions
- Always provide fallback values for all environment variables
- Use descriptive variable names matching `.env` file structure
- Group related variables together (auth, users, clients, etc.)
- Include comments explaining variable purpose

#### Variable Import Strategy
**Import works with `#` prefix:** `# @import ./common.http`
- **Use `# @import ./common.http`** - the `#` prefix is required
- **DO NOT use `@import common.http`** without `#` - it doesn't work
- Import common variables from shared files
- Include only variables actually used in that script
- Maintain consistency across scripts for shared variables
- Use global variable regions (`###` without names) for shared variables

#### Dotenv Pattern
**NO FALLBACK VALUES: All variables must be explicitly defined in environment files.**

**Correct pattern (environment variables only):**
```http
@variable={{$dotenv ENV_VAR_NAME}}
```

**Environment Variable Requirements:**
- All variables MUST be defined in either `env/.env` (global) or `env/{environment}.env` (environment-specific)
- No fallback/default values in HTTP files - this prevents configuration issues being masked
- Missing environment variables will cause clear failures rather than silent fallbacks
- Use meaningful error messages when variables are undefined

**Example:**
```http
# All variables loaded from environment files (required)
@keycloak={{$dotenv KEYCLOAK_URL}}
@admin_cli_username={{$dotenv ADMIN_CLI_USERNAME}}
@admin_cli_password={{$dotenv ADMIN_CLI_PASSWORD}}
@realm={{$dotenv REALM}}
@provider_id={{$dotenv PROVIDER_ID}}
```

**Environment File Structure:**
```
env/
├── .env              # Global variables (shared across all environments)
├── local.env         # Local development overrides
├── dev.env           # Development environment
├── main.env          # Main/integration environment  
├── test.env          # Test environment
└── prod.env          # Production environment
```

**Benefits of No-Fallback Approach:**
- **Explicit Configuration**: All environment-specific values are clearly visible in environment files
- **Fail Fast**: Missing configuration causes immediate, clear errors rather than silent fallbacks
- **Environment Isolation**: No risk of accidentally using wrong environment values
- **Debugging**: Easy to trace which environment file provides each variable
- **Security**: Prevents accidentally using default/demo credentials in production

### Request Naming and Chaining

#### @name and @ref Patterns
**Use @name and @ref for request dependencies:**
```http
### Create Client
# @name create_client
POST {{bodhi_clients_endpoint}}
Content-Type: application/json

{
  "redirect_uris": ["{{redirect_uri}}"]
}

### Get Service Token
# @name get_service_token
# @ref create_client
POST {{token_endpoint}}
Authorization: Basic {{$global.client_id}}:{{$global.client_secret}}
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials
```

**Rules:**
- Use `# @name request_name` for requests that other requests depend on
- Use `# @ref dependency_name` to declare dependencies
- Use snake_case for request names
- Chain requests logically to build workflows

### Response Handling with @response Hooks

#### @response Hook Pattern
**Use @response hooks for response processing:**
```http
POST {{api_endpoint}}
Content-Type: application/json

{
  "data": "value"
}

{{@response
  if (response.statusCode === 201) {
    const credentials = extractClientCredentials(response);
    $global.client_id = credentials.client_id;
    $global.client_secret = credentials.client_secret;
  } else {
    handleError("Operation failed", response);
  }
}}
```

**Rules:**
- Use `{{@response` and `}}` to wrap response handling code
- **Use `response.statusCode` (not `response.status`)** for HTTP status codes
- **Use `response.parsedBody || response.body`** for response data
- Store extracted values in `$global` object for cross-request access
- **Use `handleError(messagePrefix, response)` for all error handling**
- Use utility functions for common extractions (e.g., `extractClientCredentials`, `extractToken`)
- Avoid console.log for success cases - only log errors

### Global Utility Functions

#### Function Definition Pattern
**Define utility functions in global regions (all functions should be in `common.http`):**
```http
###
{{
  // Global utility functions
  function extractClientCredentials(response) {
    const body = response.parsedBody || response.body;
    if (!body || !body.client_id) {
      throw new Error(`Client credentials not found in response: ${JSON.stringify(body)}`);
    }
    return {
      client_id: body.client_id,
      client_secret: body.client_secret || null
    };
  }
  
  function extractToken(response, tokenType = 'access_token') {
    const body = response.parsedBody || response.body;
    if (!body || !body[tokenType]) {
      throw new Error(`${tokenType} not found in response: ${JSON.stringify(body)}`);
    }
    return body[tokenType];
  }
  
  function handleError(messagePrefix, response) {
    const body = response.parsedBody || response.body;
    const err = `${messagePrefix}: Status: ${response.statusCode}\n${JSON.stringify(body)}`;
    console.error(err);
    throw new Error(err);
  }
  
  // Global exports
  exports.extractClientCredentials = extractClientCredentials;
  exports.extractToken = extractToken;
  exports.handleError = handleError;
}}
```

**Rules:**
- **All utility functions should be defined in `common.http`** and imported via `# @import ./common.http`
- Define utility functions in global regions using `{{` and `}}`
- Use `exports.functionName` to make functions available across requests
- **Always include `handleError` function for consistent error handling**
- Use `response.parsedBody || response.body` for response data in utility functions
- Focus on common response extraction patterns

### Authentication Patterns

#### Keycloak v26 Format
**Always use Basic Auth format:**
```http
POST {{keycloak_url}}/realms/{{realm}}/protocol/openid-connect/token
Authorization: Basic {{client_id}}:{{client_secret}}
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials
```

**Never use deprecated form parameter format:**
```http
# WRONG - Old format
grant_type=client_credentials&client_id={{client_id}}&client_secret={{client_secret}}
```

#### HTTPyac OAuth2 Integration
**Prefer HTTPyac's built-in OAuth2 flows over manual implementations:**

**OAuth2 Authorization Code Flow:**
```http
### Get User Token via OAuth2 Authorization Code Flow
# @name get_user_token
# @ref create_client
# HTTPyac automatically handles:
# 1. Opens browser to authorization endpoint
# 2. User grants permission
# 3. Receives callback with authorization code
# 4. Exchanges code for access token
# 5. Stores token for subsequent requests
# OAuth2 variables for HTTPyac (set after client creation)
@oauth2_clientId={{$global.app_client_id}}
@oauth2_clientSecret=""
@oauth2_authorizationEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth
@oauth2_tokenEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token
@oauth2_redirectUri=http://localhost:3000/callback
@oauth2_scope=openid email profile roles scope_user_power_user
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: oauth2 authorization_code

{{@response
  if (response.statusCode === 200) {
    // Store the OAuth2 access token from HTTPyac's oauth2Session
    $global.user_token = oauth2Session?.accessToken;
    if (!$global.user_token) {
      handleError("OAuth2 access token not available", response);
    }
  } else {
    handleError("OAuth2 authorization code flow failed", response);
  }
}}
```

**OAuth2 Rules:**
- **Always use HTTPyac's built-in OAuth2 flows** instead of manual authorization code handling
- **Set OAuth2 variables using `@oauth2_*` naming convention** in request region
- **Use `oauth2Session.accessToken`** to access the token obtained by HTTPyac
- **Use `Authorization: oauth2 authorization_code`** header for authorization code flow
- **Set `oauth2_clientSecret=""` for public clients** (empty string, not undefined)
- **HTTPyac automatically handles browser opening, callback server, and token exchange**

#### Token Management
- Store tokens in `$global` object: `$global.access_token = extractToken(response)`
- Use Bearer token format: `Authorization: Bearer {{$global.access_token}}`
- For OAuth2 flows, use: `$global.user_token = oauth2Session?.accessToken`
- Include token refresh patterns where needed

### Logging and Error Handling

#### Minimal Logging Philosophy
- **Console logging ONLY for errors**
- Remove all success/info level logging
- Keep error logging with meaningful context
- Focus on actionable error information

#### Centralized Error Handling with handleError
**Always use the `handleError` utility function for consistent error handling:**
```javascript
{{@response
  if (response.statusCode === 200) {
    // Success handling
    $global.result = response.parsedBody || response.body;
  } else {
    handleError("Operation failed", response);
  }
}}
```

**The `handleError` function (defined in `common.http`):**
```javascript
function handleError(messagePrefix, response) {
  const body = response.parsedBody || response.body;
  const err = `${messagePrefix}: Status: ${response.statusCode}\n${JSON.stringify(body)}`;
  console.error(err);
  throw new Error(err);
}
```

**Rules:**
- **Always use `handleError(messagePrefix, response)` for error handling**
- Use descriptive message prefixes that identify the operation
- **Use `response.statusCode` (not `response.status`)** for HTTP status codes
- **Use `response.parsedBody || response.body`** for response data
- The `handleError` function automatically includes status code and response body
- Error messages follow the format: `"Operation failed: Status: 404\n{\"error\":\"details\"}"`
- Both logs the error and throws it to stop execution

### Script Organization

#### File Structure
```http
###
# Global Variables and Configuration
@variable={{$dotenv ENV_VAR}}
@variable=fallback_value

# Common Endpoints
@endpoint={{keycloak}}/path/to/endpoint

{{
  // Global utility functions
  function utilityFunction() { /* ... */ }
  exports.utilityFunction = utilityFunction;
}}

###
### First Operation
# @name first_operation
POST {{endpoint}}
Content-Type: application/json

{
  "data": "value"
}

{{@response
  // Response handling
}}

###
### Second Operation
# @name second_operation
# @ref first_operation
POST {{endpoint}}
Authorization: Bearer {{$global.token}}
Content-Type: application/json

{
  "data": "value"
}

{{@response
  // Response handling
}}
```

#### Method Consolidation
- **Single configurable endpoint** instead of multiple variations
- Use comments to show different parameter options
- Group related operations together
- Avoid duplication of similar endpoints

**Example:**
```http
### Add user to group
# @name add_user_to_group
# @ref get_admin_token
# Change group value: "users", "power-users", "managers", "admins"
# Change add value: true (add to group), false (remove from group)
POST {{add_to_group_endpoint}}
Authorization: Bearer {{$global.admin_token}}
Content-Type: application/json

{
  "username": "{{user_power_user}}",
  "group": "power-users",
  "add": true
}
```

### Variable Naming Conventions

#### Standard Variables
- `keycloak` - Keycloak server URL (not `keycloak_url`)
- `realm` - Keycloak realm name
- `provider_id` - Bodhi provider ID
- `client_id`, `client_secret` - Client credentials
- `access_token` - Current access token
- `user_id`, `group_id` - Resource identifiers

#### Naming Rules
- Use snake_case for all variables
- Be descriptive and unambiguous
- Include type/purpose in name (e.g., `admin_access_token`)
- Maintain consistency across all scripts
- Use `$global.variable` for cross-request variables

### Multi-Step Workflows

#### Dependency Management Using @ref
```http
### Step 1: Create Resource Client
# @name create_resource_client
POST {{bodhi_clients_endpoint}}

### Step 2: Get Service Account Token
# @name get_service_token
# @ref create_resource_client
POST {{token_endpoint}}

### Step 3: Make First Admin
# @name make_first_admin
# @ref get_service_token
POST {{make_admin_endpoint}}
```

#### Flow Documentation
- Use clear section headers with `###`
- Number steps for clarity
- Use `@ref` to declare dependencies
- Include prerequisite documentation
- Store intermediate results in `$global`

### Domain Validation Removal

#### Removed Validations
- Client ID prefix validation (`app-`, `test-app-`, etc.)
- Domain-specific business rule checks
- Extensive input validation

#### Rationale
- Scripts are for debugging, not production validation
- Developers need flexibility to test edge cases
- Business rules should be enforced in application code, not debug scripts

### Centralized common.http Pattern

#### `common.http` as the Central Hub
**All shared resources should be defined in `common.http`:**
- **Global variable definitions** - All environment variables and fallbacks
- **Shared utility functions** - `extractToken`, `extractClientCredentials`, `handleError`, etc.
- **Common endpoint definitions** - All API endpoints used across scripts
- **Reusable authentication patterns** - Token requests and validation
- **Exported functions** - All utility functions must be exported for use in other scripts

#### Usage Pattern in Other Scripts
```http
###
# Import shared variables and functions
# @import ./common.http

# Script-specific variables (if any)
@script_specific_var=value

###
### Operation Name
# @name operation_name
POST {{shared_endpoint}}
Authorization: Bearer {{$global.shared_token}}
Content-Type: application/json

{
  "data": "value"
}

{{@response
  if (response.statusCode === 200) {
    $global.result = extractToken(response);
  } else {
    handleError("Operation failed", response);
  }
}}
```

#### Benefits of Centralized Approach
- **Consistency** - All scripts use the same variables and functions
- **Maintainability** - Changes to shared logic happen in one place
- **Reusability** - Common patterns are defined once and reused
- **Error Handling** - Consistent error formatting across all scripts
- **Variable Management** - Single source of truth for environment variables

### Script-Specific Patterns

#### `common.http` (Central Hub)
- **All global variable definitions** - Environment variables and fallbacks
- **All shared utility functions** - Error handling, token extraction, etc.
- **All common endpoint definitions** - API endpoints used across scripts
- **All reusable authentication patterns** - Token requests and validation
- **All exported functions** - Functions available to other scripts

#### `resource-clients.http`
- Client CRUD operations with @name/@ref chains
- Group management workflows
- User assignment with proper error handling
- Service account testing patterns

#### `token-exchange.http`
- Token exchange workflows with audience validation
- Error scenario testing
- Token introspection patterns
- Multi-step token flows

#### `realm-management.http`
- User management with admin-cli
- Realm configuration operations
- Bulk user operations using @response hooks
- Administrative workflows

## Development Workflow

### Environment Setup
- Copy `env.example` to `.env`
- Configure all required variables
- Test basic authentication first

### Script Development
- Start with global variable region
- Define utility functions if needed
- Build operations with @name/@ref chains
- Use @response hooks for processing
- Test each step manually

### Testing Approach
- Manual execution in VS Code
- Step-by-step verification using @ref dependencies
- Response inspection in HTTPyac
- Error scenario validation

### Maintenance
- Update environment variables as needed
- Consolidate similar operations
- Remove obsolete endpoints
- Keep documentation current

## Common Patterns

### Complete Request Pattern
```http
### Operation Name
# @name operation_name
# @ref dependency_name
POST {{endpoint}}
Authorization: Bearer {{$global.token}}
Content-Type: application/json

{
  "data": "value"
}

{{@response
  if (response.statusCode === 201) {
    const body = response.parsedBody || response.body;
    $global.result_id = body.id;
  } else {
    handleError("Operation failed", response);
  }
}}
```

### Global Variable Region
```http
###
# Global Variables and Configuration
@keycloak={{$dotenv KEYCLOAK_URL}}
@keycloak=http://localhost:8080

# Common Endpoints
@token_endpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token

{{
  // Global utility functions
  function extractToken(response, tokenType = 'access_token') {
    if (!response.body || !response.body[tokenType]) {
      throw new Error(`${tokenType} not found`);
    }
    return response.body[tokenType];
  }
  
  exports.extractToken = extractToken;
}}
```

### Authentication Flow
```http
### Get Access Token
# @name get_access_token
POST {{token_endpoint}}
Authorization: Basic {{client_id}}:{{client_secret}}
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials

{{@response
  if (response.statusCode === 200) {
    $global.access_token = extractToken(response);
  } else {
    handleError("Token request failed", response);
  }
}}
```

## Anti-Patterns to Avoid

### ❌ Using @import without # prefix
```http
# DON'T DO THIS - @import without # doesn't work
@import common.http

# DO THIS - @import with # prefix works
# @import ./common.http
```

### ❌ Excessive Logging
```javascript
// DON'T DO THIS
console.log("Starting operation...");
console.log("Request sent successfully");
console.log("Response received:", response.body);
```

### ❌ Multiple Similar Endpoints
```http
// DON'T DO THIS
### Add to users group
POST .../users/{{user_id}}/groups/{{users_group_id}}

### Add to power-users group  
POST .../users/{{user_id}}/groups/{{power_users_group_id}}
```

### ❌ Old Variable Extraction Pattern
```http
// DON'T DO THIS - Old pattern
@access_token={{response.body.access_token}}

// DO THIS - New pattern with @response hooks
{{@response
  if (response.statusCode === 200) {
    $global.access_token = extractToken(response);
  }
}}
```

### ❌ Complex Validation Logic
```javascript
// DON'T DO THIS
if (response.body.clientId.startsWith("app-")) {
  // Complex validation logic
}
```

### ❌ Missing Error Handling
```javascript
// DON'T DO THIS
$global.token = response.body.access_token; // No error checking

// DO THIS
{{@response
  if (response.statusCode === 200) {
    $global.token = extractToken(response);
  } else {
    handleError("Token request failed", response);
  }
}}
```

### ❌ Manual OAuth2 Implementation
```http
// DON'T DO THIS - Manual authorization code flow
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth?response_type=code&client_id={{client_id}}...

### Manual code exchange
@auth_code=manual-code-here
POST {{token_endpoint}}
grant_type=authorization_code&code={{auth_code}}...

// DO THIS - HTTPyac built-in OAuth2 flow
@oauth2_clientId={{$global.client_id}}
@oauth2_authorizationEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth
@oauth2_tokenEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token

GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: oauth2 authorization_code
```

## Best Practices Summary

1. **Use global variable regions** - Start with `###` and no name for shared variables
2. **Import from common.http** - Use `# @import ./common.http` for shared variables and functions
3. **Use @name/@ref chains** - Build logical request dependencies
4. **Use @response hooks consistently** - Process responses with `{{@response` and `}}`
5. **Use handleError for all errors** - `handleError("Operation failed", response)` for consistent error handling
6. **Store in $global** - Use `$global.variable` for cross-request data
7. **Define utility functions in common.http** - Create reusable extraction functions with exports
8. **Use response.statusCode** - Not `response.status` for HTTP status codes
9. **Use response.parsedBody || response.body** - For response data access
10. **Minimize logging** - Only log errors (handled by `handleError`)
11. **Consolidate operations** - One configurable endpoint over many variations
12. **Document clearly** - Comments explain usage and parameter options
13. **Test incrementally** - Build and verify workflows step by step
14. **Use HTTPyac OAuth2 flows** - Prefer `Authorization: oauth2 authorization_code` over manual implementations
15. **Configure OAuth2 variables** - Use `@oauth2_*` naming convention in global regions
16. **Access OAuth2 tokens** - Use `oauth2Session.accessToken` for HTTPyac-managed tokens

These conventions ensure HTTPBook scripts leverage HTTPyac's native features properly while maintaining consistency and developer productivity. The key breakthrough was understanding HTTPyac's variable scoping rules and working around the broken `@import` functionality.

### HTTPyac Response Structure
HTTPyac uses a different response structure than expected:
- **Use `response.statusCode`** instead of `response.status`
- **Use `response.parsedBody || response.body`** for response data
- The `response.body` contains the raw response string
- The `response.parsedBody` contains the parsed JSON object
- Always check both to ensure compatibility

### Form Data Encoding
For `application/x-www-form-urlencoded` content:
- Use single-line format: `grant_type=password&client_id=admin-cli&username=user&password=pass`
- **DO NOT** use multi-line format with `&` prefixes (causes parsing errors)
- HTTPyac parses form data differently than other tools

### HTTPyac Native Features to Use
- `$global` object for cross-request variable storage
- `@name` and `@ref` for request chaining
- `@response` hooks for response processing
- `{{$dotenv VAR}}` for environment variable access
- `exports.functionName` for utility function sharing
- Global regions (`###` without names) for shared variables 