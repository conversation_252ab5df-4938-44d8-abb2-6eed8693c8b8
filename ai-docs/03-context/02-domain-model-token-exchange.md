# Domain Model and Token Exchange System

## Multi-Tenant OAuth2 Architecture

### Core Concept
The system implements a multi-tenant OAuth2 token exchange mechanism where:
- **Frontend/App Clients**: Public clients (no client secrets) that authenticate users via simplified admin-only role structure
- **Resource Server Clients**: Backend APIs that require authorized access with client secrets and comprehensive role hierarchies
- **Token Exchange**: Converts app client tokens to resource server tokens for API access using custom AudienceMatchPolicy validation

### Client Types and Relationships

#### 1. App/Frontend Clients (Public Clients)
- **Purpose**: User authentication and initial token acquisition for frontend applications  
- **Creation**: POST /apps endpoint with client-bodhi-dev-console user token
- **Configuration**: Public client, no client secret required, consent enabled
- **Flow**: Authorization Code flow with PKCE (recommended)
- **Naming Convention**: `app-{uuid}` (production) or `test-app-{uuid}` (test environment)
- **Role Structure**: Single `admin` role only
- **Group Structure**: `users-{client-id}/admins` only
- **Resource Access**: Must call /request-access to obtain resource client scopes

#### 2. Resource Server Clients (Confidential Clients)  
- **Purpose**: Backend API services requiring authorized access
- **Creation**: POST /resources endpoint (no authentication required)
- **Configuration**: Confidential client with client secret, consent disabled
- **Naming Convention**: `resource-{uuid}` (production) or `test-resource-{uuid}` (test environment)
- **Features**: Service account enabled, standard token exchange v2 enabled
- **Role Structure**: Full 4-level hierarchy (resource_user/resource_power_user/resource_manager/resource_admin)
- **Group Structure**: Complete hierarchy with all subgroups
- **Client Scope**: Associated `scope_{client-id}` with audience mapper for token exchange

### Client-Level Role System

#### App Client Role Structure (Simplified)
```
admin     (Administrative access for app client)
```

#### Resource Client Role Structure (Full Hierarchy)
```
resource_admin     (Full administrative access)
├── resource_manager    (Management operations)
│   ├── resource_power_user  (Advanced user operations)
│   │   └── resource_user    (Basic user access)
```

#### Role Definitions
- **`admin`** (App clients only): Administrative control over app client
- **`resource_user`** (Resource clients only): Basic access to client resources
- **`resource_power_user`** (Resource clients only): Enhanced user capabilities
- **`resource_manager`** (Resource clients only): Management operations within client scope
- **`resource_admin`** (Resource clients only): Full administrative control over client

**Important**: These are CLIENT-LEVEL roles, not global roles. A user can be:
- Admin on App Client A
- Power user on Resource Client B  
- No role on Resource Client C

### Group-Based Role Assignment

#### App Client Group Structure
For each app client with ID `{client-id}`:
```
users-{client-id}/                    # Top-level group
└── admins/         → admin           # Single admin group
```

#### Resource Client Group Structure
For each resource client with ID `{client-id}`:
```
users-{client-id}/                    # Top-level group
├── users/          → resource_user
├── power-users/    → resource_user + resource_power_user
├── managers/       → resource_user + resource_power_user + resource_manager
└── admins/         → resource_user + resource_power_user + resource_manager + resource_admin
```

#### Group Naming Examples
- App client `app-abcd` → Group `users-app-abcd/admins`
- Resource client `resource-wxyz` → Group `users-resource-wxyz` with full subgroup structure

#### Role Assignment Process
1. User is added to appropriate subgroup based on client type
2. Group membership automatically grants associated client roles
3. Roles are cumulative for resource clients (admins get all four roles)
4. App clients have simplified single-role assignment

## Token Exchange Flow Sequences

### 1. App Client to Resource Server Token Exchange Flow (Standard RFC 8693)
```
1. App Client → OAuth2 Authorization Code Flow → App Token (includes resource scope in audience)
2. App Client → API Request → Resource Server  
3. Resource Server → Standard Token Exchange Request → Keycloak
   - Headers: Authorization: Basic {resource-client-id}:{resource-client-secret}
   - Body: grant_type=urn:ietf:params:oauth:grant-type:token-exchange
           subject_token={app-token}
           audience={resource-server-id}
4. Keycloak → Standard Token Exchange Validation (RFC 8693)
5. Keycloak → Exchanged Token → Resource Server
6. Resource Server → API Response → App Client
```

### 2. Token Exchange Validation (Keycloak Standard V2)
```
1. Authenticate resource client using Basic auth (client_id:client_secret)
2. Validate standard.token.exchange.enabled=true on resource client
3. Validate subject token signature and claims
4. Check subject token audience includes resource client ID
5. Verify token exchange grant permissions
6. Issue new token with resource client as audience
7. Apply scope-based access control via client scopes
```

### 3. Client Creation and Admin Assignment Flows

#### App Client Creation Flow
```
1. User authenticates with client-bodhi-dev-console
2. POST /realms/{realm}/bodhi/apps with user token
3. System creates public client with admin role and consent enabled
4. System creates users-{client-id}/admins group
5. System assigns requesting user to admins group
6. User automatically becomes admin of the app client
```

#### App Client Resource Access Flow
```
1. Resource server calls POST /realms/{realm}/bodhi/resources/request-access
2. System validates resource client has standard token exchange enabled
3. System validates app client is public client
4. System adds resource client scope as optional scope to app client
5. App client can now request resource scope during OAuth2 flow
6. Token issued to app client includes resource client in audience claim
7. Resource client can exchange app token for properly scoped resource token
```

## Permission and Authorization Model

### Token Exchange Authorization (Standard V2)
- **Standard**: [RFC 8693](https://datatracker.ietf.org/doc/html/rfc8693) compliant token exchange via [Keycloak Standard Token Exchange V2](https://www.keycloak.org/securing-apps/token-exchange)
- **Configuration**: `standard.token.exchange.enabled=true` on resource clients (enabled by default in Keycloak 26.2+)
- **Authentication**: Basic auth with resource client credentials (client_id:client_secret)
- **Validation**: Keycloak's built-in token exchange validation with audience verification
- **Scope Resolution**: Dynamic audience resolution through client scopes and audience mappers

### Group-Based Access Control
- **Automatic Role Mapping**: Group membership → Client roles
- **Hierarchical Permissions**: Higher groups include lower group permissions
- **Client Isolation**: Groups are client-specific, preventing cross-client access

## Configuration Templates and Seeding

### Realm Configuration Structure
```json
{
  "clients": [
    {
      "clientId": "resource-{id}",
      "serviceAccountsEnabled": true,
      "authorizationServicesEnabled": true,
      "fullScopeAllowed": false
    }
  ],
  "roles": {
    "client": {
      "resource-{id}": [
        {"name": "resource_user"},
        {"name": "resource_power_user"},
        {"name": "resource_manager"},
        {"name": "resource_admin"}
      ]
    }
  },
  "groups": [
    {
      "name": "users-resource-{id}",
      "subGroups": [
        {"name": "users", "clientRoles": {"resource-{id}": ["resource_user"]}},
        {"name": "power-users", "clientRoles": {"resource-{id}": ["resource_user", "resource_power_user"]}},
        {"name": "managers", "clientRoles": {"resource-{id}": ["resource_user", "resource_power_user", "resource_manager"]}},
        {"name": "admins", "clientRoles": {"resource-{id}": ["resource_user", "resource_power_user", "resource_manager", "resource_admin"]}}
      ]
    }
  ]
}

#### Resource Client Creation Flow
```
1. POST /realms/{realm}/bodhi/resources (no authentication required)
2. System creates confidential client with service account
3. System creates 4-level role hierarchy  
4. System creates full group structure with role mappings
5. System enables standard token exchange v2 (standard.token.exchange.enabled=true)
6. System creates associated client scope (scope_{client-id}) with audience mapper
7. Service account tokens used for subsequent admin operations
```

## Token Claim Structure

### App Client Tokens
```json
{
  "iss": "https://keycloak.example.com/realms/bodhi",
  "aud": ["app-********-abcd-efgh-ijkl-************"],
  "sub": "user-uuid",
  "preferred_username": "<EMAIL>",
  "resource_access": {
    "app-********-abcd-efgh-ijkl-************": {
      "roles": ["admin"]
    }
  },
  "client_id": "app-********-abcd-efgh-ijkl-************"
}
```

### Resource Server Tokens (After Exchange)
```json
{
  "iss": "https://keycloak.example.com/realms/bodhi",
  "aud": ["resource-server-id"],
  "sub": "user-uuid",
  "preferred_username": "<EMAIL>",
  "resource_access": {
    "resource-server-id": {
      "roles": ["resource_user", "resource_admin"]
    }
  }
}
```

### Offline Tokens
```json
{
  "iss": "https://keycloak.example.com/realms/bodhi",
  "aud": ["client-id"],
  "sub": "user-uuid",
  "scope": "offline_access scope_token_power_user",
  "typ": "Offline"
  // Note: No resource_access claims - scope-based instead
}
```

## Security Considerations

### Token Exchange Security
- **Service Account Authentication**: Resource server service account tokens required for exchange requests
- **Audience Validation**: Custom AudienceMatchPolicy prevents unauthorized cross-client access
- **Token Signature Verification**: Ensures authenticity of all tokens involved
- **Client Type Validation**: App clients and resource clients have different security models

### Role Isolation
- **Client-Level Roles**: Prevent privilege escalation across clients
- **Simplified vs Full Hierarchy**: App clients use simplified admin-only model, resource clients use full hierarchy
- **Group-Based Assignment**: Provides audit trail and scalable management
- **Automatic Assignment**: App client creators automatically become admins

### Multi-Tenancy
- **Complete Isolation**: Different client types maintain separate permission models
- **No Shared Permissions**: No cross-client access without explicit token exchange
- **Independent Role Management**: Each client manages its own roles and groups
- **Environment Awareness**: Different prefixes and behaviors for test vs production

### Authentication Models
- **App Client Creation**: Requires user authentication via client-bodhi-dev-console
- **Resource Client Creation**: No authentication required for creation, service account tokens for admin operations
- **Token Exchange**: Requires proper service account authentication from resource server
