# Railway Deployment Configuration

This document describes the Railway deployment configuration for the Keycloak Bodhi Extension, including Docker image deployment, environment setup, and integration with GitHub Container Registry.

## Overview

Railway deploys the Keycloak Bodhi extension using pre-built Docker images from GitHub Container Registry (GHCR) instead of building from source. This approach provides faster deployments, consistency with CI/CD testing, and eliminates build failures on the Railway platform.

## Deployment Strategy

### Docker Image Deployment

Railway pulls production-ready Docker images from GitHub Container Registry (GHCR):
- **Image Source**: `ghcr.io/bodhisearch/bodhi-auth-server:latest`
- **Build Process**: No compilation on Railway - uses pre-built images
- **Authentication**: GitHub Personal Access Token with `read:packages` permission
- **Environment**: Development environment (`APP_ENV=dev`) using production image

### Service Configuration

#### Railway Service Source
Railway service is configured to deploy from Docker image rather than GitHub repository:

1. **Service Source Type**: Docker Image
2. **Image Path**: `ghcr.io/bodhisearch/bodhi-auth-server:latest`
3. **Registry Authentication**: GitHub credentials for private repository access
4. **Auto-Deploy**: Optional automatic deployment when new images are pushed

#### Authentication Setup
```bash
# Railway Environment Variables for GHCR Authentication
GITHUB_TOKEN=<github-token-with-read-packages>
```

## Environment Configuration

### Railway.toml Configuration

The `railway.toml` file configures deployment settings and environment-specific variables for multiple environments:

```toml
# Railway will deploy from Docker image configured in dashboard
# Image: ghcr.io/bodhisearch/bodhi-auth-server:latest
# This file configures deployment settings only

[deploy]
healthcheckPath = "/realms/master"
healthcheckTimeout = 600
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[environments]
prod.name = "production"
prod.plugins.postgresql.enabled = true
dev.name = "development"
dev.plugins.postgresql.enabled = true
main.name = "integration"
main.plugins.postgresql.enabled = true

[[plugins]]
name = "postgresql"

[plugins.postgresql]
version = "14"

# Common environment variables shared across all environments
[envs]
# Keycloak cache configuration
KC_CACHE="ispn"
KC_CACHE_CONFIG_FILE="/opt/keycloak/conf/cache-ispn-jdbc-ping.xml"
# Database configuration
KC_DB="postgres"
KC_DB_PASSWORD="${{Postgres.PGPASSWORD}}"
KC_DB_URL="jdbc:postgresql://${{Postgres.PGHOST}}:${{Postgres.PGPORT}}/${{Postgres.PGDATABASE}}"
KC_DB_USERNAME="${{Postgres.PGUSER}}"
# Keycloak features and health
KC_FEATURES="token-exchange"
KC_HEALTH_ENABLED="true"
# Management interface configuration
KC_HTTP_MANAGEMENT_PORT="9000"
# Hostname configuration
KC_HOSTNAME_STRICT="false"
KC_HOSTNAME_BACKCHANNEL_DYNAMIC="true"
KC_PROXY_HEADERS="xforwarded"
# Logging configuration
KC_LOG_CONSOLE_FORMAT="%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{1}] (%t) %s%e%n"
KC_LOG_CONSOLE_COLOR="false"
# Security and login failure protection
KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_MAX_FAILURES="5"
KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_MAX_WAIT="300"
KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_WAIT_INCREMENT="60"
# Bootstrap admin credentials
KC_BOOTSTRAP_ADMIN_USERNAME="bootstrap-admin"
KC_BOOTSTRAP_ADMIN_PASSWORD="<random-temp-password>"
# Transaction and connection optimizations
QUARKUS_TRANSACTION_MANAGER_ENABLE_RECOVERY="true"

# Production environment (8vCPU/16GB) - Optimized for high-throughput production workload
[environments.prod.envs]
APP_ENV="production"
# JVM settings optimized for production cluster (16GB RAM, 8 vCPU)
JAVA_OPTS="-Xms2g -Xmx12g -XX:MetaspaceSize=256M -XX:MaxMetaspaceSize=1g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4"
# Production database connection pooling
KC_DB_POOL_INITIAL_SIZE="5"
KC_DB_POOL_MAX_SIZE="50"
KC_DB_POOL_MIN_SIZE="5"
# Hostname configuration for production
KC_HOSTNAME="https://prod-id.getbodhi.app"
KC_HOSTNAME_ADMIN="https://prod-id.getbodhi.app"
# Production logging - WARN level for performance
KC_LOG_LEVEL="WARN"
# Production optimizations
QUARKUS_HTTP_LIMITS_MAX_CONNECTIONS="2000"
QUARKUS_DATASOURCE_JDBC_MAX_SIZE="50"

# Development environment (4vCPU/8GB) - Production branch for fixes
[environments.dev.envs]
APP_ENV="dev"
# JVM settings optimized for development cluster (8GB RAM, 4 vCPU)
JAVA_OPTS="-Xms1g -Xmx6g -XX:MetaspaceSize=128M -XX:MaxMetaspaceSize=512m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication -XX:ParallelGCThreads=4 -XX:ConcGCThreads=2"
# Development database connection pooling
KC_DB_POOL_INITIAL_SIZE="3"
KC_DB_POOL_MAX_SIZE="25"
KC_DB_POOL_MIN_SIZE="3"
# Hostname configuration for development
KC_HOSTNAME="https://dev-id.getbodhi.app"
KC_HOSTNAME_ADMIN="https://dev-id.getbodhi.app"
# Development logging - INFO level for debugging
KC_LOG_LEVEL="INFO"
# Development optimizations
QUARKUS_HTTP_LIMITS_MAX_CONNECTIONS="1000"
QUARKUS_DATASOURCE_JDBC_MAX_SIZE="25"

# Main environment (4vCPU/8GB) - Latest main branch for integration testing
[environments.main.envs]
APP_ENV="dev"
# JVM settings optimized for integration testing (8GB RAM, 4 vCPU)
JAVA_OPTS="-Xms1g -Xmx6g -XX:MetaspaceSize=128M -XX:MaxMetaspaceSize=512m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication -XX:ParallelGCThreads=4 -XX:ConcGCThreads=2"
# Integration testing database connection pooling
KC_DB_POOL_INITIAL_SIZE="3"
KC_DB_POOL_MAX_SIZE="25"
KC_DB_POOL_MIN_SIZE="3"
# Hostname configuration for main integration
KC_HOSTNAME="https://main-id.getbodhi.app"
KC_HOSTNAME_ADMIN="https://main-id.getbodhi.app"
# Integration testing logging - INFO level for debugging
KC_LOG_LEVEL="INFO"
# Integration testing optimizations
QUARKUS_HTTP_LIMITS_MAX_CONNECTIONS="1000"
QUARKUS_DATASOURCE_JDBC_MAX_SIZE="25"
```

**Key Points**:
- Docker image source is configured through Railway dashboard, not TOML file
- TOML file configures deployment behavior and environment-specific variables
- Multi-environment setup with optimized configurations for each environment
- Common variables shared across all environments to reduce duplication

### Multi-Environment Configuration

Railway deployment supports three distinct environments, each optimized for its specific purpose:

#### Production Environment (`prod`)
- **Purpose**: Production workload with high availability and performance
- **Resources**: 8 vCPU / 16GB RAM
- **APP_ENV**: `production`
- **Logging**: WARN level for optimal performance
- **JVM**: Aggressive heap allocation (2GB-12GB) with production GC tuning
- **Database**: Large connection pool (5-50 connections)
- **Hostname**: `https://prod-id.getbodhi.app`

#### Development Environment (`dev`)
- **Purpose**: Production branch for fixes and stable development
- **Resources**: 4 vCPU / 8GB RAM
- **APP_ENV**: `dev`
- **Logging**: INFO level for debugging
- **JVM**: Balanced memory allocation (1GB-6GB) with development-friendly settings
- **Database**: Medium connection pool (3-25 connections)
- **Hostname**: `https://dev-id.getbodhi.app`

#### Main Environment (`main`)
- **Purpose**: Latest main branch for integration testing
- **Resources**: 4 vCPU / 8GB RAM
- **APP_ENV**: `dev`
- **Logging**: INFO level for debugging integration issues
- **JVM**: Same as development environment for consistency
- **Database**: Medium connection pool (3-25 connections)
- **Hostname**: `https://main-id.getbodhi.app`

#### Common Environment Characteristics
All environments share:
- Client ID prefixes: `app-` and `resource-` (production prefixes, not `test-`)
- PostgreSQL database integration with cluster cache configuration
- Token exchange features and health endpoints enabled
- Service account authentication and user token verification

### Environment-Specific JVM Tuning

Each environment has JVM settings optimized for its resource allocation and purpose:

#### Production JVM Settings (16GB RAM)
```bash
JAVA_OPTS="-Xms2g -Xmx12g -XX:MetaspaceSize=256M -XX:MaxMetaspaceSize=1g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4"
```
- **Initial heap**: 2GB for fast startup
- **Maximum heap**: 12GB (75% of 16GB RAM)
- **Metaspace**: 256M-1GB for large-scale deployment
- **GC threads**: 8 parallel, 4 concurrent (optimized for 8 vCPU)

#### Development/Main JVM Settings (8GB RAM)
```bash
JAVA_OPTS="-Xms1g -Xmx6g -XX:MetaspaceSize=128M -XX:MaxMetaspaceSize=512m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication -XX:ParallelGCThreads=4 -XX:ConcGCThreads=2"
```
- **Initial heap**: 1GB for reasonable startup
- **Maximum heap**: 6GB (75% of 8GB RAM)
- **Metaspace**: 128M-512M for development workload
- **GC threads**: 4 parallel, 2 concurrent (optimized for 4 vCPU)

#### Keycloak Configuration
```bash
# Database configuration
KC_DB="postgres"
KC_DB_URL="jdbc:postgresql://${{Postgres-6O86.PGHOST}}:${{Postgres-6O86.PGPORT}}/${{Postgres-6O86.PGDATABASE}}"
KC_DB_USERNAME="${{Postgres-6O86.PGUSER}}"
KC_DB_PASSWORD="${{Postgres-6O86.PGPASSWORD}}"

# Connection pooling optimized for Railway
KC_DB_POOL_INITIAL_SIZE="3"
KC_DB_POOL_MAX_SIZE="20"
KC_DB_POOL_MIN_SIZE="3"

# Features and health
KC_FEATURES="token-exchange"
KC_HEALTH_ENABLED="true"
KC_HTTP_MANAGEMENT_PORT="9000"

# Hostname configuration for Railway
KC_HOSTNAME="https://main-id.getbodhi.app"
KC_HOSTNAME_ADMIN="https://main-id.getbodhi.app"
KC_HOSTNAME_STRICT="false"
KC_HOSTNAME_BACKCHANNEL_DYNAMIC="true"
KC_PROXY_HEADERS="xforwarded"

# Logging configuration
KC_LOG_LEVEL="INFO"
KC_LOG_CONSOLE_FORMAT="%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{1}] (%t) %s%e%n"
KC_LOG_CONSOLE_COLOR="false"
```

## Docker Image Integration

### Production Image Characteristics

The `ghcr.io/bodhisearch/bodhi-auth-server:latest` image provides:

**Base Configuration**:
- Keycloak 26.2.5 with Bodhi SPI extension
- PostgreSQL database support (matches Railway setup)
- Production-optimized JVM settings with memory management
- Token exchange, health endpoints, and metrics enabled

**Security Features**:
- Non-root user execution (UID 1000)
- Minimal attack surface through multi-stage builds
- Secure defaults with explicit permission grants

**Runtime Capabilities**:
- Environment-aware behavior based on `APP_ENV` variable
- Cluster-ready configuration with cache support
- Comprehensive logging and monitoring

### Image Update Process

1. **CI/CD Pipeline**: GitHub Actions builds and pushes new images to GHCR
2. **Tag Strategy**: `:latest` tag updated with each successful main branch build
3. **Railway Detection**: Railway can be configured to auto-deploy on image updates
4. **Manual Deployment**: Deploy button in Railway dashboard pulls latest image

## Deployment Process

### Automated Deployment Flow

```mermaid
graph TD
    A[GitHub Actions CI/CD] --> B[Build Docker Image]
    B --> C[Push to GHCR]
    C --> D[Railway Detects Update]
    D --> E[Pull Latest Image]
    E --> F[Start Container]
    F --> G[Health Check]
    G --> H[Service Ready]
```

1. **Image Build**: GitHub Actions builds production Docker image
2. **Registry Push**: Image pushed to `ghcr.io/bodhisearch/bodhi-auth-server:latest`
3. **Railway Pull**: Railway pulls latest image using configured authentication
4. **Environment Selection**: Railway selects appropriate environment configuration (prod/dev/main)
5. **Container Start**: Railway starts container with environment-specific variables from `railway.toml`
6. **Health Check**: Railway verifies `/realms/master` endpoint accessibility
7. **Service Ready**: Keycloak available with Bodhi SPI extension on environment-specific hostname

### Manual Deployment

- **Dashboard Deployment**: Click "Deploy" button in Railway dashboard
- **CLI Deployment**: Use Railway CLI for programmatic deployments
- **API Deployment**: Railway API for automated deployment triggers

## Benefits and Trade-offs

### Benefits

**Performance**:
- Faster deployment times (no compilation on Railway)
- Consistent startup performance with production-optimized image
- Reduced Railway resource usage during deployment

**Reliability**:
- Eliminates build failures on Railway platform
- Uses tested and validated Docker images from CI/CD
- Consistent behavior between testing and deployment environments

**Maintenance**:
- Single build process in GitHub Actions
- Easier rollbacks through tagged images
- Simplified deployment pipeline

### Trade-offs

**Dependency**:
- Requires GHCR availability for deployments
- Depends on GitHub token management and rotation
- Slight delay between CI/CD build and deployment availability

**Configuration**:
- Docker image source must be configured through Railway dashboard
- GitHub token must be managed in Railway environment variables
- Cannot use `railway.toml` for image source specification

## Troubleshooting

### Common Issues

1. **JVM Options Conflicts**:
   - **Error**: `VM option 'G1NewSizePercent' is experimental and must be enabled via -XX:+UnlockExperimentalVMOptions`
   - **Root Cause**: Railway's `JAVA_OPTS` overrides Docker image's `JAVA_OPTS_APPEND`, and experimental JVM options must be unlocked before use
   - **Solution**: Ensure `-XX:+UnlockExperimentalVMOptions` appears **first** in all `JAVA_OPTS` configurations
   - **Example**: `JAVA_OPTS="-XX:+UnlockExperimentalVMOptions -Xms2g -Xmx12g ..."`

2. **Authentication Failures**:
   - Verify GitHub token has `read:packages` permission
   - Check token expiration and rotation schedule
   - Ensure token has access to private repository

3. **Image Pull Failures**:
   - Verify image exists at specified path
   - Check Railway service authentication configuration
   - Review Railway deployment logs for specific errors

4. **Health Check Failures**:
   - Verify Keycloak startup logs for configuration errors
   - Check PostgreSQL connectivity and credentials
   - Review environment variable configuration

### Debugging Commands

```bash
# Verify image availability
docker pull ghcr.io/bodhisearch/bodhi-auth-server:latest

# Test local deployment
docker run --rm -p 8080:8080 -e APP_ENV=dev ghcr.io/bodhisearch/bodhi-auth-server:latest

# Inspect image configuration
docker inspect ghcr.io/bodhisearch/bodhi-auth-server:latest
```

## Security Considerations

### Authentication Security

- GitHub Personal Access Token stored securely in Railway environment variables
- Token scope limited to `read:packages` (minimum required permissions)
- Regular token rotation recommended for security

### Runtime Security

- Production image runs as non-root user
- Minimal base image with security-focused configuration
- Environment variables managed through Railway's secure variable system

### Network Security

- Private networking between Railway services
- Public endpoints secured through Railway's proxy infrastructure
- HTTPS termination handled by Railway platform

---

**Note**: This deployment strategy leverages Railway's Docker image deployment capabilities to provide faster, more reliable deployments while maintaining development environment characteristics and ensuring consistency with our CI/CD pipeline. 