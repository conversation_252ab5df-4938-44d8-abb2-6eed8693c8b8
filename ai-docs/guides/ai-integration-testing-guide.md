# AI Developer Guide: Keycloak Extension Integration Testing

## Overview

This guide provides AI coding assistants with comprehensive instructions for creating integration tests using the Keycloak Bodhi Extension with testcontainers. The extension implements multi-tenant OAuth2 token exchange with dynamic audience management using Keycloak's standard token exchange v2 (RFC 8693).

## Domain Model

### Client Types

#### Resource Clients (Backend APIs)
- **Purpose**: Confidential clients representing backend API services
- **Creation**: `POST /realms/{realm}/bodhi/resources` (no authentication required)
- **Configuration**: 
  - Confidential client with client secret
  - Service account enabled
  - Standard token exchange v2 enabled (`standard.token.exchange.enabled=true`)
  - 4-level role hierarchy: `resource_user`, `resource_power_user`, `resource_manager`, `resource_admin`
- **Naming**: `resource-{uuid}` (production) or `test-resource-{uuid}` (test environment)
- **Client Scope**: Automatic `scope_{client-id}` creation with audience mapper

#### App Clients (Frontend Applications)
- **Purpose**: Public clients for user authentication and initial token acquisition
- **Creation**: `POST /realms/{realm}/bodhi/apps` (requires dev console user token)
- **Configuration**:
  - Public client (no client secret)
  - Consent enabled for resource access
  - Single `admin` role only
- **Naming**: `app-{uuid}` (production) or `test-app-{uuid}` (test environment)
- **Resource Access**: Must call `/request-access` to obtain resource client scopes

### Authorization Model

#### User Groups Structure
```
users-{client-id}/
├── admins/          # resource_admin role
├── managers/        # resource_manager role  
├── power-users/     # resource_power_user role
└── users/           # resource_user role
```

#### Client Scopes (scope_user_*)
- `scope_user_user`: Basic user access level
- `scope_user_power_user`: Power user access level
- `scope_user_manager`: Manager access level
- `scope_user_admin`: Admin access level

These scopes control access granularity during token exchange and are used for fine-grained authorization.

## Testcontainers Setup

### 1. Container Configuration

```typescript
import { GenericContainer, StartedTestContainer } from 'testcontainers';

const keycloakContainer = await new GenericContainer('ghcr.io/bodhisearch/bodhi-auth-testcontainer:latest')
  .withExposedPorts(8080)
  .withEnvironment({
    'KEYCLOAK_ADMIN': 'admin',
    'KEYCLOAK_ADMIN_PASSWORD': 'admin',
    'APP_ENV': 'test'
  })
  .start();

const keycloakUrl = `http://localhost:${keycloakContainer.getMappedPort(8080)}`;
```

### 2. Realm Initialization

```typescript
import { execSync } from 'child_process';

// Initialize realm using keycloak-config-cli
const configCommand = `java -jar tools/keycloak-config-cli-26.1.0.jar \
  --import.files.locations=realm.json \
  --keycloak.url=${keycloakUrl} \
  --keycloak.user=admin \
  --keycloak.password=admin`;

execSync(configCommand, { stdio: 'inherit' });
```

## Standard Token Exchange V2

### Token Exchange Flow
1. **App Client Authentication**: User authenticates with app client, receives token with resource scope in audience
2. **API Request**: App client calls resource server API with app token
3. **Token Exchange**: Resource server exchanges app token for resource-specific token
4. **Validation**: Keycloak validates using standard RFC 8693 token exchange
5. **Response**: Resource server receives properly scoped token for API access

### Exchange Request Format
```typescript
const exchangeToken = async (resourceClient: ClientPair, subjectToken: string, scopes: string[]) => {
  const response = await fetch(`${keycloakUrl}/realms/bodhi/protocol/openid-connect/token`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Authorization': `Basic ${btoa(`${resourceClient.clientId}:${resourceClient.clientSecret}`)}`
    },
    body: new URLSearchParams({
      'grant_type': 'urn:ietf:params:oauth:grant-type:token-exchange',
      'subject_token': subjectToken,
      'audience': resourceClient.clientId,
      'scope': scopes.join(' ')
    })
  });
  
  const result = await response.json();
  return result.access_token;
};
```

## BodhiResourceProvider API Endpoints

### POST /resources
Creates a new resource client with full configuration:
- Generates confidential client with service account
- Creates 4-level role hierarchy and group structure
- Enables standard token exchange v2
- Creates associated client scope with audience mapper
- Returns client credentials and scope name

### POST /apps  
Creates a new app client (requires dev console user token):
- Generates public client with no client secret
- Creates simplified admin-only role structure
- Enables consent for resource access
- Returns client ID only

### POST /resources/request-access
Enables app client to access specific resource server:
- Validates resource client has token exchange enabled
- Validates app client is public client
- Adds resource client scope as optional scope to app client
- Enables dynamic consent-based access control

### POST /resources/make-resource-admin
Makes a user admin of a resource client:
- Requires resource client service account token
- Adds user to admins group
- Grants resource_admin role

### POST /resources/add-to-group
Adds user to specific group within resource client:
- Requires admin token for the resource client
- Supports all group levels (users, power-users, managers, admins)
- Automatically maps appropriate roles

## Implementation Patterns

### 1. Resource Client Creation and Setup
```typescript
// Create resource client
const createResourceResponse = await fetch(`${keycloakUrl}/realms/bodhi/bodhi/resources`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'Test API Server',
    description: 'Test resource for integration testing',
    redirect_uris: ['http://localhost:8080/callback']
  })
});

const resourceClient = await createResourceResponse.json();
// Returns: { client_id, client_secret, scope }

// Get service account token
const serviceTokenResponse = await fetch(`${keycloakUrl}/realms/bodhi/protocol/openid-connect/token`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  body: new URLSearchParams({
    'grant_type': 'client_credentials',
    'scope': 'service_account',
    'client_id': resourceClient.client_id,
    'client_secret': resourceClient.client_secret
  })
});

const serviceToken = (await serviceTokenResponse.json()).access_token;
```

### 2. App Client Creation and Resource Access
```typescript
// Get dev console user token first (requires authentication)
const devConsoleToken = await getDevConsoleUserToken();

// Create app client
const createAppResponse = await fetch(`${keycloakUrl}/realms/bodhi/bodhi/apps`, {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${devConsoleToken}`
  },
  body: JSON.stringify({
    name: 'Test Frontend App',
    description: 'Test app for integration testing',
    redirect_uris: ['http://localhost:3000/callback']
  })
});

const appClient = await createAppResponse.json();
// Returns: { client_id }

// Request access to resource
const requestAccessResponse = await fetch(`${keycloakUrl}/realms/bodhi/bodhi/resources/request-access`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${serviceToken}`
  },
  body: JSON.stringify({
    app_client_id: appClient.client_id
  })
});

const accessResult = await requestAccessResponse.json();
// Returns: { scope: 'scope_resource-{id}' }
```

### 3. OAuth Flow with Resource Scope
```typescript
// OAuth authorization with resource scope
const authUrl = new URL(`${keycloakUrl}/realms/bodhi/protocol/openid-connect/auth`);
authUrl.searchParams.set('client_id', appClient.client_id);
authUrl.searchParams.set('redirect_uri', 'http://localhost:3000/callback');
authUrl.searchParams.set('response_type', 'code');
authUrl.searchParams.set('scope', `openid email profile roles scope_user_user ${resourceClient.scope}`);

// After user consent and code exchange, app token will include resource client in audience
```

### 4. Token Exchange for API Access
```typescript
// Resource server exchanges app token for resource-specific token
const exchangedToken = await exchangeToken(
  { clientId: resourceClient.client_id, clientSecret: resourceClient.client_secret },
  appUserToken,
  ['openid', 'email', 'profile', 'roles', 'scope_user_power_user']
);

// Exchanged token has resource client as audience and appropriate roles
```

## Error Handling and Common Pitfalls

### 1. Audience Validation Errors
**Problem**: "Client not in audience" during token exchange
**Solution**: Ensure app client requested resource scope during OAuth flow

### 2. Missing Permissions
**Problem**: Token exchange fails with permission denied
**Solution**: Verify resource client has `standard.token.exchange.enabled=true`

### 3. Scope Mismatch
**Problem**: Exchanged token missing expected roles
**Solution**: Check user group membership and requested scopes match

### 4. Authentication Failures
**Problem**: Service account token requests fail
**Solution**: Verify client credentials and service account is enabled

## Testing Best Practices

1. **Container Lifecycle**: Start container once per test suite, clean data between tests
2. **Token Validation**: Always decode and verify JWT claims in tests
3. **Scope Testing**: Test different scope combinations for access control
4. **Error Scenarios**: Test invalid clients, missing permissions, and malformed requests
5. **Cleanup**: Remove test clients and users after test completion

This guide provides the foundation for creating comprehensive integration tests that validate the complete OAuth2 token exchange workflow with proper audience management and access control.
