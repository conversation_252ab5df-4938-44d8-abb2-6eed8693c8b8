# Regular Dockerfile for Keycloak with custom SPI
# Shares common logic with production build

# Stage 1: Dependency resolver - support multi-platform builds
FROM --platform=$BUILDPLATFORM openjdk:21-jdk-slim AS deps

WORKDIR /app

# Copy Maven wrapper and configuration
COPY mvnw .
COPY .mvn .mvn/
COPY pom.production.xml pom.xml

# Make Maven wrapper executable
RUN chmod +x ./mvnw

# Download dependencies
RUN ./mvnw dependency:resolve-sources dependency:resolve dependency:go-offline -B

FROM deps AS builder

# Copy source code for building
COPY src/main/ src/main/

# Build the extension
RUN ./mvnw clean package -o

# Verify JAR exists
RUN ls -la target/ && test -f target/keycloak-bodhi-ext.jar

# Keycloak build stage - only build-time options here
FROM quay.io/keycloak/keycloak:26.2.5 AS optimizer

# Copy our custom extension
COPY --from=builder /app/target/keycloak-bodhi-ext.jar /opt/keycloak/providers/

# Copy cache configuration
COPY cache-ispn-jdbc-ping.xml /opt/keycloak/conf/cache-ispn-jdbc-ping.xml

# Build Keycloak with build-time options optimized for production cluster
RUN /opt/keycloak/bin/kc.sh build \
    --db=postgres \
    --http-relative-path=/ \
    --features=token-exchange \
    --health-enabled=true \
    --metrics-enabled=true \
    --transaction-xa-enabled=false
 
# Final runtime stage
FROM quay.io/keycloak/keycloak:26.2.5

# Copy optimized Keycloak
COPY --from=optimizer /opt/keycloak/ /opt/keycloak/

# Set container memory for production cluster (8GB RAM, 4 vCPU, 2+ pods)
# Aggressive heap allocation for high-throughput production workload
ENV JAVA_OPTS_KC_HEAP="-XX:InitialRAMPercentage=50 -XX:MaxRAMPercentage=80 -XX:MinHeapFreeRatio=5 -XX:MaxHeapFreeRatio=15"

# JVM optimizations for clustered production performance
# Note: Unlock experimental options MUST come first, before any experimental options
# Railway uses IPv6-only private network, so we support dual-stack networking
ENV JAVA_OPTS_APPEND="-server -XX:+UnlockExperimentalVMOptions -XX:+UseG1GC -XX:+UseStringDeduplication -XX:+OptimizeStringConcat -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:MaxGCPauseMillis=200 -XX:G1HeapRegionSize=16m -XX:G1NewSizePercent=30 -XX:G1MaxNewSizePercent=40 -XX:ParallelGCThreads=4 -XX:ConcGCThreads=2 -Djava.security.egd=file:/dev/./urandom -Djava.awt.headless=true -Dfile.encoding=UTF-8 -Duser.timezone=UTC -XX:+ExitOnOutOfMemoryError"

# Production environment
ENV APP_ENV=production

# Create non-root user for better security
USER 1000

# Set working directory
WORKDIR /opt/keycloak

# Labels for better maintainability
LABEL maintainer="BodhiSearch" \
      version="1.0" \
      description="Keycloak with Bodhi SPI extension" \
      keycloak.version="26.2.5" \
      security.obfuscated="false"

# Use ENTRYPOINT for proper signal handling - allows users to pass custom options
# Default production cluster configuration (can be overridden)
ENTRYPOINT ["/opt/keycloak/bin/kc.sh"]
CMD ["start", "--optimized", \
     "--hostname-strict=false", \
     "--hostname-backchannel-dynamic=true", \
     "--proxy-headers=xforwarded", \
     "--http-enabled=true", \
     "--log-level=INFO", \
     "--log-console-color=false", \
     "--http-max-queued-requests=1000", \
     "--db-pool-initial-size=5", \
     "--db-pool-max-size=20", \
     "--db-pool-min-size=5"]
