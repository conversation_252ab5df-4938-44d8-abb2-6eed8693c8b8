# Railway will deploy from Docker image configured in dashboard
# Image: ghcr.io/bodhisearch/bodhi-auth-server:latest
# This file configures deployment settings only

[deploy]
healthcheckPath = "/realms/master"
healthcheckTimeout = 600
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 10

[environments]
prod.name = "production"
prod.plugins.postgresql.enabled = true
dev.name = "development"
dev.plugins.postgresql.enabled = true
main.name = "integration"
main.plugins.postgresql.enabled = true

[[plugins]]
name = "postgresql"

[plugins.postgresql]
version = "14"

# Common environment variables shared across all environments
[envs]
# Keycloak cache configuration
KC_CACHE="ispn"
KC_CACHE_CONFIG_FILE="/opt/keycloak/conf/cache-ispn-jdbc-ping.xml"
# Database configuration
KC_DB="postgres"
KC_DB_PASSWORD="${{Postgres.PGPASSWORD}}"
KC_DB_URL="jdbc:postgresql://${{Postgres.PGHOST}}:${{Postgres.PGPORT}}/${{Postgres.PGDATABASE}}"
KC_DB_USERNAME="${{Postgres.PGUSER}}"
# Keycloak features and health
KC_FEATURES="token-exchange"
KC_HEALTH_ENABLED="true"
# Management interface configuration
KC_HTTP_MANAGEMENT_PORT="9000"
# Hostname configuration
KC_HOSTNAME_STRICT="false"
KC_HOSTNAME_BACKCHANNEL_DYNAMIC="true"
KC_PROXY_HEADERS="xforwarded"
# Logging configuration
KC_LOG_CONSOLE_FORMAT="%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{1}] (%t) %s%e%n"
KC_LOG_CONSOLE_COLOR="false"
# Security and login failure protection
KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_MAX_FAILURES="5"
KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_MAX_WAIT="300"
KC_SPI_EVENTS_LISTENER_LOGIN_FAILURES_WAIT_INCREMENT="60"
# Bootstrap admin credentials
KC_BOOTSTRAP_ADMIN_USERNAME="bootstrap-admin"
KC_BOOTSTRAP_ADMIN_PASSWORD="<random-temp-password>"
# Transaction and connection optimizations
QUARKUS_TRANSACTION_MANAGER_ENABLE_RECOVERY="true"

# Production environment (8vCPU/16GB) - Optimized for high-throughput production workload
[environments.prod.envs]
APP_ENV="production"
# JVM settings optimized for production cluster (16GB RAM, 8 vCPU)
# Note: UnlockExperimentalVMOptions MUST come first
JAVA_OPTS="-XX:+UnlockExperimentalVMOptions -Xms2g -Xmx12g -XX:MetaspaceSize=256M -XX:MaxMetaspaceSize=1g -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication -XX:ParallelGCThreads=8 -XX:ConcGCThreads=4"
# Production database connection pooling
KC_DB_POOL_INITIAL_SIZE="5"
KC_DB_POOL_MAX_SIZE="50"
KC_DB_POOL_MIN_SIZE="5"
# Hostname configuration for production
KC_HOSTNAME="https://prod-id.getbodhi.app"
KC_HOSTNAME_ADMIN="https://prod-id.getbodhi.app"
# Production logging - WARN level for performance
KC_LOG_LEVEL="WARN"
# Production optimizations
QUARKUS_HTTP_LIMITS_MAX_CONNECTIONS="2000"
QUARKUS_DATASOURCE_JDBC_MAX_SIZE="50"

# Development environment (4vCPU/8GB) - Production branch for fixes
[environments.dev.envs]
APP_ENV="dev"
# JVM settings optimized for development cluster (8GB RAM, 4 vCPU)
# Note: UnlockExperimentalVMOptions MUST come first
JAVA_OPTS="-XX:+UnlockExperimentalVMOptions -Xms1g -Xmx6g -XX:MetaspaceSize=128M -XX:MaxMetaspaceSize=512m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication -XX:ParallelGCThreads=4 -XX:ConcGCThreads=2"
# Development database connection pooling
KC_DB_POOL_INITIAL_SIZE="3"
KC_DB_POOL_MAX_SIZE="25"
KC_DB_POOL_MIN_SIZE="3"
# Hostname configuration for development
KC_HOSTNAME="https://dev-id.getbodhi.app"
KC_HOSTNAME_ADMIN="https://dev-id.getbodhi.app"
# Development logging - INFO level for debugging
KC_LOG_LEVEL="INFO"
# Development optimizations
QUARKUS_HTTP_LIMITS_MAX_CONNECTIONS="1000"
QUARKUS_DATASOURCE_JDBC_MAX_SIZE="25"

# Main environment (4vCPU/8GB) - Latest main branch for integration testing
[environments.main.envs]
APP_ENV="dev"
# JVM settings optimized for integration testing (8GB RAM, 4 vCPU)
# Note: UnlockExperimentalVMOptions MUST come first
JAVA_OPTS="-XX:+UnlockExperimentalVMOptions -Xms1g -Xmx6g -XX:MetaspaceSize=128M -XX:MaxMetaspaceSize=512m -XX:+UseG1GC -XX:MaxGCPauseMillis=100 -XX:+UseStringDeduplication -XX:ParallelGCThreads=4 -XX:ConcGCThreads=2"
# Integration testing database connection pooling
KC_DB_POOL_INITIAL_SIZE="3"
KC_DB_POOL_MAX_SIZE="25"
KC_DB_POOL_MIN_SIZE="3"
# Hostname configuration for main integration
KC_HOSTNAME="https://main-id.getbodhi.app"
KC_HOSTNAME_ADMIN="https://main-id.getbodhi.app"
# Integration testing logging - INFO level for debugging
KC_LOG_LEVEL="INFO"
# Integration testing optimizations
QUARKUS_HTTP_LIMITS_MAX_CONNECTIONS="1000"
QUARKUS_DATASOURCE_JDBC_MAX_SIZE="25"