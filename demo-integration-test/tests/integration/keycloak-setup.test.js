import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { GenericContainer } from 'testcontainers';
import { execSync } from 'child_process';
import { KeycloakClient } from '../../src/keycloak-client.js';

describe('Keycloak Bodhi Extension Integration', () => {
  let container;
  let keycloakUrl;
  let keycloakClient;

  beforeAll(async () => {
    console.log('Starting Keycloak container...');

    // Start Keycloak container with correct environment variables
    container = await new GenericContainer('ghcr.io/bodhisearch/bodhi-auth-testcontainer:latest')
      .withExposedPorts(8080)
      .withEnvironment({
        'KC_BOOTSTRAP_ADMIN_USERNAME': 'admin',
        'KC_BOOTSTRAP_ADMIN_PASSWORD': 'admin',
        'KC_HOSTNAME': 'localhost',
        'KC_FEATURES': 'token-exchange',
        'APP_ENV': 'test'
      })
      .withStartupTimeout(120000) // 2 minutes
      .start();

    keycloakUrl = `http://localhost:${container.getMappedPort(8080)}`;
    keycloakClient = new KeycloakClient(keycloakUrl);

    console.log(`Keycloak started at: ${keycloakUrl}`);

    // Wait for Keycloak to be ready
    await waitForKeycloak(keycloakUrl);

    // Import realm configuration using keycloak-config-cli with existing realm.json
    try {
      console.log('Importing realm configuration...');
      execSync(`java -jar ../tools/keycloak-config-cli-26.1.0.jar \
        --import.files.locations=./realm.json \
        --keycloak.url=${keycloakUrl} \
        --keycloak.user=admin \
        --keycloak.password=admin`,
        { stdio: 'inherit' }
      );
      console.log('Realm configuration imported successfully');
    } catch (error) {
      console.warn('Could not import realm config with keycloak-config-cli:', error.message);
      // Continue without realm import for basic container test
    }
  }, 180000); // 3 minutes timeout

  afterAll(async () => {
    if (container) {
      await container.stop();
    }
  });

  it('should start Keycloak container and be accessible', async () => {
    // Simple test to verify container is running and accessible
    expect(container).toBeTruthy();
    expect(keycloakUrl).toBeTruthy();

    // Test basic connectivity to Keycloak
    const response = await fetch(`${keycloakUrl}/realms/master`);
    expect(response.ok).toBe(true);

    const realmInfo = await response.json();
    expect(realmInfo.realm).toBe('master');

    console.log('✅ Keycloak container is running and accessible');
    console.log(`✅ Keycloak URL: ${keycloakUrl}`);
  });

  it('should create a resource client via /resources endpoint', async () => {
    const resourceClient = await keycloakClient.createResourceClient(
      'Test API Server',
      'Test resource for integration testing',
      ['http://localhost:8080/callback']
    );

    // Verify response structure
    expect(resourceClient).toHaveProperty('client_id');
    expect(resourceClient).toHaveProperty('client_secret');
    expect(resourceClient).toHaveProperty('scope');

    // Verify naming conventions (uses resource- prefix in test environment without live_test param)
    expect(resourceClient.client_id).toMatch(/^resource-/);
    expect(resourceClient.scope).toBe(`scope_${resourceClient.client_id}`);

    console.log('✅ Resource client created:', {
      clientId: resourceClient.client_id,
      scope: resourceClient.scope
    });
  });

  it('should get service account token for resource client', async () => {
    // Create resource client
    const resourceClient = await keycloakClient.createResourceClient(
      'Test Service Account',
      'Test resource for service account testing',
      ['http://localhost:8080/callback']
    );

    // Get service account token
    const serviceToken = await keycloakClient.getServiceAccountToken(
      resourceClient.client_id,
      resourceClient.client_secret
    );

    expect(serviceToken).toBeTruthy();
    expect(typeof serviceToken).toBe('string');

    // Decode and verify token
    const decodedToken = keycloakClient.decodeToken(serviceToken);
    expect(decodedToken.azp).toBe(resourceClient.client_id);
    expect(decodedToken.typ).toBe('Bearer');
    expect(decodedToken.client_id).toBe(resourceClient.client_id);

    console.log('✅ Service account token obtained and verified');
  });

  it('should check if resource has admin users', async () => {
    // Create resource client
    const resourceClient = await keycloakClient.createResourceClient(
      'Test Admin Check',
      'Test resource for admin checking',
      ['http://localhost:8080/callback']
    );

    // Get service account token
    const serviceToken = await keycloakClient.getServiceAccountToken(
      resourceClient.client_id,
      resourceClient.client_secret
    );

    // Check if resource has admin (should be false initially)
    const hasAdmin = await keycloakClient.hasResourceAdmin(serviceToken);
    expect(hasAdmin).toHaveProperty('has_admin');
    expect(hasAdmin.has_admin).toBe(false);

    console.log('✅ Resource admin check working correctly');
  });
});

async function waitForKeycloak(url, maxAttempts = 30) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch(`${url}/realms/master`);
      if (response.ok) {
        console.log('Keycloak is ready');
        return;
      }
    } catch (error) {
      // Ignore connection errors during startup
    }

    console.log(`Waiting for Keycloak... (${i + 1}/${maxAttempts})`);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  throw new Error('Keycloak failed to start within timeout period');
}
