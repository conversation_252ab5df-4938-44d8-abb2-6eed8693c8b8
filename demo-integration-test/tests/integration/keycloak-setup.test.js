import { describe, it, expect, beforeAll, afterAll } from 'vitest';
import { GenericContainer } from 'testcontainers';
import { execSync } from 'child_process';

describe('Keycloak Bodhi Extension Integration', () => {
  let container;
  let keycloakUrl;

  beforeAll(async () => {
    console.log('Starting Keycloak container...');

    // Start Keycloak container with correct environment variables
    container = await new GenericContainer('ghcr.io/bodhisearch/bodhi-auth-testcontainer:latest')
      .withExposedPorts(8080)
      .withEnvironment({
        'KC_BOOTSTRAP_ADMIN_USERNAME': 'admin',
        'KC_BOOTSTRAP_ADMIN_PASSWORD': 'admin',
        'KC_HOSTNAME': 'localhost',
        'KC_FEATURES': 'token-exchange',
        'APP_ENV': 'test'
      })
      .withStartupTimeout(120000) // 2 minutes
      .start();

    keycloakUrl = `http://localhost:${container.getMappedPort(8080)}`;

    console.log(`Keycloak started at: ${keycloakUrl}`);

    // Wait for Keycloak to be ready
    await waitForKeycloak(keycloakUrl);

    // Import realm configuration using keycloak-config-cli with existing realm.json
    try {
      console.log('Importing realm configuration...');
      execSync(`java -jar ../tools/keycloak-config-cli-26.1.0.jar \
        --import.files.locations=./realm.json \
        --keycloak.url=${keycloakUrl} \
        --keycloak.user=admin \
        --keycloak.password=admin`,
        { stdio: 'inherit' }
      );
      console.log('Realm configuration imported successfully');
    } catch (error) {
      console.warn('Could not import realm config with keycloak-config-cli:', error.message);
      // Continue without realm import for basic container test
    }
  }, 180000); // 3 minutes timeout

  afterAll(async () => {
    if (container) {
      await container.stop();
    }
  });

  it('should start Keycloak container and be accessible', async () => {
    // Simple test to verify container is running and accessible
    expect(container).toBeTruthy();
    expect(keycloakUrl).toBeTruthy();

    // Test basic connectivity to Keycloak
    const response = await fetch(`${keycloakUrl}/realms/master`);
    expect(response.ok).toBe(true);

    const realmInfo = await response.json();
    expect(realmInfo.realm).toBe('master');

    console.log('✅ Keycloak container is running and accessible');
    console.log(`✅ Keycloak URL: ${keycloakUrl}`);
  });
});

async function waitForKeycloak(url, maxAttempts = 30) {
  for (let i = 0; i < maxAttempts; i++) {
    try {
      const response = await fetch(`${url}/realms/master`);
      if (response.ok) {
        console.log('Keycloak is ready');
        return;
      }
    } catch (error) {
      // Ignore connection errors during startup
    }

    console.log(`Waiting for Keycloak... (${i + 1}/${maxAttempts})`);
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  throw new Error('Keycloak failed to start within timeout period');
}
