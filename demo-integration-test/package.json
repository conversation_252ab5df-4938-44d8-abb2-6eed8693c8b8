{"name": "keycloak-bodhi-integration-demo", "version": "1.0.0", "description": "Demo integration testing with Keycloak Bodhi Extension", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "test": "vitest run", "test:ui": "vitest --ui", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui"}, "dependencies": {"node-fetch": "^3.3.2"}, "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.10.0", "testcontainers": "^10.4.0", "vite": "^5.0.0", "vitest": "^1.0.0"}}