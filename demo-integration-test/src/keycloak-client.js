import fetch from 'node-fetch';

export class KeycloakClient {
  constructor(baseUrl, realm = 'bodhi') {
    this.baseUrl = baseUrl;
    this.realm = realm;
    this.tokenEndpoint = `${baseUrl}/realms/${realm}/protocol/openid-connect/token`;
    this.bodhiEndpoint = `${baseUrl}/realms/${realm}/bodhi`;
  }

  /**
   * Create a new resource client
   */
  async createResourceClient(name, description, redirectUris) {
    const response = await fetch(`${this.bodhiEndpoint}/resources`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name,
        description,
        redirect_uris: redirectUris
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to create resource client: ${response.status} ${await response.text()}`);
    }

    return await response.json();
  }

  /**
   * Get service account token for a resource client
   */
  async getServiceAccountToken(clientId, clientSecret) {
    const response = await fetch(this.tokenEndpoint, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${clientId}:${clientSecret}`).toString('base64')}`
      },
      body: new URLSearchParams({
        'grant_type': 'client_credentials',
        'scope': 'service_account'
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to get service account token: ${response.status} ${await response.text()}`);
    }

    const result = await response.json();
    return result.access_token;
  }

  /**
   * Exchange app token for resource-specific token
   */
  async exchangeToken(resourceClientId, resourceClientSecret, subjectToken, scopes = []) {
    const response = await fetch(this.tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${resourceClientId}:${resourceClientSecret}`).toString('base64')}`
      },
      body: new URLSearchParams({
        'grant_type': 'urn:ietf:params:oauth:grant-type:token-exchange',
        'subject_token': subjectToken,
        'audience': resourceClientId,
        'scope': scopes.join(' ')
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Token exchange failed: ${response.status} ${errorText}`);
    }

    const result = await response.json();
    return result.access_token;
  }

  /**
   * Decode JWT token (basic implementation for testing)
   */
  decodeToken(token) {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT token');
    }
    
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
    return payload;
  }

  /**
   * Make a user admin of a resource client
   */
  async makeResourceAdmin(serviceToken, userEmail) {
    const response = await fetch(`${this.bodhiEndpoint}/resources/admin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceToken}`
      },
      body: JSON.stringify({
        user_email: userEmail
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to make user admin: ${response.status} ${await response.text()}`);
    }

    return await response.json();
  }

  /**
   * Request access for app client to resource client
   */
  async requestAccess(serviceToken, appClientId) {
    const response = await fetch(`${this.bodhiEndpoint}/resources/request-access`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${serviceToken}`
      },
      body: JSON.stringify({
        app_client_id: appClientId
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to request access: ${response.status} ${await response.text()}`);
    }

    return await response.json();
  }
}
