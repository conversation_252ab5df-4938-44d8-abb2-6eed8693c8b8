diff --git a/src/main/java/com/bodhisearch/OfflineTokenSupportExchangeProvider.java b/src/main/java/com/bodhisearch/OfflineTokenSupportExchangeProvider.java
new file mode 100644
index 0000000..54ded8a
--- /dev/null
+++ b/src/main/java/com/bodhisearch/OfflineTokenSupportExchangeProvider.java
@@ -0,0 +1,38 @@
+package com.bodhisearch;
+
+import org.keycloak.models.UserModel;
+import org.keycloak.models.UserSessionModel;
+import org.keycloak.protocol.oidc.DefaultTokenExchangeProvider;
+import org.keycloak.protocol.oidc.TokenExchangeContext;
+
+import jakarta.ws.rs.core.MultivaluedMap;
+import jakarta.ws.rs.core.Response;
+import org.keycloak.representations.AccessToken;
+import java.util.Arrays;
+import java.util.HashSet;
+import java.util.Set;
+
+public class OfflineTokenSupportExchangeProvider extends DefaultTokenExchangeProvider {
+  private MultivaluedMap<String, String> formParams;
+
+  @Override
+  public Response exchange(TokenExchangeContext context) {
+    this.formParams = context.getFormParams();
+    return super.exchange(context);
+  }
+
+  protected Response exchangeClientToClient(UserModel targetUser, UserSessionModel targetUserSession,
+      AccessToken token, boolean disallowOnHolderOfTokenMismatch) {
+    String offlineToken = this.formParams.getFirst("offline_token");
+    if (offlineToken != null) {
+      String currentScope = token.getScope();
+      Set<String> scopes = new HashSet<>();
+      if (currentScope != null && !currentScope.isEmpty()) {
+        scopes.addAll(Arrays.asList(currentScope.split(" ")));
+      }
+      scopes.add("offline_access");
+      token.setScope(String.join(" ", scopes));
+    }
+    return super.exchangeClientToClient(targetUser, targetUserSession, token, disallowOnHolderOfTokenMismatch);
+  }
+}
diff --git a/src/main/java/com/bodhisearch/OfflineTokenSupportExchangeProviderFactory.java b/src/main/java/com/bodhisearch/OfflineTokenSupportExchangeProviderFactory.java
new file mode 100644
index 0000000..a9eda91
--- /dev/null
+++ b/src/main/java/com/bodhisearch/OfflineTokenSupportExchangeProviderFactory.java
@@ -0,0 +1,17 @@
+package com.bodhisearch;
+
+import org.keycloak.models.KeycloakSession;
+import org.keycloak.protocol.oidc.DefaultTokenExchangeProviderFactory;
+import org.keycloak.protocol.oidc.TokenExchangeProvider;
+
+public class OfflineTokenSupportExchangeProviderFactory extends DefaultTokenExchangeProviderFactory {
+  @Override
+  public TokenExchangeProvider create(KeycloakSession session) {
+    return new OfflineTokenSupportExchangeProvider();
+  }
+
+  @Override
+  public String getId() {
+    return "offline-token-support";
+  }
+}
diff --git a/src/main/resources/META-INF/services/org.keycloak.protocol.oidc.TokenExchangeProviderFactory b/src/main/resources/META-INF/services/org.keycloak.protocol.oidc.TokenExchangeProviderFactory
new file mode 100644
index 0000000..bc08ba6
--- /dev/null
+++ b/src/main/resources/META-INF/services/org.keycloak.protocol.oidc.TokenExchangeProviderFactory
@@ -0,0 +1 @@
+com.bodhisearch.OfflineTokenSupportExchangeProviderFactory
