# Unified ProGuard Configuration for Keycloak Bodhi SPI Extension
# Combines runtime and production settings for maximum obfuscation with functionality preservation

# Input and Output
-injars target/keycloak-bodhi-ext.jar
-outjars target/keycloak-bodhi-ext-obfuscated.jar

# Library JARs - Java runtime modules (Keycloak provides dependencies at runtime)
-libraryjars <java.home>/jmods/java.base.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.logging.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.management.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.naming.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.net.http.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.security.jgss.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.sql.jmod(!**.jar;!module-info.class)
-libraryjars <java.home>/jmods/java.xml.jmod(!**.jar;!module-info.class)

# Optimization settings
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 3

# Enhanced obfuscation settings
-overloadaggressively
-repackageclasses 'obf'
-allowaccessmodification
-mergeinterfacesaggressively
-useuniqueclassmembernames
-flattenpackagehierarchy 'obf'

# String and resource obfuscation
-adaptclassstrings
-adaptresourcefilenames **.properties,**.xml,**.txt,**.html,**.css,**.js
-adaptresourcefilecontents **.properties,META-INF/MANIFEST.MF,**.java

# Control flow obfuscation (make code harder to reverse engineer)
-assumenosideeffects class java.lang.System {
    public static long currentTimeMillis();
    static java.lang.Class class$(java.lang.String);
    static java.lang.Class class$(java.lang.String, boolean);
}

# Debug information handling
-renamesourcefileattribute SourceFile
-keepattributes SourceFile,LineNumberTable,*Annotation*,Signature,InnerClasses,EnclosingMethod

# ===== KEYCLOAK SPI PRESERVATION =====

# Keep SPI interfaces and class hierarchy - CRITICAL for Keycloak SPI
-keep class * implements org.keycloak.services.resource.RealmResourceProviderFactory {
  *;
}

-keep class * implements org.keycloak.services.resource.RealmResourceProvider {
  *;
}

-keep class * implements org.keycloak.provider.ProviderFactory {
  *;
}

# Keep all Keycloak interfaces and their implementations
-keep public interface org.keycloak.provider.Provider {
  *;
}

-keep public interface org.keycloak.services.resource.RealmResourceProviderFactory {
  *;
}

-keep public interface org.keycloak.services.resource.RealmResourceProvider {
  *;
}

# Keep class hierarchy relationships
-keep public class * extends org.keycloak.provider.Provider {
  public *;
}

-keep public class * implements org.keycloak.provider.Provider {
  public *;
}

# ===== PROVIDER CLASSES PRESERVATION =====

# Keep our specific provider factory
-keep class com.bodhisearch.BodhiResourceProviderFactory {
  *;
}

# Selective preservation for BodhiResourceProvider - allow internal obfuscation
-keep class com.bodhisearch.BodhiResourceProvider {
  # Keep SPI interface methods (required by Keycloak)
  public void close();
  public java.lang.Object getResource();
  
  # Keep constructor (required for instantiation)
  public <init>(...);
  
  # Keep JAX-RS annotated public methods (REST endpoints)
  @jakarta.ws.rs.* public <methods>;
  
  # Keep essential string constants used in Keycloak role/group operations
  public static final java.lang.String CLIENT_BODHI_DEV_CONSOLE;
  public static final java.lang.String RESOURCE_USER;
  public static final java.lang.String RESOURCE_POWER_USER;
  public static final java.lang.String RESOURCE_MANAGER;
  public static final java.lang.String RESOURCE_ADMIN;
  public static final java.lang.String GROUP_MANAGERS;
  public static final java.lang.String GROUP_ADMINS;
  public static final java.lang.String GROUP_USERS;
  public static final java.lang.String GROUP_POWER_USERS;
}

# More aggressive method name obfuscation - keep only public API methods
-keepclassmembernames class com.bodhisearch.BodhiResourceProvider {
  # Only keep public API methods, obfuscate everything else
  @jakarta.ws.rs.* public <methods>;
  public void close();
  public java.lang.Object getResource();
  public <init>(...);
}

# Obfuscate private/package-private method names
-keepclassmembers,allowobfuscation class com.bodhisearch.BodhiResourceProvider {
  !public <methods>;
}

# Aggressive obfuscation settings for internal implementation
-keepclassmembernames class com.bodhisearch.BodhiResourceProvider {
  # Don't keep names of internal/private methods - let them be obfuscated
  !public <methods>;
  !public <fields>;
}

# ===== JAX-RS ANNOTATION PRESERVATION =====

# Keep all JAX-RS annotations
-keep @interface jakarta.ws.rs.* { *; }
-keep @interface jakarta.ws.rs.core.* { *; }

# Keep classes with JAX-RS annotations
-keep @jakarta.ws.rs.Path class * {
  *;
}

# Keep methods with JAX-RS annotations (POST, GET, PUT, DELETE, etc.)
-keepclassmembers class * {
  @jakarta.ws.rs.* *;
}

# Keep JAX-RS parameter annotations
-keep @interface jakarta.ws.rs.PathParam
-keep @interface jakarta.ws.rs.QueryParam
-keep @interface jakarta.ws.rs.FormParam
-keep @interface jakarta.ws.rs.HeaderParam
-keep @interface jakarta.ws.rs.CookieParam
-keep @interface jakarta.ws.rs.MatrixParam

# Specifically keep our REST endpoint methods
-keepclassmembers class com.bodhisearch.BodhiResourceProvider {
  @jakarta.ws.rs.* *;
}

# ===== JACKSON JSON PROCESSING =====

# Keep Jackson annotations for JSON processing
-keep @interface com.fasterxml.jackson.annotation.* { *; }

-keep @com.fasterxml.jackson.annotation.* class * {
  *;
}

-keepclassmembers class * {
  @com.fasterxml.jackson.annotation.* *;
}

# Enhanced obfuscation for inner classes while preserving JSON functionality
-keepclassmembers class com.bodhisearch.BodhiResourceProvider$* {
  @com.fasterxml.jackson.annotation.JsonProperty *;
  # Keep default constructor for Jackson deserialization
  public <init>();
  # Allow all other constructors, methods, and field names to be obfuscated
}

# ===== EXCEPTION AND UTILITY CLASSES =====

# Keep exception classes for proper error handling
-keep public class * extends java.lang.Exception {
  public *;
}

# Keep enum classes
-keep public enum * {
  public static **[] values();
  public static ** valueOf(java.lang.String);
}

# Keep SLF4J logger fields (commonly used)
-keepclassmembers class * {
  private static final org.slf4j.Logger *;
}

# ===== SECURITY AND WARNINGS =====

# Don't warn about missing classes (they'll be provided by Keycloak at runtime)
-dontwarn **
-ignorewarnings

# Disable shrinking to avoid removing needed classes
-dontshrink

# Security: Don't skip non-public library classes
-dontskipnonpubliclibraryclasses
-dontskipnonpubliclibraryclassmembers 