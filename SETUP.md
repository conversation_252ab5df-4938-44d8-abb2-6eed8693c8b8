# Railway Deployment Setup

## Railway Docker Image Deployment

Railway deploys the Keycloak Bodhi extension from pre-built Docker images hosted on GitHub Container Registry (GHCR) instead of building from source.

### Service Configuration

1. **Service Source**: Configure Railway service to deploy from Docker image
   - Image: `ghcr.io/bodhisearch/bodhi-auth-server:latest`
   - Authentication: GitHub Personal Access Token with `read:packages` scope

2. **Required GitHub Token Setup**:
   - Create GitHub Personal Access Token (Classic) with `read:packages` permission
   - Configure Railway service environment variables for GHCR authentication
   - Token must have access to private `bodhisearch/keycloak-bodhi-ext` repository

### GitHub Container Registry Authentication

#### Creating GitHub Token

1. **Generate Personal Access Token**:
   - Go to GitHub → Settings → Developer settings → Personal access tokens → Tokens (classic)
   - Click "Generate new token (classic)"
   - Select scopes: `read:packages` (minimum required for private registry access)
   - Copy the generated token securely

2. **Railway Environment Variables**:
   Configure this variable in Railway service settings:
   ```bash
   # GitHub token for GHCR authentication
   GITHUB_TOKEN=<your-github-token-with-read-packages>
   ```

#### Railway Service Setup

1. **Change Service Source**:
   - Go to Railway project → Service settings
   - Under "Service Source", click "Connect Docker Image"
   - Enter image path: `ghcr.io/bodhisearch/bodhi-auth-server:latest`
   - Provide authentication credentials using GitHub token

2. **Environment Configuration**:
   - The `railway.toml` file configures deployment settings only
   - All Keycloak environment variables remain the same
   - `APP_ENV=dev` ensures development environment behavior

## GitHub Actions CI/CD

### Required Secrets

The following secrets need to be configured in your GitHub repository for CI/CD automation:

- `RAILWAY_PROJECT_ID` - Your Railway project ID (if using Railway CLI deployment)
- `RAILWAY_SERVICE_ID` - Your Railway service ID (if using Railway CLI deployment)
- `RAILWAY_TOKEN` - Your Railway deployment token (if using Railway CLI deployment)

### GitHub Container Registry Setup

The workflow automatically uses GitHub Container Registry (GHCR) with the following built-in configurations:

- **Registry**: `ghcr.io`
- **Authentication**: Uses `GITHUB_TOKEN` (automatically provided by GitHub Actions)
- **Image Name**: `bodhisearch/bodhi-auth-server` (production), `bodhisearch/bodhi-auth-testcontainer` (testing)
- **Permissions**: The workflow has `packages: write` permission to push images

#### Setting Up GitHub Container Registry Access

1. **Repository Settings**: Go to your repository → Settings → Actions → General
2. **Workflow Permissions**: Ensure "Read and write permissions" is selected for `GITHUB_TOKEN`
3. **Package Visibility**: Images will be private by default. To make them public:
   - Go to your repository's main page
   - Click on "Packages" in the right sidebar (after first build)
   - Select your package → Package settings → Change visibility

#### Image Locations

After successful builds, your images will be available at:
- `ghcr.io/bodhisearch/bodhi-auth-server:latest` (production image)
- `ghcr.io/bodhisearch/bodhi-auth-server:v1.0.0` (versioned releases)
- `ghcr.io/bodhisearch/bodhi-auth-testcontainer:main` (testcontainer image)

## Deployment Flow

### New Deployment Process

1. **CI/CD Pipeline**: GitHub Actions builds and pushes `bodhi-auth-server:latest` to GHCR
2. **Railway Deployment**: Railway pulls latest image from GHCR using configured authentication
3. **Container Start**: Railway starts container with environment variables from `railway.toml`
4. **Health Check**: Railway verifies `/realms/master` endpoint is accessible
5. **Service Ready**: Keycloak available with Bodhi SPI extension

### Manual Deployment

- **Railway Dashboard**: Click "Deploy" button to pull latest image
- **Automatic Updates**: Railway can be configured to auto-deploy when new images are pushed to GHCR
