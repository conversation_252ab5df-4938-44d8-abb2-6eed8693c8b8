# Obfuscation Dictionary for ProGuard
# These names will be used to replace original class/method/field names
# Using misleading but valid Java identifiers to confuse reverse engineers

# Single character names (most compact)
a
b
c
d
e
f
g
h
i
j
k
l
m
n
o
p
q
r
s
t
u
v
w
x
y
z

# Two character combinations
aa
ab
ac
ad
ae
af
ag
ah
ai
aj
ak
al
am
an
ao
ap
aq
ar
as
at
au
av
aw
ax
ay
az
ba
bb
bc
bd
be
bf
bg
bh
bi
bj
bk
bl
bm
bn
bo
bp
bq
br
bs
bt
bu
bv
bw
bx
by
bz

# Misleading but innocent-looking names
util
helper
manager
service
handler
processor
controller
factory
builder
config
settings
constants
common
base
abstract
impl
internal
core
main
app
system
data
model
entity
dto
request
response
result
status
type
kind
mode
flag
option
param
value
key
id
name
code
message
error
exception
info
debug
trace
log
logger
temp
tmp
cache
buffer
queue
stack
list
map
set
collection
array
string
number
integer
long
double
float
boolean
char
byte
object
class
method
field
property
attribute
annotation
interface
enum
package 

****************************************
