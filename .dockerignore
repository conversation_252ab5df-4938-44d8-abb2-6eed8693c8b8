# Git and version control
.git/
.gitignore
.gitattributes

# Documentation
README.md
docs/
ai-docs/
*.md

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Build artifacts (except target/ which is needed for obfuscation)
node_modules/
.gradle/
build/

# Logs
*.log
logs/

# Temporary files
tmp/
temp/
*.tmp

# OS generated files
Thumbs.db
ehthumbs.db

# Docker files (except the ones we need)
docker-compose.yml
docker-compose.*.yml
!docker-compose.production.yml
Dockerfile
!Dockerfile.production

# Secrets and sensitive files
secrets/
*.key
*.pem
*.p12
*.jks
.env.local
.env.*.local

# Obfuscation artifacts (will be generated during build)
obfuscation-mappings/
obfuscation-mappings-encrypted/
tools/

# Test files
src/test/
test/
tests/

# Coverage reports
coverage/
.nyc_output/
*.lcov

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Cache directories
.cache/
.npm/
.yarn/

# Package manager files
package-lock.json
yarn.lock

# Python artifacts (if any)
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Backup files
*.bak
*.backup
*.orig

# Deployment artifacts
deploy-*/
*.tar.gz

# Local development overrides
docker-compose.override.yml
.env.development
.env.test 