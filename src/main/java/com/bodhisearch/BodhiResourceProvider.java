package com.bodhisearch;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.keycloak.models.ClientModel;
import org.keycloak.models.GroupModel;
import org.keycloak.models.GroupProvider;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.models.RoleModel;
import org.keycloak.models.UserModel;
import org.keycloak.models.utils.KeycloakModelUtils;
import org.keycloak.services.managers.AppAuthManager;
import org.keycloak.services.managers.AuthenticationManager;
import org.keycloak.services.managers.AuthenticationManager.AuthResult;
import org.keycloak.services.resource.RealmResourceProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.annotation.JsonProperty;

import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;

import org.keycloak.models.ClientScopeModel;
import org.keycloak.models.ProtocolMapperModel;

public class BodhiResourceProvider implements RealmResourceProvider {
  public static final String CLIENT_BODHI_DEV_CONSOLE = "client-bodhi-dev-console";
  private static final Logger logger = LoggerFactory.getLogger(BodhiResourceProvider.class);
  public static final String RESOURCE_USER = "resource_user";
  public static final String RESOURCE_POWER_USER = "resource_power_user";
  public static final String RESOURCE_MANAGER = "resource_manager";
  public static final String RESOURCE_ADMIN = "resource_admin";
  private static final String GROUP_USERS_TEMPLATE = "users-%s";
  public static final String GROUP_MANAGERS = "managers";
  public static final String GROUP_ADMINS = "admins";
  public static final String GROUP_USERS = "users";
  public static final String GROUP_POWER_USERS = "power-users";
  private final KeycloakSession session;

  public BodhiResourceProvider(KeycloakSession session) {
    this.session = session;
  }

  @Override
  public void close() {
  }

  @Override
  public Object getResource() {
    return this;
  }

  @POST
  @Path("resources")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response newResource(ClientRequest request) {
    try {
      return newResourceInternal(request);
    } catch (Exception e) {
      logger.error("Internal server error occurred", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse(e.getMessage()))
          .build();
    }
  }

  Response newResourceInternal(ClientRequest request) {
    if (request.name == null) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse("name is required"))
          .build();
    }
    String appEnv = System.getenv("APP_ENV");
    String resourcePrefix = "resource-";
    boolean directAccessGrantsEnabled = false;
    if ("test".equals(appEnv)) { // enable direct access grants in testing
      directAccessGrantsEnabled = true;
    }
    if ("dev".equals(appEnv) || "test".equals(appEnv)) {
      boolean liveTest = session.getContext().getUri().getQueryParameters().containsKey("live_test");
      if (liveTest) { // prefix resources with test that are created by live test on dev server
        resourcePrefix = "test-resource-";
        directAccessGrantsEnabled = true;
      }
    }
    RealmModel realm = session.getContext().getRealm();
    String clientId = String.format("%s%s", resourcePrefix, KeycloakModelUtils.generateId());
    ClientModel client = realm.addClient(clientId);
    client.setName(request.name);
    if (request.description != null) {
      client.setDescription(request.description);
    }
    client.setEnabled(true);
    for (String redirectUri : request.redirectUris) {
      client.addRedirectUri(redirectUri);
    }
    client.addWebOrigin("+");
    client.setClientAuthenticatorType("client-secret");
    client.setConsentRequired(false);
    client.setStandardFlowEnabled(true);
    client.setDirectAccessGrantsEnabled(directAccessGrantsEnabled);
    client.setPublicClient(false);
    client.setFullScopeAllowed(false);
    client.setProtocol("openid-connect");
    client.setAttribute("standard.token.exchange.enabled", "true"); // Standard Token Exchange V2

    // Generate and set client secret
    String clientSecret = KeycloakModelUtils.generateSecret(client);
    client.setSecret(clientSecret);

    // IMPORTANT: Set service accounts enabled BEFORE creating roles
    client.setServiceAccountsEnabled(true);
    setupServiceAccountUser(realm, client);

    // Add client roles
    client.addRole(RESOURCE_USER);
    client.addRole(RESOURCE_POWER_USER);
    client.addRole(RESOURCE_MANAGER);
    client.addRole(RESOURCE_ADMIN);

    // Create group structure
    GroupModel topLevelGroup = realm.createGroup(String.format("users-%s", client.getClientId()));
    GroupModel usersGroup = realm.createGroup(null, GROUP_USERS, topLevelGroup);
    GroupModel powerUsersGroup = realm.createGroup(null, GROUP_POWER_USERS, topLevelGroup);
    GroupModel managersGroup = realm.createGroup(null, GROUP_MANAGERS, topLevelGroup);
    GroupModel adminsGroup = realm.createGroup(null, GROUP_ADMINS, topLevelGroup);

    // Assign roles to groups
    usersGroup.grantRole(client.getRole(RESOURCE_USER));
    powerUsersGroup.grantRole(client.getRole(RESOURCE_USER));
    powerUsersGroup.grantRole(client.getRole(RESOURCE_POWER_USER));
    managersGroup.grantRole(client.getRole(RESOURCE_USER));
    managersGroup.grantRole(client.getRole(RESOURCE_POWER_USER));
    managersGroup.grantRole(client.getRole(RESOURCE_MANAGER));
    adminsGroup.grantRole(client.getRole(RESOURCE_USER));
    adminsGroup.grantRole(client.getRole(RESOURCE_POWER_USER));
    adminsGroup.grantRole(client.getRole(RESOURCE_MANAGER));
    adminsGroup.grantRole(client.getRole(RESOURCE_ADMIN));

    ClientScopeModel resourceScope = createResourceScope(realm, client);
    
    // Store metadata as client attributes for efficient lookups
    client.setAttribute("bodhi.top_level_group.name", topLevelGroup.getName());
    client.setAttribute("bodhi.top_level_group.id", topLevelGroup.getId());
    client.setAttribute("bodhi.client_scope.name", resourceScope.getName());
    client.setAttribute("bodhi.client_scope.id", resourceScope.getId());
    
    // Add the resource scope to itself as optional for self-referencing
    client.addClientScope(resourceScope, false); // false = optional scope
    
    session.getTransactionManager().commit();
    ResourceClientResponse response = new ResourceClientResponse(
        client.getClientId(),
        clientSecret,
        resourceScope.getName());
    return Response.status(Response.Status.CREATED).entity(response).build();
  }

  @POST
  @Path("resources/make-resource-admin")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response makeFirstResourceAdmin(MakeAdminRequest request) {
    try {
      return makeFirstResourceAdminInternal(request);
    } catch (Exception e) {
      logger.error("Internal server error occurred", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse(e.getMessage()))
          .build();
    }
  }

  Response makeFirstResourceAdminInternal(MakeAdminRequest request) {
    AuthenticationManager.AuthResult authResult = new AppAuthManager.BearerTokenAuthenticator(session)
        .authenticate();
    try {
      checkForServiceAccount(authResult);
      boolean hasAdmin = hasAdmin(authResult);
      if (hasAdmin) {
        return Response.status(Response.Status.BAD_REQUEST)
            .entity(new ErrorResponse("resource already has a admin user"))
            .build();
      }
    } catch (ResourceProviderException e) {
      return Response.status(e.status)
          .entity(new ErrorResponse(e.message)).build();
    }
    String clientId = authResult.getToken().getIssuedFor();
    RealmModel realm = session.getContext().getRealm();
    String resourceGroupName = String.format(GROUP_USERS_TEMPLATE, clientId);
    Optional<GroupModel> topLevelGroup = session.getProvider(GroupProvider.class).getTopLevelGroupsStream(realm)
        .filter(g -> g.getName().equals(resourceGroupName)).findFirst();
    if (topLevelGroup.isEmpty()) {
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse("resource group not configured for client")).build();
    }
    Optional<GroupModel> adminGroup = topLevelGroup.get().getSubGroupsStream(GROUP_ADMINS, true, 0, 1).findFirst();
    if (adminGroup.isEmpty()) {
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse("admin group not configured for client")).build();
    }
    UserModel user = session.users().getUserByUsername(realm, request.username);
    if (user == null) {
      return Response.status(Response.Status.BAD_REQUEST).entity(new ErrorResponse("user not found")).build();
    }
    if (user.isMemberOf(adminGroup.get())) {
      return Response.ok().entity(new SuccessResponse("user already member of admin group")).build();
    }
    user.joinGroup(adminGroup.get());
    return Response.status(Response.Status.CREATED).build();
  }

  @GET
  @Path("resources/has-resource-admin")
  @Produces(MediaType.APPLICATION_JSON)
  public Response hasResourceAdmin() {
    try {
      return hasResourceAdminInternal();
    } catch (Exception e) {
      logger.error("Internal server error occurred", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse(e.getMessage()))
          .build();
    }
  }

  Response hasResourceAdminInternal() {
    AuthenticationManager.AuthResult authResult = new AppAuthManager.BearerTokenAuthenticator(session)
        .authenticate();
    try {
      checkForServiceAccount(authResult);
      boolean hasAdmin = hasAdmin(authResult);
      return Response.status(200).entity(new HasAdminResponse(hasAdmin)).build();
    } catch (ResourceProviderException e) {
      return Response.status(e.status)
          .entity(new ErrorResponse(e.message)).build();
    }
  }

  private boolean hasAdmin(AuthResult authResult) throws ResourceProviderException {
    String clientId = authResult.getToken().getIssuedFor();
    RealmModel realm = session.getContext().getRealm();
    String resourceGroupName = String.format(GROUP_USERS_TEMPLATE, clientId);
    Optional<GroupModel> topLevelGroup = session.getProvider(GroupProvider.class).getTopLevelGroupsStream(realm)
        .filter(g -> g.getName().equals(resourceGroupName)).findFirst();
    if (topLevelGroup.isEmpty()) {
      throw new ResourceProviderException("resource group not configured for client",
          Response.Status.INTERNAL_SERVER_ERROR);
    }
    Optional<GroupModel> adminGroup = topLevelGroup.get().getSubGroupsStream(GROUP_ADMINS, true, 0, 1).findFirst();
    if (adminGroup.isEmpty()) {
      throw new ResourceProviderException("admin group not configured for client",
          Response.Status.INTERNAL_SERVER_ERROR);
    }
    return session.users().getGroupMembersStream(realm, adminGroup.get()).findFirst().isPresent();
  }

  @POST
  @Path("resources/add-user-to-group")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response addUserToGroup(AddToGroupRequest request) {
    try {
      return addUserToGroupInternal(request);
    } catch (Exception e) {
      logger.error("Internal server error occurred", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse(e.getMessage()))
          .build();
    }
  }

  @POST
  @Path("apps")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response newApp(ClientRequest request) {
    try {
      return newAppInternal(request);
    } catch (Exception e) {
      logger.error("Internal server error occurred", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse(e.getMessage()))
          .build();
    }
  }

  Response addUserToGroupInternal(AddToGroupRequest request) {
    AuthenticationManager.AuthResult authResult = new AppAuthManager.BearerTokenAuthenticator(session)
        .authenticate();
    if (authResult == null) {
      return Response.status(Response.Status.UNAUTHORIZED).entity(new ErrorResponse("invalid session")).build();
    }
    String clientId = authResult.getToken().getIssuedFor();
    RealmModel realm = session.getContext().getRealm();
    ClientModel client = realm.getClientByClientId(clientId);
    if (client == null) {
      return Response.status(Response.Status.NOT_FOUND).entity(new ErrorResponse("Client not found")).build();
    }
    RoleModel adminRole = client.getRole(RESOURCE_ADMIN);
    if (adminRole == null) {
      return Response.status(Response.Status.BAD_REQUEST).entity(new ErrorResponse("resource-admin role not found"))
          .build();
    }
    UserModel adminUser = authResult.getUser();
    if (!adminUser.hasRole(adminRole)) {
      return Response.status(Response.Status.FORBIDDEN)
          .entity(new ErrorResponse("User does not have resource-admin role"))
          .build();
    }
    UserModel user = session.users().getUserByUsername(realm, request.username);
    if (user == null) {
      return Response.status(Response.Status.BAD_REQUEST).entity(new ErrorResponse("User to approve not found"))
          .build();
    }
    String groupName = request.group;
    if (groupName == null) {
      return Response.status(Response.Status.BAD_REQUEST).entity(new ErrorResponse("invalid role"))
          .build();
    }
    // Use stored group ID from metadata instead of name derivation
    String groupId = client.getAttribute("bodhi.top_level_group.id");
    if (groupId == null) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse("Client group metadata not found. Client may not be properly configured."))
          .build();
    }
    GroupModel topLevelGroup = realm.getGroupById(groupId);
    if (topLevelGroup == null) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse("Client group not found. Client may not be properly configured."))
          .build();
    }
    Optional<GroupModel> requestGroup = topLevelGroup.getSubGroupsStream(groupName, true, 0, 1).findFirst();
    if (requestGroup.isEmpty()) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse(String.format("Client does not have %s group", request.group)))
          .build();
    }
    if (request.add) {
      if (user.isMemberOf(requestGroup.get())) {
        return Response.ok(new SuccessResponse("user already member of group")).build();
      }
      user.joinGroup(requestGroup.get());
      return Response.status(Response.Status.CREATED).entity(new SuccessResponse("added to group"))
          .build();
    } else {
      if (!user.isMemberOf(requestGroup.get())) {
        return Response.status(Response.Status.BAD_REQUEST)
            .entity(new ErrorResponse("user not member of group"))
            .build();
      }
      user.leaveGroup(requestGroup.get());
      return Response.status(Response.Status.OK).entity(new SuccessResponse("removed from group"))
          .build();
    }
  }

  Response newAppInternal(ClientRequest request) {
    AuthenticationManager.AuthResult authResult = new AppAuthManager.BearerTokenAuthenticator(session)
        .authenticate();
    try {
      checkForDevConsoleUserToken(authResult);
    } catch (ResourceProviderException e) {
      return Response.status(e.status)
          .entity(new ErrorResponse(e.message)).build();
    }
    String appEnv = System.getenv("APP_ENV");
    String appPrefix = "app-";
    boolean testEnv = "test".equals(appEnv);
    boolean devEnv = "dev".equals(appEnv);
    if (testEnv) {
      appPrefix = "test-app-";
    }
    if (devEnv || testEnv) {
      boolean liveTest = session.getContext().getUri().getQueryParameters().containsKey("live_test");
      if (liveTest) {
        appPrefix = "test-app-";
      }
    }

    RealmModel realm = session.getContext().getRealm();

    // Create a new app client
    String clientId = String.format("%s%s", appPrefix, KeycloakModelUtils.generateId());
    ClientModel client = realm.addClient(clientId);
    client.setEnabled(true);
    client.setName(request.name);
    client.setDescription(request.description);

    for (String redirectUri : request.redirectUris) {
      client.addRedirectUri(redirectUri);
    }
    client.addWebOrigin("+");
    client.setStandardFlowEnabled(true);
    client.setDirectAccessGrantsEnabled(testEnv);
    client.setServiceAccountsEnabled(false); // No service account for app clients
    client.setPublicClient(true);
    client.setFullScopeAllowed(false);
    client.setProtocol("openid-connect");
    client.setConsentRequired(!testEnv);
    client.setConsentScreenText("Application will obtain your information as detailed");
    client.setDisplayOnConsentScreen(true);
    client.setGuiOrder("1");

    // Add client-level admin role for app clients
    client.addRole("admin");

    // Create group structure for app clients
    GroupModel topLevelGroup = realm.createGroup(String.format("users-%s", client.getClientId()));
    GroupModel adminsGroup = realm.createGroup(null, GROUP_ADMINS, topLevelGroup);

    // Assign admin role to admins group
    adminsGroup.grantRole(client.getRole("admin"));

    // Add the requesting user to the admins group
    UserModel requestingUser = authResult.getUser();
    requestingUser.joinGroup(adminsGroup);

    // Store metadata as client attributes for efficient lookups
    client.setAttribute("bodhi.top_level_group.name", topLevelGroup.getName());
    client.setAttribute("bodhi.top_level_group.id", topLevelGroup.getId());

    ClientSecretPair responseBody = new ClientSecretPair(clientId, null);
    return Response.status(Response.Status.CREATED).entity(responseBody).build();
  }

  @POST
  @Path("resources/request-access")
  @Consumes(MediaType.APPLICATION_JSON)
  @Produces(MediaType.APPLICATION_JSON)
  public Response requestAccess(AppAccessRequest request) {
    try {
      return requestAccessInternal(request);
    } catch (Exception e) {
      logger.error("Internal server error occurred", e);
      return Response.status(Response.Status.INTERNAL_SERVER_ERROR)
          .entity(new ErrorResponse(e.getMessage()))
          .build();
    }
  }

  Response requestAccessInternal(AppAccessRequest request) {
    AuthenticationManager.AuthResult authResult = new AppAuthManager.BearerTokenAuthenticator(session)
        .authenticate();
    try {
      checkForServiceAccount(authResult);
    } catch (ResourceProviderException e) {
      return Response.status(e.status)
          .entity(new ErrorResponse(e.message)).build();
    }
    String resourceClientId = authResult.getToken().getIssuedFor();
    RealmModel realm = session.getContext().getRealm();
    ClientModel resourceClient = realm.getClientByClientId(resourceClientId);
    if (resourceClient == null) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse("Resource client not found"))
          .build();
    }
    if (!"true".equals(resourceClient.getAttribute("standard.token.exchange.enabled"))) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse("Resource client does not have token exchange enabled"))
          .build();
    }
    ClientModel appClient = realm.getClientByClientId(request.appClientId);
    if (appClient == null) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse("App client not found"))
          .build();
    }
    if (!appClient.isPublicClient()) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse("Only public app clients can request access"))
          .build();
    }
    // Use stored client scope ID from metadata instead of name derivation
    String scopeId = resourceClient.getAttribute("bodhi.client_scope.id");
    if (scopeId == null) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse("Resource client scope metadata not found. Client may not be properly configured."))
          .build();
    }
    ClientScopeModel resourceScope = realm.getClientScopeById(scopeId);
    if (resourceScope == null) {
      return Response.status(Response.Status.BAD_REQUEST)
          .entity(new ErrorResponse("Resource scope not found. Resource client may not be properly configured."))
          .build();
    }
    boolean scopeAlreadyAdded = hasClientScope(appClient, resourceScope);
    Status status = Response.Status.OK;
    if (!scopeAlreadyAdded) {
      // Add scope as optional to app client (for on-demand consent)
      appClient.addClientScope(resourceScope, false); // false = optional scope
      status = Response.Status.CREATED;
      logger.info("Added optional scope {} to app client {}", resourceScope.getName(), request.appClientId);
    }
    AppAccessResponse response = new AppAccessResponse(resourceScope.getName());
    return Response.status(status).entity(response).build();
  }

  private boolean hasClientScope(ClientModel client, ClientScopeModel scope) {
    return client.getClientScopes(true).containsKey(scope.getName()) ||
        client.getClientScopes(false).containsKey(scope.getName());
  }

  private ClientScopeModel createResourceScope(RealmModel realm, ClientModel resourceClient) {
    String scopeName = "scope_" + resourceClient.getClientId();
    String scopeDescription = "Access to " + resourceClient.getName() + " APIs";

    // Create new client scope (no existence check needed - fail fast if exists)
    ClientScopeModel resourceScope = realm.addClientScope(scopeName);
    resourceScope.setDescription(scopeDescription);
    resourceScope.setProtocol("openid-connect");
    resourceScope.setDisplayOnConsentScreen(true);
    resourceScope.setConsentScreenText("Allow access to " + resourceClient.getName() + " APIs");
    resourceScope.setIncludeInTokenScope(true);

    // Add audience mapper to include resource client in audience
    ProtocolMapperModel audienceMapper = new ProtocolMapperModel();
    audienceMapper.setName("resource-audience-mapper");
    audienceMapper.setProtocol("openid-connect");
    audienceMapper.setProtocolMapper("oidc-audience-mapper");

    Map<String, String> config = new HashMap<>();
    config.put("included.client.audience", resourceClient.getClientId());
    config.put("id.token.claim", "false");
    config.put("access.token.claim", "true");
    audienceMapper.setConfig(config);
    resourceScope.addProtocolMapper(audienceMapper);

    // Do not add as default client scope - this makes it type "None"
    // Client scopes will be added explicitly as optional when needed
    logger.info("Created new resource scope {} for client {}", scopeName, resourceClient.getClientId());
    return resourceScope;
  }

  public void checkForServiceAccount(AuthenticationManager.AuthResult authResult) throws ResourceProviderException {
    if (authResult == null) {
      throw new ResourceProviderException("invalid session", Response.Status.UNAUTHORIZED);
    }
    Object serviceAccount = authResult.getToken().getOtherClaims().get("client_id");
    if (serviceAccount == null) {
      throw new ResourceProviderException("not a service account token", Response.Status.UNAUTHORIZED);
    }
    String clientId = authResult.getToken().getIssuedFor();
    if (!clientId.equals(serviceAccount)) {
      throw new ResourceProviderException("client id and authorized party do not match", Response.Status.UNAUTHORIZED);
    }
    RealmModel realm = session.getContext().getRealm();
    ClientModel client = realm.getClientByClientId(clientId);
    if (client == null) {
      throw new ResourceProviderException("client not found", Response.Status.BAD_REQUEST);
    }
  }

  public void checkForDevConsoleUserToken(AuthenticationManager.AuthResult authResult)
      throws ResourceProviderException {
    if (authResult == null) {
      throw new ResourceProviderException("invalid session", Response.Status.UNAUTHORIZED);
    }

    // Check that this is a user token (not a service account token)
    Object serviceAccount = authResult.getToken().getOtherClaims().get("client_id");
    if (serviceAccount != null) {
      throw new ResourceProviderException("service account tokens not allowed", Response.Status.UNAUTHORIZED);
    }

    // Check that the token was issued for the correct client
    String clientId = authResult.getToken().getIssuedFor();
    if (!CLIENT_BODHI_DEV_CONSOLE.equals(clientId)) {
      throw new ResourceProviderException("unauthorized client", Response.Status.UNAUTHORIZED);
    }

    // Verify the client exists and is enabled
    RealmModel realm = session.getContext().getRealm();
    ClientModel client = realm.getClientByClientId(clientId);
    if (client == null) {
      throw new ResourceProviderException("client not found", Response.Status.BAD_REQUEST);
    }
    if (!client.isEnabled()) {
      throw new ResourceProviderException("client not enabled", Response.Status.UNAUTHORIZED);
    }
  }

  private void setupServiceAccountUser(RealmModel realm, ClientModel client) {
    // Create the service account user explicitly
    UserModel serviceAccountUser = session.users().getServiceAccount(client);
    if (serviceAccountUser == null) {
      // If service account doesn't exist, create it manually
      serviceAccountUser = session.users().addUser(realm, "service-account-" + client.getClientId());
      serviceAccountUser.setEnabled(true);
      serviceAccountUser.setServiceAccountClientLink(client.getId());
      // Set required attributes for service account
      serviceAccountUser.setSingleAttribute("service_account_client_id", client.getClientId());
    }
  }

  public static class ClientRequest {
    @JsonProperty("name")
    public String name;
    @JsonProperty("description")
    public String description;
    @JsonProperty("redirect_uris")
    public List<String> redirectUris;

    public ClientRequest() {
    }

    public ClientRequest(String name, String description, List<String> redirectUris) {
      this.name = name;
      this.description = description;
      this.redirectUris = redirectUris;
    }
  }

  public static class MakeAdminRequest {
    @JsonProperty("username")
    private String username;
  }

  public static class AddToGroupRequest {
    @JsonProperty("username")
    private String username;
    @JsonProperty("group")
    private String group;
    @JsonProperty("add")
    private boolean add;
  }

  public static class ErrorResponse {
    @JsonProperty("error")
    public final String error;

    public ErrorResponse(String error) {
      this.error = error;
    }
  }

  public static class SuccessResponse {
    @JsonProperty("message")
    public final String message;

    public SuccessResponse(String message) {
      this.message = message;
    }
  }

  public static class ClientSecretPair {
    @JsonProperty("client_id")
    public final String clientId;
    @JsonProperty("client_secret")
    public final String clientSecret;

    public ClientSecretPair(String clientId, String clientSecret) {
      this.clientId = clientId;
      this.clientSecret = clientSecret;
    }
  }

  public static class ResourceClientResponse {
    @JsonProperty("client_id")
    public final String clientId;
    @JsonProperty("client_secret")
    public final String clientSecret;
    @JsonProperty("scope")
    public final String scope;

    public ResourceClientResponse(String clientId, String clientSecret, String scope) {
      this.clientId = clientId;
      this.clientSecret = clientSecret;
      this.scope = scope;
    }
  }

  public static class HasAdminResponse {
    @JsonProperty("has_admin")
    public final boolean hasAdmin;

    public HasAdminResponse(boolean hasAdmin) {
      this.hasAdmin = hasAdmin;
    }
  }

  public static class ResourceProviderException extends Exception {
    private String message;
    private Response.Status status;

    public ResourceProviderException(String message, Response.Status status) {
      this.message = message;
      this.status = status;
    }
  }

  public static class AppAccessRequest {
    @JsonProperty("app_client_id")
    private String appClientId;
  }

  public static class AppAccessResponse {
    @JsonProperty("scope")
    public final String scope;

    public AppAccessResponse(String scope) {
      this.scope = scope;
    }
  }
}