<configuration>
  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
    </encoder>
  </appender>

  <root level="INFO">
    <appender-ref ref="STDOUT" />
  </root>

  <!-- Test containers logging -->
  <logger name="org.testcontainers" level="INFO" />
  <logger name="dasniko.testcontainers.keycloak" level="INFO" />
  <logger name="com.github.dockerjava" level="WARN" />
  <logger name="tc" level="INFO" />

  <!-- Application logging -->
  <logger name="com.bodhisearch" level="DEBUG" />

  <!-- Keycloak logging -->
  <logger name="org.keycloak" level="INFO" />

  <!-- HTTP client logging -->
  <logger name="org.apache.http" level="INFO" />
  <logger name="io.restassured" level="INFO" />

  <!-- Reduce noise from other libraries -->
  <logger name="org.apache.maven" level="WARN" />
  <logger name="org.eclipse.aether" level="WARN" />
  <logger name="org.junit" level="INFO" />
</configuration>