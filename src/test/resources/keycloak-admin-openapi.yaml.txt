---
openapi: 3.0.3
info:
  title: Keycloak Admin REST API
  description: This is a REST API reference for the Keycloak Admin REST API.
  version: "1.0"
tags:
- name: Attack Detection
- name: Authentication Management
- name: Client Attribute Certificate
- name: Client Initial Access
- name: Client Registration Policy
- name: Client Role Mappings
- name: Client Scopes
- name: Clients
- name: Component
- name: Groups
- name: Identity Providers
- name: Key
- name: Protocol Mappers
- name: Realms Admin
- name: Role Mapper
- name: Roles
- name: Roles (by ID)
- name: Scope Mappings
- name: Users
paths:
  /admin/realms:
    get:
      tags:
      - Realms Admin
      summary: Get accessible realms Returns a list of accessible realms. The list
        is filtered based on what realms the caller is allowed to view.
      parameters:
      - name: briefRepresentation
        in: query
        schema:
          default: false
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RealmRepresentation'
    post:
      tags:
      - Realms Admin
      summary: Import a realm. Imports a realm from a full representation of that
        realm.
      description: Realm name must be unique.
      requestBody:
        content:
          application/json:
            schema:
              format: binary
              type: string
      responses:
        "201":
          description: Created
  /admin/realms/{realm}:
    get:
      tags:
      - Realms Admin
      summary: Get the top-level representation of the realm It will not include nested
        information like User and Client representations.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RealmRepresentation'
    put:
      tags:
      - Realms Admin
      summary: "Update the top-level information of the realm Any user, roles or client\
        \ information in the representation will be ignored."
      description: This will only update top-level attributes of the realm.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RealmRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Realms Admin
      summary: Delete the realm
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/admin-events:
    get:
      tags:
      - Realms Admin
      summary: "Get admin events Returns all admin events, or filters events based\
        \ on URL query parameters listed here"
      parameters:
      - name: authClient
        in: query
        schema:
          type: string
      - name: authIpAddress
        in: query
        schema:
          type: string
      - name: authRealm
        in: query
        schema:
          type: string
      - name: authUser
        in: query
        description: user id
        schema:
          type: string
      - name: dateFrom
        in: query
        schema:
          type: string
      - name: dateTo
        in: query
        schema:
          type: string
      - name: first
        in: query
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        description: Maximum results size (defaults to 100)
        schema:
          format: int32
          type: integer
      - name: operationTypes
        in: query
        schema:
          type: array
          items:
            type: string
      - name: resourcePath
        in: query
        schema:
          type: string
      - name: resourceTypes
        in: query
        schema:
          type: array
          items:
            type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdminEventRepresentation'
    delete:
      tags:
      - Realms Admin
      summary: Delete all admin events
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/attack-detection/brute-force/users:
    delete:
      tags:
      - Attack Detection
      summary: Clear any user login failures for all users This can release temporary
        disabled users
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/attack-detection/brute-force/users/{userId}:
    get:
      tags:
      - Attack Detection
      summary: Get status of a username in brute force detection
      parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
    delete:
      tags:
      - Attack Detection
      summary: Clear any user login failures for the user This can release temporary
        disabled user
      parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/authenticator-providers:
    get:
      tags:
      - Authentication Management
      summary: Get authenticator providers Returns a stream of authenticator providers.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties: {}
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/client-authenticator-providers:
    get:
      tags:
      - Authentication Management
      summary: Get client authenticator providers Returns a stream of client authenticator
        providers.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties: {}
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/config:
    post:
      tags:
      - Authentication Management
      summary: Create new authenticator configuration
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthenticatorConfigRepresentation'
      responses:
        "200":
          description: OK
      deprecated: true
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/config-description/{providerId}:
    get:
      tags:
      - Authentication Management
      summary: Get authenticator provider's configuration description
      parameters:
      - name: providerId
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticatorConfigInfoRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/config/{id}:
    get:
      tags:
      - Authentication Management
      summary: Get authenticator configuration
      parameters:
      - name: id
        in: path
        description: Configuration id
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticatorConfigRepresentation'
    put:
      tags:
      - Authentication Management
      summary: Update authenticator configuration
      parameters:
      - name: id
        in: path
        description: Configuration id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthenticatorConfigRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Authentication Management
      summary: Delete authenticator configuration
      parameters:
      - name: id
        in: path
        description: Configuration id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/executions:
    post:
      tags:
      - Authentication Management
      summary: Add new authentication execution
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthenticationExecutionRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/executions/{executionId}:
    get:
      tags:
      - Authentication Management
      summary: Get Single Execution
      parameters:
      - name: executionId
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Authentication Management
      summary: Delete execution
      parameters:
      - name: executionId
        in: path
        description: Execution id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/executions/{executionId}/config:
    post:
      tags:
      - Authentication Management
      summary: Update execution with new configuration
      parameters:
      - name: executionId
        in: path
        description: Execution id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthenticatorConfigRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/executions/{executionId}/config/{id}:
    get:
      tags:
      - Authentication Management
      summary: Get execution's configuration
      parameters:
      - name: executionId
        in: path
        description: Execution id
        required: true
        schema:
          type: string
      - name: id
        in: path
        description: Configuration id
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticatorConfigRepresentation'
      deprecated: true
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/executions/{executionId}/lower-priority:
    post:
      tags:
      - Authentication Management
      summary: Lower execution's priority
      parameters:
      - name: executionId
        in: path
        description: Execution id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/executions/{executionId}/raise-priority:
    post:
      tags:
      - Authentication Management
      summary: Raise execution's priority
      parameters:
      - name: executionId
        in: path
        description: Execution id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/flows:
    get:
      tags:
      - Authentication Management
      summary: Get authentication flows Returns a stream of authentication flows.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AuthenticationFlowRepresentation'
    post:
      tags:
      - Authentication Management
      summary: Create a new authentication flow
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthenticationFlowRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/flows/{flowAlias}/copy:
    post:
      tags:
      - Authentication Management
      summary: Copy existing authentication flow under a new name The new name is
        given as 'newName' attribute of the passed JSON object
      parameters:
      - name: flowAlias
        in: path
        description: name of the existing authentication flow
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: string
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/flows/{flowAlias}/executions:
    get:
      tags:
      - Authentication Management
      summary: Get authentication executions for a flow
      parameters:
      - name: flowAlias
        in: path
        description: Flow alias
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
    put:
      tags:
      - Authentication Management
      summary: Update authentication executions of a Flow
      parameters:
      - name: flowAlias
        in: path
        description: Flow alias
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthenticationExecutionInfoRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/flows/{flowAlias}/executions/execution:
    post:
      tags:
      - Authentication Management
      summary: Add new authentication execution to a flow
      parameters:
      - name: flowAlias
        in: path
        description: Alias of parent flow
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: string
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/flows/{flowAlias}/executions/flow:
    post:
      tags:
      - Authentication Management
      summary: Add new flow with new execution to existing flow
      parameters:
      - name: flowAlias
        in: path
        description: Alias of parent authentication flow
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: string
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/flows/{id}:
    get:
      tags:
      - Authentication Management
      summary: Get authentication flow for id
      parameters:
      - name: id
        in: path
        description: Flow id
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthenticationFlowRepresentation'
    put:
      tags:
      - Authentication Management
      summary: Update an authentication flow
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AuthenticationFlowRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Authentication Management
      summary: Delete an authentication flow
      parameters:
      - name: id
        in: path
        description: Flow id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/form-action-providers:
    get:
      tags:
      - Authentication Management
      summary: Get form action providers Returns a stream of form action providers.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties: {}
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/form-providers:
    get:
      tags:
      - Authentication Management
      summary: Get form providers Returns a stream of form providers.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties: {}
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/per-client-config-description:
    get:
      tags:
      - Authentication Management
      summary: Get configuration descriptions for all clients
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: array
                  items:
                    $ref: '#/components/schemas/ConfigPropertyRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/register-required-action:
    post:
      tags:
      - Authentication Management
      summary: Register a new required actions
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/required-actions:
    get:
      tags:
      - Authentication Management
      summary: Get required actions Returns a stream of required actions.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RequiredActionProviderRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/required-actions/{alias}:
    get:
      tags:
      - Authentication Management
      summary: Get required action for alias
      parameters:
      - name: alias
        in: path
        description: Alias of required action
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RequiredActionProviderRepresentation'
    put:
      tags:
      - Authentication Management
      summary: Update required action
      parameters:
      - name: alias
        in: path
        description: Alias of required action
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RequiredActionProviderRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Authentication Management
      summary: Delete required action
      parameters:
      - name: alias
        in: path
        description: Alias of required action
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/required-actions/{alias}/lower-priority:
    post:
      tags:
      - Authentication Management
      summary: Lower required action's priority
      parameters:
      - name: alias
        in: path
        description: Alias of required action
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/required-actions/{alias}/raise-priority:
    post:
      tags:
      - Authentication Management
      summary: Raise required action's priority
      parameters:
      - name: alias
        in: path
        description: Alias of required action
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/authentication/unregistered-required-actions:
    get:
      tags:
      - Authentication Management
      summary: Get unregistered required actions Returns a stream of unregistered
        required actions.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties:
                    type: string
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-description-converter:
    post:
      tags:
      - Realms Admin
      summary: Base path for importing clients under this realm.
      requestBody:
        content:
          application/json:
            schema:
              type: string
          application/xml:
            schema:
              type: string
          text/plain:
            schema:
              type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-policies/policies:
    get:
      tags:
      - Realms Admin
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientPoliciesRepresentation'
    put:
      tags:
      - Realms Admin
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientPoliciesRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-policies/profiles:
    get:
      tags:
      - Realms Admin
      parameters:
      - name: include-global-profiles
        in: query
        schema:
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientProfilesRepresentation'
    put:
      tags:
      - Realms Admin
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientProfilesRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-registration-policy/providers:
    get:
      tags:
      - Client Registration Policy
      summary: Base path for retrieve providers with the configProperties properly
        filled
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ComponentTypeRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes:
    get:
      tags:
      - Client Scopes
      summary: Get client scopes belonging to the realm Returns a list of client scopes
        belonging to the realm
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClientScopeRepresentation'
    post:
      tags:
      - Client Scopes
      summary: Create a new client scope Client Scope’s name must be unique!
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientScopeRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}:
    get:
      tags:
      - Client Scopes
      summary: Get representation of the client scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientScopeRepresentation'
    put:
      tags:
      - Client Scopes
      summary: Update the client scope
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientScopeRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Client Scopes
      summary: Delete the client scope
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/protocol-mappers/add-models:
    post:
      tags:
      - Protocol Mappers
      summary: Create multiple mappers
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ProtocolMapperRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/protocol-mappers/models:
    get:
      tags:
      - Protocol Mappers
      summary: Get mappers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProtocolMapperRepresentation'
    post:
      tags:
      - Protocol Mappers
      summary: Create a mapper
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProtocolMapperRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/protocol-mappers/models/{id}:
    get:
      tags:
      - Protocol Mappers
      summary: Get mapper by id
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProtocolMapperRepresentation'
    put:
      tags:
      - Protocol Mappers
      summary: Update the mapper
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProtocolMapperRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Protocol Mappers
      summary: Delete the mapper
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/protocol-mappers/protocol/{protocol}:
    get:
      tags:
      - Protocol Mappers
      summary: Get mappers by name for a specific protocol
      parameters:
      - name: protocol
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProtocolMapperRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/scope-mappings:
    get:
      tags:
      - Scope Mappings
      summary: Get all scope mappings for the client
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MappingsRepresentation'
      deprecated: true
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/scope-mappings/clients/{client}:
    get:
      tags:
      - Scope Mappings
      summary: Get the roles associated with a client's scope Returns roles for the
        client.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Scope Mappings
      summary: Add client-level roles to the client's scope
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Scope Mappings
      summary: Remove client-level roles from the client's scope.
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/scope-mappings/clients/{client}/available:
    get:
      tags:
      - Scope Mappings
      summary: The available client-level roles Returns the roles for the client that
        can be associated with the client's scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/scope-mappings/clients/{client}/composite:
    get:
      tags:
      - Scope Mappings
      summary: Get effective client roles Returns the roles for the client that are
        associated with the client's scope.
      parameters:
      - name: briefRepresentation
        in: query
        description: "if false, return roles with their attributes"
        schema:
          default: true
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/scope-mappings/realm:
    get:
      tags:
      - Scope Mappings
      summary: Get realm-level roles associated with the client's scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Scope Mappings
      summary: Add a set of realm-level roles to the client's scope
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Scope Mappings
      summary: Remove a set of realm-level roles from the client's scope
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/scope-mappings/realm/available:
    get:
      tags:
      - Scope Mappings
      summary: Get realm-level roles that are available to attach to this client's
        scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-scopes/{client-scope-id}/scope-mappings/realm/composite:
    get:
      tags:
      - Scope Mappings
      summary: Get effective realm-level roles associated with the client’s scope
        What this does is recurse any composite roles associated with the client’s
        scope and adds the roles to this lists.
      description: The method is really to show a comprehensive total view of realm-level
        roles associated with the client.
      parameters:
      - name: briefRepresentation
        in: query
        description: "if false, return roles with their attributes"
        schema:
          default: true
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-session-stats:
    get:
      tags:
      - Realms Admin
      summary: Get client session stats Returns a JSON map.
      description: "The key is the client id, the value is the number of sessions\
        \ that currently are active with that client. Only clients that actually have\
        \ a session associated with them will be in this map."
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties:
                    type: string
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates:
    get:
      tags:
      - Client Scopes
      summary: Get client scopes belonging to the realm Returns a list of client scopes
        belonging to the realm
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClientScopeRepresentation'
    post:
      tags:
      - Client Scopes
      summary: Create a new client scope Client Scope’s name must be unique!
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientScopeRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}:
    get:
      tags:
      - Client Scopes
      summary: Get representation of the client scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientScopeRepresentation'
    put:
      tags:
      - Client Scopes
      summary: Update the client scope
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientScopeRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Client Scopes
      summary: Delete the client scope
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/protocol-mappers/add-models:
    post:
      tags:
      - Protocol Mappers
      summary: Create multiple mappers
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ProtocolMapperRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/protocol-mappers/models:
    get:
      tags:
      - Protocol Mappers
      summary: Get mappers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProtocolMapperRepresentation'
    post:
      tags:
      - Protocol Mappers
      summary: Create a mapper
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProtocolMapperRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/protocol-mappers/models/{id}:
    get:
      tags:
      - Protocol Mappers
      summary: Get mapper by id
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProtocolMapperRepresentation'
    put:
      tags:
      - Protocol Mappers
      summary: Update the mapper
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProtocolMapperRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Protocol Mappers
      summary: Delete the mapper
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/protocol-mappers/protocol/{protocol}:
    get:
      tags:
      - Protocol Mappers
      summary: Get mappers by name for a specific protocol
      parameters:
      - name: protocol
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProtocolMapperRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/scope-mappings:
    get:
      tags:
      - Scope Mappings
      summary: Get all scope mappings for the client
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MappingsRepresentation'
      deprecated: true
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/scope-mappings/clients/{client}:
    get:
      tags:
      - Scope Mappings
      summary: Get the roles associated with a client's scope Returns roles for the
        client.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Scope Mappings
      summary: Add client-level roles to the client's scope
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Scope Mappings
      summary: Remove client-level roles from the client's scope.
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/scope-mappings/clients/{client}/available:
    get:
      tags:
      - Scope Mappings
      summary: The available client-level roles Returns the roles for the client that
        can be associated with the client's scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/scope-mappings/clients/{client}/composite:
    get:
      tags:
      - Scope Mappings
      summary: Get effective client roles Returns the roles for the client that are
        associated with the client's scope.
      parameters:
      - name: briefRepresentation
        in: query
        description: "if false, return roles with their attributes"
        schema:
          default: true
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/scope-mappings/realm:
    get:
      tags:
      - Scope Mappings
      summary: Get realm-level roles associated with the client's scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Scope Mappings
      summary: Add a set of realm-level roles to the client's scope
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Scope Mappings
      summary: Remove a set of realm-level roles from the client's scope
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/scope-mappings/realm/available:
    get:
      tags:
      - Scope Mappings
      summary: Get realm-level roles that are available to attach to this client's
        scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/client-templates/{client-scope-id}/scope-mappings/realm/composite:
    get:
      tags:
      - Scope Mappings
      summary: Get effective realm-level roles associated with the client’s scope
        What this does is recurse any composite roles associated with the client’s
        scope and adds the roles to this lists.
      description: The method is really to show a comprehensive total view of realm-level
        roles associated with the client.
      parameters:
      - name: briefRepresentation
        in: query
        description: "if false, return roles with their attributes"
        schema:
          default: true
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-scope-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients:
    get:
      tags:
      - Clients
      summary: Get clients belonging to the realm.
      description: "If a client can’t be retrieved from the storage due to a problem\
        \ with the underlying storage, it is silently removed from the returned list.\
        \ This ensures that concurrent modifications to the list don’t prevent callers\
        \ from retrieving this list."
      parameters:
      - name: clientId
        in: query
        description: filter by clientId
        schema:
          type: string
      - name: first
        in: query
        description: the first result
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        description: the max results to return
        schema:
          format: int32
          type: integer
      - name: q
        in: query
        schema:
          type: string
      - name: search
        in: query
        description: whether this is a search query or a getClientById query
        schema:
          default: false
          type: boolean
      - name: viewableOnly
        in: query
        description: filter clients that cannot be viewed in full by admin
        schema:
          default: false
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClientRepresentation'
    post:
      tags:
      - Clients
      summary: Create a new client Client’s client_id must be unique!
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientRepresentation'
      responses:
        "201":
          description: Created
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients-initial-access:
    get:
      tags:
      - Client Initial Access
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClientInitialAccessPresentation'
    post:
      tags:
      - Client Initial Access
      summary: Create a new initial access token.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientInitialAccessCreatePresentation'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientInitialAccessCreatePresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients-initial-access/{id}:
    delete:
      tags:
      - Client Initial Access
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}:
    get:
      tags:
      - Clients
      summary: Get representation of the client
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientRepresentation'
    put:
      tags:
      - Clients
      summary: Update the client
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClientRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Clients
      summary: Delete the client
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server:
    get:
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceServerRepresentation'
    put:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceServerRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/import:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceServerRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/permission:
    get:
      parameters:
      - name: fields
        in: query
        schema:
          type: string
      - name: first
        in: query
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        schema:
          format: int32
          type: integer
      - name: name
        in: query
        schema:
          type: string
      - name: owner
        in: query
        schema:
          type: string
      - name: permission
        in: query
        schema:
          type: boolean
      - name: policyId
        in: query
        schema:
          type: string
      - name: resource
        in: query
        schema:
          type: string
      - name: scope
        in: query
        schema:
          type: string
      - name: type
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AbstractPolicyRepresentation'
        "204":
          description: No Content
    post:
      requestBody:
        content:
          application/json:
            schema:
              type: string
      responses:
        "201":
          description: Created
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/permission/evaluate:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PolicyEvaluationRequest'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PolicyEvaluationResponse'
        "500":
          description: Internal Server Error
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/permission/providers:
    get:
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PolicyProviderRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/permission/search:
    get:
      parameters:
      - name: fields
        in: query
        schema:
          type: string
      - name: name
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AbstractPolicyRepresentation'
        "204":
          description: No Content
        "400":
          description: Bad Request
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/policy:
    get:
      parameters:
      - name: fields
        in: query
        schema:
          type: string
      - name: first
        in: query
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        schema:
          format: int32
          type: integer
      - name: name
        in: query
        schema:
          type: string
      - name: owner
        in: query
        schema:
          type: string
      - name: permission
        in: query
        schema:
          type: boolean
      - name: policyId
        in: query
        schema:
          type: string
      - name: resource
        in: query
        schema:
          type: string
      - name: scope
        in: query
        schema:
          type: string
      - name: type
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AbstractPolicyRepresentation'
        "204":
          description: No Content
    post:
      requestBody:
        content:
          application/json:
            schema:
              type: string
      responses:
        "201":
          description: Created
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/policy/evaluate:
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PolicyEvaluationRequest'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PolicyEvaluationResponse'
        "500":
          description: Internal Server Error
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/policy/providers:
    get:
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PolicyProviderRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/policy/search:
    get:
      parameters:
      - name: fields
        in: query
        schema:
          type: string
      - name: name
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AbstractPolicyRepresentation'
        "204":
          description: No Content
        "400":
          description: Bad Request
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/resource:
    get:
      parameters:
      - name: _id
        in: query
        schema:
          type: string
      - name: deep
        in: query
        schema:
          type: boolean
      - name: exactName
        in: query
        schema:
          type: boolean
      - name: first
        in: query
        schema:
          format: int32
          type: integer
      - name: matchingUri
        in: query
        schema:
          type: boolean
      - name: max
        in: query
        schema:
          format: int32
          type: integer
      - name: name
        in: query
        schema:
          type: string
      - name: owner
        in: query
        schema:
          type: string
      - name: scope
        in: query
        schema:
          type: string
      - name: type
        in: query
        schema:
          type: string
      - name: uri
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ResourceRepresentation'
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRepresentation'
      responses:
        "201":
          description: Created
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceRepresentation'
        "400":
          description: Bad Request
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: _id
      in: query
      schema:
        type: string
    - name: deep
      in: query
      schema:
        type: boolean
    - name: exactName
      in: query
      schema:
        type: boolean
    - name: first
      in: query
      schema:
        format: int32
        type: integer
    - name: matchingUri
      in: query
      schema:
        type: boolean
    - name: max
      in: query
      schema:
        format: int32
        type: integer
    - name: name
      in: query
      schema:
        type: string
    - name: owner
      in: query
      schema:
        type: string
    - name: scope
      in: query
      schema:
        type: string
    - name: type
      in: query
      schema:
        type: string
    - name: uri
      in: query
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/resource/search:
    get:
      parameters:
      - name: name
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceRepresentation'
        "400":
          description: Bad Request
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: _id
      in: query
      schema:
        type: string
    - name: deep
      in: query
      schema:
        type: boolean
    - name: exactName
      in: query
      schema:
        type: boolean
    - name: first
      in: query
      schema:
        format: int32
        type: integer
    - name: matchingUri
      in: query
      schema:
        type: boolean
    - name: max
      in: query
      schema:
        format: int32
        type: integer
    - name: name
      in: query
      schema:
        type: string
    - name: owner
      in: query
      schema:
        type: string
    - name: scope
      in: query
      schema:
        type: string
    - name: type
      in: query
      schema:
        type: string
    - name: uri
      in: query
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/resource/{resource-id}:
    get:
      parameters:
      - name: resource-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceRepresentation'
        "404":
          description: Not found
    put:
      parameters:
      - name: resource-id
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRepresentation'
      responses:
        "204":
          description: No Content
        "404":
          description: Not Found
    delete:
      parameters:
      - name: resource-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
        "404":
          description: Not Found
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: _id
      in: query
      schema:
        type: string
    - name: deep
      in: query
      schema:
        type: boolean
    - name: exactName
      in: query
      schema:
        type: boolean
    - name: first
      in: query
      schema:
        format: int32
        type: integer
    - name: matchingUri
      in: query
      schema:
        type: boolean
    - name: max
      in: query
      schema:
        format: int32
        type: integer
    - name: name
      in: query
      schema:
        type: string
    - name: owner
      in: query
      schema:
        type: string
    - name: scope
      in: query
      schema:
        type: string
    - name: type
      in: query
      schema:
        type: string
    - name: uri
      in: query
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/resource/{resource-id}/attributes:
    get:
      parameters:
      - name: resource-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: _id
      in: query
      schema:
        type: string
    - name: deep
      in: query
      schema:
        type: boolean
    - name: exactName
      in: query
      schema:
        type: boolean
    - name: first
      in: query
      schema:
        format: int32
        type: integer
    - name: matchingUri
      in: query
      schema:
        type: boolean
    - name: max
      in: query
      schema:
        format: int32
        type: integer
    - name: name
      in: query
      schema:
        type: string
    - name: owner
      in: query
      schema:
        type: string
    - name: scope
      in: query
      schema:
        type: string
    - name: type
      in: query
      schema:
        type: string
    - name: uri
      in: query
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/resource/{resource-id}/permissions:
    get:
      parameters:
      - name: resource-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PolicyRepresentation'
        "404":
          description: Not found
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: _id
      in: query
      schema:
        type: string
    - name: deep
      in: query
      schema:
        type: boolean
    - name: exactName
      in: query
      schema:
        type: boolean
    - name: first
      in: query
      schema:
        format: int32
        type: integer
    - name: matchingUri
      in: query
      schema:
        type: boolean
    - name: max
      in: query
      schema:
        format: int32
        type: integer
    - name: name
      in: query
      schema:
        type: string
    - name: owner
      in: query
      schema:
        type: string
    - name: scope
      in: query
      schema:
        type: string
    - name: type
      in: query
      schema:
        type: string
    - name: uri
      in: query
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/resource/{resource-id}/scopes:
    get:
      parameters:
      - name: resource-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ScopeRepresentation'
        "404":
          description: Not found
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: _id
      in: query
      schema:
        type: string
    - name: deep
      in: query
      schema:
        type: boolean
    - name: exactName
      in: query
      schema:
        type: boolean
    - name: first
      in: query
      schema:
        format: int32
        type: integer
    - name: matchingUri
      in: query
      schema:
        type: boolean
    - name: max
      in: query
      schema:
        format: int32
        type: integer
    - name: name
      in: query
      schema:
        type: string
    - name: owner
      in: query
      schema:
        type: string
    - name: scope
      in: query
      schema:
        type: string
    - name: type
      in: query
      schema:
        type: string
    - name: uri
      in: query
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/scope:
    get:
      parameters:
      - name: first
        in: query
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        schema:
          format: int32
          type: integer
      - name: name
        in: query
        schema:
          type: string
      - name: scopeId
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ScopeRepresentation'
    post:
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ScopeRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/scope/search:
    get:
      parameters:
      - name: name
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ScopeRepresentation'
        "204":
          description: No Content
        "400":
          description: Bad Request
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/scope/{scope-id}:
    get:
      parameters:
      - name: scope-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ScopeRepresentation'
        "404":
          description: Not found
    put:
      parameters:
      - name: scope-id
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ScopeRepresentation'
      responses:
        "200":
          description: OK
    delete:
      parameters:
      - name: scope-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/scope/{scope-id}/permissions:
    get:
      parameters:
      - name: scope-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/PolicyRepresentation'
        "404":
          description: Not found
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/scope/{scope-id}/resources:
    get:
      parameters:
      - name: scope-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ResourceRepresentation'
        "404":
          description: Not found
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/authz/resource-server/settings:
    get:
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResourceServerRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/certificates/{attr}:
    get:
      tags:
      - Client Attribute Certificate
      summary: Get key info
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CertificateRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: attr
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/certificates/{attr}/download:
    post:
      tags:
      - Client Attribute Certificate
      summary: "Get a keystore file for the client, containing private key and public\
        \ certificate"
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeyStoreConfig'
      responses:
        "200":
          description: OK
          content:
            application/octet-stream:
              schema:
                format: binary
                type: string
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: attr
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/certificates/{attr}/generate:
    post:
      tags:
      - Client Attribute Certificate
      summary: Generate a new certificate with new key pair
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CertificateRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: attr
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/certificates/{attr}/generate-and-download:
    post:
      tags:
      - Client Attribute Certificate
      summary: |-
        Generate a new keypair and certificate, and get the private key file

        Generates a keypair and certificate and serves the private key in a specified keystore format.
        Only generated public certificate is saved in Keycloak DB - the private key is not.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/KeyStoreConfig'
      responses:
        "200":
          description: OK
          content:
            application/octet-stream:
              schema:
                format: binary
                type: string
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: attr
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/certificates/{attr}/upload:
    post:
      tags:
      - Client Attribute Certificate
      summary: Upload certificate and eventually private key
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CertificateRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: attr
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/certificates/{attr}/upload-certificate:
    post:
      tags:
      - Client Attribute Certificate
      summary: "Upload only certificate, not private key"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CertificateRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: attr
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/client-secret:
    get:
      tags:
      - Clients
      summary: Get the client secret
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CredentialRepresentation'
    post:
      tags:
      - Clients
      summary: Generate a new secret for the client
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CredentialRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/client-secret/rotated:
    get:
      tags:
      - Clients
      summary: Get the rotated client secret
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CredentialRepresentation'
    delete:
      tags:
      - Clients
      summary: Invalidate the rotated secret for the client
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/default-client-scopes:
    get:
      tags:
      - Clients
      summary: Get default client scopes.  Only name and ids are returned.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClientScopeRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/default-client-scopes/{clientScopeId}:
    put:
      tags:
      - Clients
      parameters:
      - name: clientScopeId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Clients
      parameters:
      - name: clientScopeId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/evaluate-scopes/generate-example-access-token:
    get:
      tags:
      - Clients
      summary: Create JSON with payload of example access token
      parameters:
      - name: scope
        in: query
        schema:
          type: string
      - name: userId
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessToken'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/evaluate-scopes/generate-example-id-token:
    get:
      tags:
      - Clients
      summary: Create JSON with payload of example id token
      parameters:
      - name: scope
        in: query
        schema:
          type: string
      - name: userId
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IDToken'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/evaluate-scopes/generate-example-userinfo:
    get:
      tags:
      - Clients
      summary: Create JSON with payload of example user info
      parameters:
      - name: scope
        in: query
        schema:
          type: string
      - name: userId
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/evaluate-scopes/protocol-mappers:
    get:
      tags:
      - Clients
      summary: "Return list of all protocol mappers, which will be used when generating\
        \ tokens issued for particular client."
      description: This means protocol mappers assigned to this client directly and
        protocol mappers assigned to all client scopes of this client.
      parameters:
      - name: scope
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProtocolMapperEvaluationRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/evaluate-scopes/scope-mappings/{roleContainerId}/granted:
    get:
      tags:
      - Clients
      summary: "Get effective scope mapping of all roles of particular role container,\
        \ which this client is defacto allowed to have in the accessToken issued for\
        \ him."
      description: "This contains scope mappings, which this client has directly,\
        \ as well as scope mappings, which are granted to all client scopes, which\
        \ are linked with this client."
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: roleContainerId
      in: path
      description: either realm name OR client UUID
      required: true
      schema:
        type: string
    - name: scope
      in: query
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/evaluate-scopes/scope-mappings/{roleContainerId}/not-granted:
    get:
      tags:
      - Clients
      summary: "Get roles, which this client doesn't have scope for and can't have\
        \ them in the accessToken issued for him."
      description: "Defacto all the other roles of particular role container, which\
        \ are not in {@link #getGrantedScopeMappings()}"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: roleContainerId
      in: path
      description: either realm name OR client UUID
      required: true
      schema:
        type: string
    - name: scope
      in: query
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/installation/providers/{providerId}:
    get:
      tags:
      - Clients
      parameters:
      - name: providerId
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/management/permissions:
    get:
      tags:
      - Clients
      summary: Return object stating whether client Authorization permissions have
        been initialized or not and a reference
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    put:
      tags:
      - Clients
      summary: Return object stating whether client Authorization permissions have
        been initialized or not and a reference
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementPermissionReference'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/nodes:
    post:
      tags:
      - Clients
      summary: Register a cluster node with the client Manually register cluster node
        to this client - usually it’s not needed to call this directly as adapter
        should handle by sending registration request to Keycloak
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/nodes/{node}:
    delete:
      tags:
      - Clients
      summary: Unregister a cluster node from the client
      parameters:
      - name: node
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/offline-session-count:
    get:
      tags:
      - Clients
      summary: "Get application offline session count Returns a number of offline\
        \ user sessions associated with this client { \"count\": number }"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  format: int64
                  type: integer
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/offline-sessions:
    get:
      tags:
      - Clients
      summary: Get offline sessions for client Returns a list of offline user sessions
        associated with this client
      parameters:
      - name: first
        in: query
        description: Paging offset
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        description: Maximum results size (defaults to 100)
        schema:
          format: int32
          type: integer
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserSessionRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/optional-client-scopes:
    get:
      tags:
      - Clients
      summary: Get optional client scopes.  Only name and ids are returned.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClientScopeRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/optional-client-scopes/{clientScopeId}:
    put:
      tags:
      - Clients
      parameters:
      - name: clientScopeId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Clients
      parameters:
      - name: clientScopeId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/protocol-mappers/add-models:
    post:
      tags:
      - Protocol Mappers
      summary: Create multiple mappers
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/ProtocolMapperRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/protocol-mappers/models:
    get:
      tags:
      - Protocol Mappers
      summary: Get mappers
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProtocolMapperRepresentation'
    post:
      tags:
      - Protocol Mappers
      summary: Create a mapper
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProtocolMapperRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/protocol-mappers/models/{id}:
    get:
      tags:
      - Protocol Mappers
      summary: Get mapper by id
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProtocolMapperRepresentation'
    put:
      tags:
      - Protocol Mappers
      summary: Update the mapper
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProtocolMapperRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Protocol Mappers
      summary: Delete the mapper
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/protocol-mappers/protocol/{protocol}:
    get:
      tags:
      - Protocol Mappers
      summary: Get mappers by name for a specific protocol
      parameters:
      - name: protocol
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ProtocolMapperRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/push-revocation:
    post:
      tags:
      - Clients
      summary: "Push the client's revocation policy to its admin URL If the client\
        \ has an admin URL, push revocation policy to it."
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GlobalRequestResult'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/registration-access-token:
    post:
      tags:
      - Clients
      summary: Generate a new registration access token for the client
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/roles:
    get:
      tags:
      - Roles
      summary: Get all roles for the realm or client
      parameters:
      - name: briefRepresentation
        in: query
        schema:
          default: true
          type: boolean
      - name: first
        in: query
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        schema:
          format: int32
          type: integer
      - name: search
        in: query
        schema:
          default: ""
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Roles
      summary: Create a new role for the realm or client
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/roles/{role-name}:
    get:
      tags:
      - Roles
      summary: Get a role by name
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleRepresentation'
    put:
      tags:
      - Roles
      summary: Update a role by name
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Roles
      summary: Delete a role by name
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/roles/{role-name}/composites:
    get:
      tags:
      - Roles
      summary: Get composites of the role
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Roles
      summary: Add a composite to the role
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Roles
      summary: Remove roles from the role's composite
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/roles/{role-name}/composites/clients/{client-uuid}:
    get:
      tags:
      - Roles
      summary: Get client-level roles for the client that are in the role's composite
      parameters:
      - name: client-uuid
        in: path
        required: true
        schema:
          type: string
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/roles/{role-name}/composites/realm:
    get:
      tags:
      - Roles
      summary: Get realm-level roles of the role's composite
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/roles/{role-name}/groups:
    get:
      tags:
      - Roles
      summary: Returns a stream of groups that have the specified role name
      parameters:
      - name: role-name
        in: path
        description: the role name.
        required: true
        schema:
          type: string
      - name: briefRepresentation
        in: query
        description: "if false, return a full representation of the {@code GroupRepresentation}\
          \ objects."
        schema:
          default: true
          type: boolean
      - name: first
        in: query
        description: "first result to return. Ignored if negative or {@code null}."
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        description: "maximum number of results to return. Ignored if negative or\
          \ {@code null}."
        schema:
          format: int32
          type: integer
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GroupRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/roles/{role-name}/management/permissions:
    get:
      tags:
      - Roles
      summary: Return object stating whether role Authorization permissions have been
        initialized or not and a reference
      parameters:
      - name: role-name
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    put:
      tags:
      - Roles
      summary: Return object stating whether role Authorization permissions have been
        initialized or not and a reference
      parameters:
      - name: role-name
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementPermissionReference'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/roles/{role-name}/users:
    get:
      tags:
      - Roles
      summary: Returns a stream of users that have the specified role name.
      parameters:
      - name: role-name
        in: path
        description: the role name.
        required: true
        schema:
          type: string
      - name: first
        in: query
        description: "first result to return. Ignored if negative or {@code null}."
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        description: "maximum number of results to return. Ignored if negative or\
          \ {@code null}."
        schema:
          format: int32
          type: integer
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/scope-mappings:
    get:
      tags:
      - Scope Mappings
      summary: Get all scope mappings for the client
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MappingsRepresentation'
      deprecated: true
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/scope-mappings/clients/{client}:
    get:
      tags:
      - Scope Mappings
      summary: Get the roles associated with a client's scope Returns roles for the
        client.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Scope Mappings
      summary: Add client-level roles to the client's scope
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Scope Mappings
      summary: Remove client-level roles from the client's scope.
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/scope-mappings/clients/{client}/available:
    get:
      tags:
      - Scope Mappings
      summary: The available client-level roles Returns the roles for the client that
        can be associated with the client's scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/scope-mappings/clients/{client}/composite:
    get:
      tags:
      - Scope Mappings
      summary: Get effective client roles Returns the roles for the client that are
        associated with the client's scope.
      parameters:
      - name: briefRepresentation
        in: query
        description: "if false, return roles with their attributes"
        schema:
          default: true
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/scope-mappings/realm:
    get:
      tags:
      - Scope Mappings
      summary: Get realm-level roles associated with the client's scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Scope Mappings
      summary: Add a set of realm-level roles to the client's scope
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Scope Mappings
      summary: Remove a set of realm-level roles from the client's scope
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/scope-mappings/realm/available:
    get:
      tags:
      - Scope Mappings
      summary: Get realm-level roles that are available to attach to this client's
        scope
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/scope-mappings/realm/composite:
    get:
      tags:
      - Scope Mappings
      summary: Get effective realm-level roles associated with the client’s scope
        What this does is recurse any composite roles associated with the client’s
        scope and adds the roles to this lists.
      description: The method is really to show a comprehensive total view of realm-level
        roles associated with the client.
      parameters:
      - name: briefRepresentation
        in: query
        description: "if false, return roles with their attributes"
        schema:
          default: true
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/service-account-user:
    get:
      tags:
      - Clients
      summary: Get a user dedicated to the service account
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/session-count:
    get:
      tags:
      - Clients
      summary: "Get application session count Returns a number of user sessions associated\
        \ with this client { \"count\": number }"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  format: int64
                  type: integer
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/test-nodes-available:
    get:
      tags:
      - Clients
      summary: Test if registered cluster nodes are available Tests availability by
        sending 'ping' request to all cluster nodes.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GlobalRequestResult'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/clients/{client-uuid}/user-sessions:
    get:
      tags:
      - Clients
      summary: |
        Get user sessions for client Returns a list of user sessions associated with this client
      parameters:
      - name: first
        in: query
        description: Paging offset
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        description: Maximum results size (defaults to 100)
        schema:
          format: int32
          type: integer
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserSessionRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: client-uuid
      in: path
      description: id of client (not client-id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/components:
    get:
      tags:
      - Component
      parameters:
      - name: name
        in: query
        schema:
          type: string
      - name: parent
        in: query
        schema:
          type: string
      - name: type
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ComponentRepresentation'
    post:
      tags:
      - Component
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComponentRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/components/{id}:
    get:
      tags:
      - Component
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ComponentRepresentation'
    put:
      tags:
      - Component
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComponentRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Component
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/components/{id}/sub-component-types:
    get:
      tags:
      - Component
      summary: List of subcomponent types that are available to configure for a particular
        parent component.
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      - name: type
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ComponentTypeRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/credential-registrators:
    get:
      tags:
      - Realms Admin
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/default-default-client-scopes:
    get:
      tags:
      - Realms Admin
      summary: Get realm default client scopes.  Only name and ids are returned.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClientScopeRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/default-default-client-scopes/{clientScopeId}:
    put:
      tags:
      - Realms Admin
      parameters:
      - name: clientScopeId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Realms Admin
      parameters:
      - name: clientScopeId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/default-groups:
    get:
      tags:
      - Realms Admin
      summary: Get group hierarchy.  Only name and ids are returned.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GroupRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/default-groups/{groupId}:
    put:
      tags:
      - Realms Admin
      parameters:
      - name: groupId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Realms Admin
      parameters:
      - name: groupId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/default-optional-client-scopes:
    get:
      tags:
      - Realms Admin
      summary: Get realm optional client scopes.  Only name and ids are returned.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ClientScopeRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/default-optional-client-scopes/{clientScopeId}:
    put:
      tags:
      - Realms Admin
      parameters:
      - name: clientScopeId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Realms Admin
      parameters:
      - name: clientScopeId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/events:
    get:
      tags:
      - Realms Admin
      summary: "Get events Returns all events, or filters them based on URL query\
        \ parameters listed here"
      parameters:
      - name: client
        in: query
        description: App or oauth client name
        schema:
          type: string
      - name: dateFrom
        in: query
        description: From date
        schema:
          type: string
      - name: dateTo
        in: query
        description: To date
        schema:
          type: string
      - name: first
        in: query
        description: Paging offset
        schema:
          format: int32
          type: integer
      - name: ipAddress
        in: query
        description: IP Address
        schema:
          type: string
      - name: max
        in: query
        description: Maximum results size (defaults to 100)
        schema:
          format: int32
          type: integer
      - name: type
        in: query
        description: The types of events to return
        schema:
          type: array
          items:
            type: string
      - name: user
        in: query
        description: User id
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/EventRepresentation'
    delete:
      tags:
      - Realms Admin
      summary: Delete all events
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/events/config:
    get:
      tags:
      - Realms Admin
      summary: Get the events provider configuration Returns JSON object with events
        provider configuration
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RealmEventsConfigRepresentation'
    put:
      tags:
      - Realms Admin
      description: Update the events provider Change the events provider and/or its
        configuration
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RealmEventsConfigRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/group-by-path/{path}:
    get:
      tags:
      - Realms Admin
      parameters:
      - name: path
        in: path
        required: true
        schema:
          type: array
          items:
            $ref: '#/components/schemas/PathSegment'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups:
    get:
      tags:
      - Groups
      summary: Get group hierarchy.  Only name and ids are returned.
      parameters:
      - name: briefRepresentation
        in: query
        schema:
          default: true
          type: boolean
      - name: exact
        in: query
        schema:
          default: "false"
          type: boolean
      - name: first
        in: query
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        schema:
          format: int32
          type: integer
      - name: populateHierarchy
        in: query
        schema:
          default: true
          type: boolean
      - name: q
        in: query
        schema:
          type: string
      - name: search
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GroupRepresentation'
    post:
      tags:
      - Groups
      summary: create or add a top level realm groupSet or create child.
      description: This will update the group and set the parent if it exists. Create
        it and set the parent if the group doesn’t exist.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/count:
    get:
      tags:
      - Groups
      summary: Returns the groups counts.
      parameters:
      - name: search
        in: query
        schema:
          type: string
      - name: top
        in: query
        schema:
          default: false
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  format: int64
                  type: integer
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}:
    get:
      tags:
      - Groups
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GroupRepresentation'
    put:
      tags:
      - Groups
      summary: "Update group, ignores subgroups."
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Groups
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}/children:
    get:
      tags:
      - Groups
      summary: Return a paginated list of subgroups that have a parent group corresponding
        to the group on the URL
      parameters:
      - name: briefRepresentation
        in: query
        schema:
          default: "false"
          type: boolean
      - name: first
        in: query
        schema:
          format: int32
          default: "0"
          type: integer
      - name: max
        in: query
        schema:
          format: int32
          default: "10"
          type: integer
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GroupRepresentation'
    post:
      tags:
      - Groups
      summary: Set or create child.
      description: This will just set the parent if it exists. Create it and set the
        parent if the group doesn’t exist.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GroupRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}/management/permissions:
    get:
      tags:
      - Groups
      summary: Return object stating whether client Authorization permissions have
        been initialized or not and a reference
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    put:
      tags:
      - Groups
      summary: Return object stating whether client Authorization permissions have
        been initialized or not and a reference
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementPermissionReference'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}/members:
    get:
      tags:
      - Groups
      summary: "Get users Returns a stream of users, filtered according to query parameters"
      parameters:
      - name: briefRepresentation
        in: query
        description: "Only return basic information (only guaranteed to return id,\
          \ username, created, first and last name, email, enabled state, email verification\
          \ state, federation link, and access. Note that it means that namely user\
          \ attributes, required actions, and not before are not returned.)"
        schema:
          type: boolean
      - name: first
        in: query
        description: Pagination offset
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        description: Maximum results size (defaults to 100)
        schema:
          format: int32
          type: integer
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}/role-mappings:
    get:
      tags:
      - Role Mapper
      summary: Get role mappings
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MappingsRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}/role-mappings/clients/{client}:
    get:
      tags:
      - Client Role Mappings
      summary: "Get client-level role mappings for the user, and the app"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Client Role Mappings
      summary: Add client-level roles to the user role mapping
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Client Role Mappings
      summary: Delete client-level roles from user role mapping
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}/role-mappings/clients/{client}/available:
    get:
      tags:
      - Client Role Mappings
      summary: Get available client-level roles that can be mapped to the user
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}/role-mappings/clients/{client}/composite:
    get:
      tags:
      - Client Role Mappings
      summary: Get effective client-level role mappings This recurses any composite
        roles
      parameters:
      - name: briefRepresentation
        in: query
        description: "if false, return roles with their attributes"
        schema:
          default: true
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}/role-mappings/realm:
    get:
      tags:
      - Role Mapper
      summary: Get realm-level role mappings
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Role Mapper
      summary: Add realm-level role mappings to the user
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Role Mapper
      summary: Delete realm-level role mappings
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}/role-mappings/realm/available:
    get:
      tags:
      - Role Mapper
      summary: Get realm-level roles that can be mapped
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/groups/{group-id}/role-mappings/realm/composite:
    get:
      tags:
      - Role Mapper
      summary: Get effective realm-level role mappings This will recurse all composite
        roles to get the result.
      parameters:
      - name: briefRepresentation
        in: query
        description: "if false, return roles with their attributes"
        schema:
          default: true
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: group-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/identity-provider/import-config:
    post:
      tags:
      - Identity Providers
      summary: Import identity provider from JSON body
      description: Import identity provider from uploaded JSON file
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties: {}
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: string
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/identity-provider/instances:
    get:
      tags:
      - Identity Providers
      summary: List identity providers
      parameters:
      - name: briefRepresentation
        in: query
        description: "Boolean which defines whether brief representations are returned\
          \ (default: false)"
        schema:
          type: boolean
      - name: first
        in: query
        description: Pagination offset
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        description: Maximum results size (defaults to 100)
        schema:
          format: int32
          type: integer
      - name: search
        in: query
        description: "Filter specific providers by name. Search can be prefix (name*),\
          \ contains (*name*) or exact (\"name\"). Default prefixed."
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/IdentityProviderRepresentation'
    post:
      tags:
      - Identity Providers
      summary: Create a new identity provider
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IdentityProviderRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/identity-provider/instances/{alias}:
    get:
      tags:
      - Identity Providers
      summary: Get the identity provider
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdentityProviderRepresentation'
    put:
      tags:
      - Identity Providers
      summary: Update the identity provider
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IdentityProviderRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Identity Providers
      summary: Delete the identity provider
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: alias
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/identity-provider/instances/{alias}/export:
    get:
      tags:
      - Identity Providers
      summary: Export public broker configuration for identity provider
      parameters:
      - name: format
        in: query
        description: Format to use
        schema:
          type: string
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: alias
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/identity-provider/instances/{alias}/management/permissions:
    get:
      tags:
      - Identity Providers
      summary: Return object stating whether client Authorization permissions have
        been initialized or not and a reference
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    put:
      tags:
      - Identity Providers
      summary: Return object stating whether client Authorization permissions have
        been initialized or not and a reference
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementPermissionReference'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: alias
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/identity-provider/instances/{alias}/mapper-types:
    get:
      tags:
      - Identity Providers
      summary: Get mapper types for identity provider
      responses:
        "200":
          description: OK
          content:
            '*/*':
              schema:
                type: object
                additionalProperties:
                  $ref: '#/components/schemas/IdentityProviderMapperTypeRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: alias
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/identity-provider/instances/{alias}/mappers:
    get:
      tags:
      - Identity Providers
      summary: Get mappers for identity provider
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/IdentityProviderMapperRepresentation'
    post:
      tags:
      - Identity Providers
      summary: Add a mapper to identity provider
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IdentityProviderMapperRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: alias
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/identity-provider/instances/{alias}/mappers/{id}:
    get:
      tags:
      - Identity Providers
      summary: Get mapper by id for the identity provider
      parameters:
      - name: id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/IdentityProviderMapperRepresentation'
    put:
      tags:
      - Identity Providers
      summary: Update a mapper for the identity provider
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/IdentityProviderMapperRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Identity Providers
      summary: Delete a mapper for the identity provider
      parameters:
      - name: id
        in: path
        description: Mapper id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: alias
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/identity-provider/instances/{alias}/reload-keys:
    get:
      tags:
      - Identity Providers
      summary: "Reaload keys for the identity provider if the provider supports it,\
        \ \"true\" is returned if reload was performed, \"false\" if not."
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: boolean
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: alias
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/identity-provider/providers/{provider_id}:
    get:
      tags:
      - Identity Providers
      summary: Get the identity provider factory for that provider id
      parameters:
      - name: provider_id
        in: path
        description: The provider id to get the factory
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/keys:
    get:
      tags:
      - Key
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/KeysMetadataRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/localization:
    get:
      tags:
      - Realms Admin
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/localization/{locale}:
    get:
      tags:
      - Realms Admin
      parameters:
      - name: locale
        in: path
        required: true
        schema:
          type: string
      - name: useRealmDefaultLocaleFallback
        in: query
        schema:
          type: boolean
        deprecated: true
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  type: string
    post:
      tags:
      - Realms Admin
      summary: Import localization from uploaded JSON file
      parameters:
      - name: locale
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: string
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Realms Admin
      parameters:
      - name: locale
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/localization/{locale}/{key}:
    get:
      tags:
      - Realms Admin
      parameters:
      - name: key
        in: path
        required: true
        schema:
          type: string
      - name: locale
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            text/plain:
              schema:
                type: string
    put:
      tags:
      - Realms Admin
      parameters:
      - name: key
        in: path
        required: true
        schema:
          type: string
      - name: locale
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          text/plain:
            schema:
              type: string
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Realms Admin
      parameters:
      - name: key
        in: path
        required: true
        schema:
          type: string
      - name: locale
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/logout-all:
    post:
      tags:
      - Realms Admin
      summary: Removes all user sessions.
      description: Any client that has an admin url will also be told to invalidate
        any sessions they have.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GlobalRequestResult'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/partial-export:
    post:
      tags:
      - Realms Admin
      summary: Partial export of existing realm into a JSON file.
      parameters:
      - name: exportClients
        in: query
        schema:
          type: boolean
      - name: exportGroupsAndRoles
        in: query
        schema:
          type: boolean
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/partialImport:
    post:
      tags:
      - Realms Admin
      summary: Partial import from a JSON file to an existing realm.
      requestBody:
        content:
          application/json:
            schema:
              format: binary
              type: string
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/push-revocation:
    post:
      tags:
      - Realms Admin
      summary: Push the realm's revocation policy to any client that has an admin
        url associated with it.
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GlobalRequestResult'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles:
    get:
      tags:
      - Roles
      summary: Get all roles for the realm or client
      parameters:
      - name: briefRepresentation
        in: query
        schema:
          default: true
          type: boolean
      - name: first
        in: query
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        schema:
          format: int32
          type: integer
      - name: search
        in: query
        schema:
          default: ""
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Roles
      summary: Create a new role for the realm or client
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles-by-id/{role-id}:
    get:
      tags:
      - Roles (by ID)
      summary: Get a specific role's representation
      parameters:
      - name: role-id
        in: path
        description: id of role
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleRepresentation'
    put:
      tags:
      - Roles (by ID)
      summary: Update the role
      parameters:
      - name: role-id
        in: path
        description: id of role
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Roles (by ID)
      summary: Delete the role
      parameters:
      - name: role-id
        in: path
        description: id of role
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles-by-id/{role-id}/composites:
    get:
      tags:
      - Roles (by ID)
      summary: Get role's children Returns a set of role's children provided the role
        is a composite.
      parameters:
      - name: role-id
        in: path
        required: true
        schema:
          type: string
      - name: first
        in: query
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        schema:
          format: int32
          type: integer
      - name: search
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Roles (by ID)
      summary: Make the role a composite role by associating some child roles
      parameters:
      - name: role-id
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Roles (by ID)
      summary: Remove a set of roles from the role's composite
      parameters:
      - name: role-id
        in: path
        description: Role id
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles-by-id/{role-id}/composites/clients/{clientUuid}:
    get:
      tags:
      - Roles (by ID)
      summary: Get client-level roles for the client that are in the role's composite
      parameters:
      - name: clientUuid
        in: path
        required: true
        schema:
          type: string
      - name: role-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles-by-id/{role-id}/composites/realm:
    get:
      tags:
      - Roles (by ID)
      summary: Get realm-level roles that are in the role's composite
      parameters:
      - name: role-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles-by-id/{role-id}/management/permissions:
    get:
      tags:
      - Roles (by ID)
      summary: Return object stating whether role Authorization permissions have been
        initialized or not and a reference
      parameters:
      - name: role-id
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    put:
      tags:
      - Roles (by ID)
      summary: Return object stating whether role Authorization permissions have been
        initialized or not and a reference
      parameters:
      - name: role-id
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementPermissionReference'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles/{role-name}:
    get:
      tags:
      - Roles
      summary: Get a role by name
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RoleRepresentation'
    put:
      tags:
      - Roles
      summary: Update a role by name
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Roles
      summary: Delete a role by name
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles/{role-name}/composites:
    get:
      tags:
      - Roles
      summary: Get composites of the role
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Roles
      summary: Add a composite to the role
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Roles
      summary: Remove roles from the role's composite
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles/{role-name}/composites/clients/{client-uuid}:
    get:
      tags:
      - Roles
      summary: Get client-level roles for the client that are in the role's composite
      parameters:
      - name: client-uuid
        in: path
        required: true
        schema:
          type: string
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles/{role-name}/composites/realm:
    get:
      tags:
      - Roles
      summary: Get realm-level roles of the role's composite
      parameters:
      - name: role-name
        in: path
        description: role's name (not id!)
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles/{role-name}/groups:
    get:
      tags:
      - Roles
      summary: Returns a stream of groups that have the specified role name
      parameters:
      - name: role-name
        in: path
        description: the role name.
        required: true
        schema:
          type: string
      - name: briefRepresentation
        in: query
        description: "if false, return a full representation of the {@code GroupRepresentation}\
          \ objects."
        schema:
          default: true
          type: boolean
      - name: first
        in: query
        description: "first result to return. Ignored if negative or {@code null}."
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        description: "maximum number of results to return. Ignored if negative or\
          \ {@code null}."
        schema:
          format: int32
          type: integer
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GroupRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles/{role-name}/management/permissions:
    get:
      tags:
      - Roles
      summary: Return object stating whether role Authorization permissions have been
        initialized or not and a reference
      parameters:
      - name: role-name
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    put:
      tags:
      - Roles
      summary: Return object stating whether role Authorization permissions have been
        initialized or not and a reference
      parameters:
      - name: role-name
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementPermissionReference'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/roles/{role-name}/users:
    get:
      tags:
      - Roles
      summary: Returns a stream of users that have the specified role name.
      parameters:
      - name: role-name
        in: path
        description: the role name.
        required: true
        schema:
          type: string
      - name: first
        in: query
        description: "first result to return. Ignored if negative or {@code null}."
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        description: "maximum number of results to return. Ignored if negative or\
          \ {@code null}."
        schema:
          format: int32
          type: integer
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/sessions/{session}:
    delete:
      tags:
      - Realms Admin
      summary: Remove a specific user session.
      description: Any client that has an admin url will also be told to invalidate
        this particular session.
      parameters:
      - name: session
        in: path
        required: true
        schema:
          type: string
      - name: isOffline
        in: query
        schema:
          default: false
          type: boolean
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/testSMTPConnection:
    post:
      tags:
      - Realms Admin
      summary: Test SMTP connection with current logged in user
      requestBody:
        content:
          application/json:
            schema:
              type: object
              additionalProperties:
                type: string
          application/x-www-form-urlencoded:
            schema:
              type: object
              properties:
                config:
                  type: string
      responses:
        "200":
          description: OK
      deprecated: true
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users:
    get:
      tags:
      - Users
      summary: "Get users Returns a stream of users, filtered according to query parameters."
      parameters:
      - name: briefRepresentation
        in: query
        description: "Boolean which defines whether brief representations are returned\
          \ (default: false)"
        schema:
          type: boolean
      - name: email
        in: query
        description: "A String contained in email, or the complete email, if param\
          \ \"exact\" is true"
        schema:
          type: string
      - name: emailVerified
        in: query
        description: whether the email has been verified
        schema:
          type: boolean
      - name: enabled
        in: query
        description: Boolean representing if user is enabled or not
        schema:
          type: boolean
      - name: exact
        in: query
        description: "Boolean which defines whether the params \"last\", \"first\"\
          , \"email\" and \"username\" must match exactly"
        schema:
          type: boolean
      - name: first
        in: query
        description: Pagination offset
        schema:
          format: int32
          type: integer
      - name: firstName
        in: query
        description: "A String contained in firstName, or the complete firstName,\
          \ if param \"exact\" is true"
        schema:
          type: string
      - name: idpAlias
        in: query
        description: The alias of an Identity Provider linked to the user
        schema:
          type: string
      - name: idpUserId
        in: query
        description: The userId at an Identity Provider linked to the user
        schema:
          type: string
      - name: lastName
        in: query
        description: "A String contained in lastName, or the complete lastName, if\
          \ param \"exact\" is true"
        schema:
          type: string
      - name: max
        in: query
        description: Maximum results size (defaults to 100)
        schema:
          format: int32
          type: integer
      - name: q
        in: query
        description: "A query to search for custom attributes, in the format 'key1:value2\
          \ key2:value2'"
        schema:
          type: string
      - name: search
        in: query
        description: "A String contained in username, first or last name, or email.\
          \ Default search behavior is prefix-based (e.g., foo or foo*). Use *foo*\
          \ for infix search and \"foo\" for exact search."
        schema:
          type: string
      - name: username
        in: query
        description: "A String contained in username, or the complete username, if\
          \ param \"exact\" is true"
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserRepresentation'
    post:
      tags:
      - Users
      summary: Create a new user Username must be unique.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRepresentation'
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users-management-permissions:
    get:
      tags:
      - Realms Admin
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    put:
      tags:
      - Realms Admin
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ManagementPermissionReference'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ManagementPermissionReference'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/count:
    get:
      tags:
      - Users
      summary: Returns the number of users that match the given criteria.
      description: "It can be called in three different ways. 1. Don’t specify any\
        \ criteria and pass {@code null}. The number of all users within that realm\
        \ will be returned. <p> 2. If {@code search} is specified other criteria such\
        \ as {@code last} will be ignored even though you set them. The {@code search}\
        \ string will be matched against the first and last name, the username and\
        \ the email of a user. <p> 3. If {@code search} is unspecified but any of\
        \ {@code last}, {@code first}, {@code email} or {@code username} those criteria\
        \ are matched against their respective fields on a user entity. Combined with\
        \ a logical and."
      parameters:
      - name: email
        in: query
        description: email filter
        schema:
          type: string
      - name: emailVerified
        in: query
        schema:
          type: boolean
      - name: enabled
        in: query
        description: Boolean representing if user is enabled or not
        schema:
          type: boolean
      - name: firstName
        in: query
        description: first name filter
        schema:
          type: string
      - name: lastName
        in: query
        description: last name filter
        schema:
          type: string
      - name: q
        in: query
        schema:
          type: string
      - name: search
        in: query
        description: "arbitrary search string for all the fields below. Default search\
          \ behavior is prefix-based (e.g., foo or foo*). Use *foo* for infix search\
          \ and \"foo\" for exact search."
        schema:
          type: string
      - name: username
        in: query
        description: username filter
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                format: int32
                type: integer
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/profile:
    get:
      tags:
      - Users
      description: Get the configuration for the user profile
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UPConfig'
    put:
      tags:
      - Users
      description: Set the configuration for the user profile
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UPConfig'
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UPConfig'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/profile/metadata:
    get:
      tags:
      - Users
      description: Get the UserProfileMetadata from the configuration
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserProfileMetadata'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}:
    get:
      tags:
      - Users
      summary: Get representation of the user
      parameters:
      - name: userProfileMetadata
        in: query
        description: Indicates if the user profile metadata should be added to the
          response
        schema:
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserRepresentation'
    put:
      tags:
      - Users
      summary: Update the user
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserRepresentation'
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Users
      summary: Delete the user
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/configured-user-storage-credential-types:
    get:
      tags:
      - Users
      summary: "Return credential types, which are provided by the user storage where\
        \ user is stored."
      description: "Returned values can contain for example \"password\", \"otp\"\
        \ etc. This will always return empty list for \"local\" users, which are not\
        \ backed by any user storage"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/consents:
    get:
      tags:
      - Users
      summary: Get consents granted by the user
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  additionalProperties: {}
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/consents/{client}:
    delete:
      tags:
      - Users
      summary: Revoke consent and offline tokens for particular client from user
      parameters:
      - name: client
        in: path
        description: Client id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/credentials:
    get:
      tags:
      - Users
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/CredentialRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/credentials/{credentialId}:
    delete:
      tags:
      - Users
      summary: Remove a credential for a user
      parameters:
      - name: credentialId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/credentials/{credentialId}/moveAfter/{newPreviousCredentialId}:
    post:
      tags:
      - Users
      summary: Move a credential to a position behind another credential
      parameters:
      - name: credentialId
        in: path
        description: The credential to move
        required: true
        schema:
          type: string
      - name: newPreviousCredentialId
        in: path
        description: "The credential that will be the previous element in the list.\
          \ If set to null, the moved credential will be the first element in the\
          \ list."
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/credentials/{credentialId}/moveToFirst:
    post:
      tags:
      - Users
      summary: Move a credential to a first position in the credentials list of the
        user
      parameters:
      - name: credentialId
        in: path
        description: The credential to move
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/credentials/{credentialId}/userLabel:
    put:
      tags:
      - Users
      summary: Update a credential label for a user
      parameters:
      - name: credentialId
        in: path
        required: true
        schema:
          type: string
      requestBody:
        content:
          text/plain:
            schema:
              type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/disable-credential-types:
    put:
      tags:
      - Users
      summary: Disable all credentials for a user of a specific type
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/execute-actions-email:
    put:
      tags:
      - Users
      summary: Send an email to the user with a link they can click to execute particular
        actions.
      description: "An email contains a link the user can click to perform a set of\
        \ required actions. The redirectUri and clientId parameters are optional.\
        \ If no redirect is given, then there will be no link back to click after\
        \ actions have completed. Redirect uri must be a valid uri for the particular\
        \ clientId."
      parameters:
      - name: client_id
        in: query
        description: Client id
        schema:
          type: string
      - name: lifespan
        in: query
        description: Number of seconds after which the generated token expires
        schema:
          format: int32
          type: integer
      - name: redirect_uri
        in: query
        description: Redirect uri
        schema:
          type: string
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: string
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/federated-identity:
    get:
      tags:
      - Users
      summary: Get social logins associated with the user
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/FederatedIdentityRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/federated-identity/{provider}:
    post:
      tags:
      - Users
      summary: Add a social login provider to the user
      parameters:
      - name: provider
        in: path
        description: Social login provider id
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
    delete:
      tags:
      - Users
      summary: Remove a social login provider from user
      parameters:
      - name: provider
        in: path
        description: Social login provider id
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/groups:
    get:
      tags:
      - Users
      parameters:
      - name: briefRepresentation
        in: query
        schema:
          default: true
          type: boolean
      - name: first
        in: query
        schema:
          format: int32
          type: integer
      - name: max
        in: query
        schema:
          format: int32
          type: integer
      - name: search
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/GroupRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/groups/count:
    get:
      tags:
      - Users
      parameters:
      - name: search
        in: query
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties:
                  format: int64
                  type: integer
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/groups/{groupId}:
    put:
      tags:
      - Users
      parameters:
      - name: groupId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Users
      parameters:
      - name: groupId
        in: path
        required: true
        schema:
          type: string
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/impersonation:
    post:
      tags:
      - Users
      summary: Impersonate the user
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: object
                additionalProperties: {}
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/logout:
    post:
      tags:
      - Users
      summary: Remove all user sessions associated with the user Also send notification
        to all clients that have an admin URL to invalidate the sessions for the particular
        user.
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/offline-sessions/{clientUuid}:
    get:
      tags:
      - Users
      summary: Get offline sessions associated with the user and client
      parameters:
      - name: clientUuid
        in: path
        required: true
        schema:
          type: string
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserSessionRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/reset-password:
    put:
      tags:
      - Users
      summary: Set up a new password for the user.
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CredentialRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/reset-password-email:
    put:
      tags:
      - Users
      summary: Send an email to the user with a link they can click to reset their
        password.
      description: The redirectUri and clientId parameters are optional. The default
        for the redirect is the account client. This endpoint has been deprecated.  Please
        use the execute-actions-email passing a list with UPDATE_PASSWORD within it.
      parameters:
      - name: client_id
        in: query
        description: client id
        schema:
          type: string
      - name: redirect_uri
        in: query
        description: redirect uri
        schema:
          type: string
      responses:
        "200":
          description: OK
      deprecated: true
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/role-mappings:
    get:
      tags:
      - Role Mapper
      summary: Get role mappings
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MappingsRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/role-mappings/clients/{client}:
    get:
      tags:
      - Client Role Mappings
      summary: "Get client-level role mappings for the user, and the app"
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Client Role Mappings
      summary: Add client-level roles to the user role mapping
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Client Role Mappings
      summary: Delete client-level roles from user role mapping
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/role-mappings/clients/{client}/available:
    get:
      tags:
      - Client Role Mappings
      summary: Get available client-level roles that can be mapped to the user
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/role-mappings/clients/{client}/composite:
    get:
      tags:
      - Client Role Mappings
      summary: Get effective client-level role mappings This recurses any composite
        roles
      parameters:
      - name: briefRepresentation
        in: query
        description: "if false, return roles with their attributes"
        schema:
          default: true
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
    - name: client
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/role-mappings/realm:
    get:
      tags:
      - Role Mapper
      summary: Get realm-level role mappings
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    post:
      tags:
      - Role Mapper
      summary: Add realm-level role mappings to the user
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    delete:
      tags:
      - Role Mapper
      summary: Delete realm-level role mappings
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/RoleRepresentation'
      responses:
        "204":
          description: No Content
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/role-mappings/realm/available:
    get:
      tags:
      - Role Mapper
      summary: Get realm-level roles that can be mapped
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/role-mappings/realm/composite:
    get:
      tags:
      - Role Mapper
      summary: Get effective realm-level role mappings This will recurse all composite
        roles to get the result.
      parameters:
      - name: briefRepresentation
        in: query
        description: "if false, return roles with their attributes"
        schema:
          default: true
          type: boolean
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/RoleRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/send-verify-email:
    put:
      tags:
      - Users
      summary: Send an email-verification email to the user An email contains a link
        the user can click to verify their email address.
      description: "The redirectUri, clientId and lifespan parameters are optional.\
        \ The default for the redirect is the account client. The default for the\
        \ lifespan is 12 hours"
      parameters:
      - name: client_id
        in: query
        description: Client id
        schema:
          type: string
      - name: lifespan
        in: query
        description: Number of seconds after which the generated token expires
        schema:
          format: int32
          type: integer
      - name: redirect_uri
        in: query
        description: Redirect uri
        schema:
          type: string
      responses:
        "200":
          description: OK
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
  /admin/realms/{realm}/users/{user-id}/sessions:
    get:
      tags:
      - Users
      summary: Get sessions associated with the user
      responses:
        "200":
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/UserSessionRepresentation'
    parameters:
    - name: realm
      in: path
      description: realm name (not id!)
      required: true
      schema:
        type: string
    - name: user-id
      in: path
      required: true
      schema:
        type: string
components:
  schemas:
    AbstractPolicyRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        type:
          type: string
        policies:
          uniqueItems: true
          type: array
          items:
            type: string
        resources:
          uniqueItems: true
          type: array
          items:
            type: string
        scopes:
          uniqueItems: true
          type: array
          items:
            type: string
        logic:
          $ref: '#/components/schemas/Logic'
        decisionStrategy:
          $ref: '#/components/schemas/DecisionStrategy'
        owner:
          type: string
        resourcesData:
          uniqueItems: true
          type: array
          items:
            $ref: '#/components/schemas/ResourceRepresentation'
        scopesData:
          uniqueItems: true
          type: array
          items:
            $ref: '#/components/schemas/ScopeRepresentation'
    Access:
      type: object
      properties:
        roles:
          uniqueItems: true
          type: array
          items:
            type: string
        verify_caller:
          type: boolean
    AccessToken:
      type: object
      properties:
        jti:
          type: string
        exp:
          format: int64
          type: integer
        nbf:
          format: int64
          type: integer
        iat:
          format: int64
          type: integer
        iss:
          type: string
        sub:
          type: string
        typ:
          type: string
        azp:
          type: string
        otherClaims:
          type: object
          additionalProperties: {}
        nonce:
          type: string
        auth_time:
          format: int64
          type: integer
        session_state:
          type: string
        at_hash:
          type: string
        c_hash:
          type: string
        name:
          type: string
        given_name:
          type: string
        family_name:
          type: string
        middle_name:
          type: string
        nickname:
          type: string
        preferred_username:
          type: string
        profile:
          type: string
        picture:
          type: string
        website:
          type: string
        email:
          type: string
        email_verified:
          type: boolean
        gender:
          type: string
        birthdate:
          type: string
        zoneinfo:
          type: string
        locale:
          type: string
        phone_number:
          type: string
        phone_number_verified:
          type: boolean
        address:
          $ref: '#/components/schemas/AddressClaimSet'
        updated_at:
          format: int64
          type: integer
        claims_locales:
          type: string
        acr:
          type: string
        s_hash:
          type: string
        authTime:
          format: int32
          type: integer
          writeOnly: true
          deprecated: true
        sid:
          type: string
        trusted-certs:
          uniqueItems: true
          type: array
          items:
            type: string
        allowed-origins:
          uniqueItems: true
          type: array
          items:
            type: string
        realm_access:
          $ref: '#/components/schemas/Access'
        resource_access:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/Access'
          writeOnly: true
        authorization:
          $ref: '#/components/schemas/Authorization'
        cnf:
          $ref: '#/components/schemas/Confirmation'
        scope:
          type: string
    AddressClaimSet:
      type: object
      properties:
        formatted:
          type: string
        street_address:
          type: string
        locality:
          type: string
        region:
          type: string
        postal_code:
          type: string
        country:
          type: string
    AdminEventRepresentation:
      type: object
      properties:
        time:
          format: int64
          type: integer
        realmId:
          type: string
        authDetails:
          $ref: '#/components/schemas/AuthDetailsRepresentation'
        operationType:
          type: string
        resourceType:
          type: string
        resourcePath:
          type: string
        representation:
          type: string
        error:
          type: string
    ApplicationRepresentation:
      type: object
      properties:
        id:
          type: string
        clientId:
          type: string
        description:
          type: string
        rootUrl:
          type: string
        adminUrl:
          type: string
        baseUrl:
          type: string
        surrogateAuthRequired:
          type: boolean
        enabled:
          type: boolean
        alwaysDisplayInConsole:
          type: boolean
        clientAuthenticatorType:
          type: string
        secret:
          type: string
        registrationAccessToken:
          type: string
        defaultRoles:
          type: array
          items:
            type: string
          deprecated: true
        redirectUris:
          type: array
          items:
            type: string
        webOrigins:
          type: array
          items:
            type: string
        notBefore:
          format: int32
          type: integer
        bearerOnly:
          type: boolean
        consentRequired:
          type: boolean
        standardFlowEnabled:
          type: boolean
        implicitFlowEnabled:
          type: boolean
        directAccessGrantsEnabled:
          type: boolean
        serviceAccountsEnabled:
          type: boolean
        authorizationServicesEnabled:
          type: boolean
        directGrantsOnly:
          type: boolean
          deprecated: true
        publicClient:
          type: boolean
        frontchannelLogout:
          type: boolean
        protocol:
          type: string
        attributes:
          type: object
          additionalProperties:
            type: string
        authenticationFlowBindingOverrides:
          type: object
          additionalProperties:
            type: string
        fullScopeAllowed:
          type: boolean
        nodeReRegistrationTimeout:
          format: int32
          type: integer
        registeredNodes:
          type: object
          additionalProperties:
            format: int32
            type: integer
        protocolMappers:
          type: array
          items:
            $ref: '#/components/schemas/ProtocolMapperRepresentation'
        clientTemplate:
          type: string
          deprecated: true
        useTemplateConfig:
          type: boolean
          deprecated: true
        useTemplateScope:
          type: boolean
          deprecated: true
        useTemplateMappers:
          type: boolean
          deprecated: true
        defaultClientScopes:
          type: array
          items:
            type: string
        optionalClientScopes:
          type: array
          items:
            type: string
        authorizationSettings:
          $ref: '#/components/schemas/ResourceServerRepresentation'
        access:
          type: object
          additionalProperties:
            type: boolean
        origin:
          type: string
        name:
          type: string
        claims:
          type: object
          allOf:
          - $ref: '#/components/schemas/ClaimRepresentation'
          deprecated: true
      deprecated: true
    AuthDetailsRepresentation:
      type: object
      properties:
        realmId:
          type: string
        clientId:
          type: string
        userId:
          type: string
        ipAddress:
          type: string
    AuthenticationExecutionExportRepresentation:
      type: object
      properties:
        authenticatorConfig:
          type: string
        authenticator:
          type: string
        authenticatorFlow:
          type: boolean
        requirement:
          type: string
        priority:
          format: int32
          type: integer
        autheticatorFlow:
          type: boolean
          deprecated: true
        flowAlias:
          type: string
        userSetupAllowed:
          type: boolean
    AuthenticationExecutionInfoRepresentation:
      type: object
      properties:
        id:
          type: string
        requirement:
          type: string
        displayName:
          type: string
        alias:
          type: string
        description:
          type: string
        requirementChoices:
          type: array
          items:
            type: string
        configurable:
          type: boolean
        authenticationFlow:
          type: boolean
        providerId:
          type: string
        authenticationConfig:
          type: string
        flowId:
          type: string
        level:
          format: int32
          type: integer
        index:
          format: int32
          type: integer
    AuthenticationExecutionRepresentation:
      type: object
      properties:
        authenticatorConfig:
          type: string
        authenticator:
          type: string
        authenticatorFlow:
          type: boolean
        requirement:
          type: string
        priority:
          format: int32
          type: integer
        autheticatorFlow:
          type: boolean
          deprecated: true
        id:
          type: string
        flowId:
          type: string
        parentFlow:
          type: string
    AuthenticationFlowRepresentation:
      type: object
      properties:
        id:
          type: string
        alias:
          type: string
        description:
          type: string
        providerId:
          type: string
        topLevel:
          type: boolean
        builtIn:
          type: boolean
        authenticationExecutions:
          type: array
          items:
            $ref: '#/components/schemas/AuthenticationExecutionExportRepresentation'
    AuthenticatorConfigInfoRepresentation:
      type: object
      properties:
        name:
          type: string
        providerId:
          type: string
        helpText:
          type: string
        properties:
          type: array
          items:
            $ref: '#/components/schemas/ConfigPropertyRepresentation'
    AuthenticatorConfigRepresentation:
      type: object
      properties:
        id:
          type: string
        alias:
          type: string
        config:
          type: object
          additionalProperties:
            type: string
    Authorization:
      type: object
      properties:
        permissions:
          type: array
          items:
            $ref: '#/components/schemas/Permission'
    CertificateRepresentation:
      type: object
      properties:
        privateKey:
          type: string
        publicKey:
          type: string
        certificate:
          type: string
        kid:
          type: string
    ClaimRepresentation:
      type: object
      properties:
        name:
          type: boolean
        username:
          type: boolean
        profile:
          type: boolean
        picture:
          type: boolean
        website:
          type: boolean
        email:
          type: boolean
        gender:
          type: boolean
        locale:
          type: boolean
        address:
          type: boolean
        phone:
          type: boolean
    ClientInitialAccessCreatePresentation:
      type: object
      properties:
        expiration:
          format: int32
          type: integer
        count:
          format: int32
          type: integer
    ClientInitialAccessPresentation:
      type: object
      properties:
        id:
          type: string
        token:
          type: string
        timestamp:
          format: int32
          type: integer
        expiration:
          format: int32
          type: integer
        count:
          format: int32
          type: integer
        remainingCount:
          format: int32
          type: integer
    ClientMappingsRepresentation:
      type: object
      properties:
        id:
          type: string
        client:
          type: string
        mappings:
          type: array
          items:
            $ref: '#/components/schemas/RoleRepresentation'
    ClientPoliciesRepresentation:
      type: object
      properties:
        policies:
          type: array
          items:
            $ref: '#/components/schemas/ClientPolicyRepresentation'
    ClientPolicyConditionRepresentation:
      type: object
      properties:
        condition:
          type: string
        configuration:
          type: array
    ClientPolicyExecutorRepresentation:
      type: object
      properties:
        executor:
          type: string
        configuration:
          type: array
    ClientPolicyRepresentation:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        enabled:
          type: boolean
        conditions:
          type: array
          items:
            $ref: '#/components/schemas/ClientPolicyConditionRepresentation'
        profiles:
          type: array
          items:
            type: string
    ClientProfileRepresentation:
      type: object
      properties:
        name:
          type: string
        description:
          type: string
        executors:
          type: array
          items:
            $ref: '#/components/schemas/ClientPolicyExecutorRepresentation'
    ClientProfilesRepresentation:
      type: object
      properties:
        profiles:
          type: array
          items:
            $ref: '#/components/schemas/ClientProfileRepresentation'
        globalProfiles:
          type: array
          items:
            $ref: '#/components/schemas/ClientProfileRepresentation'
    ClientRepresentation:
      type: object
      properties:
        id:
          type: string
        clientId:
          type: string
        name:
          type: string
        description:
          type: string
        rootUrl:
          type: string
        adminUrl:
          type: string
        baseUrl:
          type: string
        surrogateAuthRequired:
          type: boolean
        enabled:
          type: boolean
        alwaysDisplayInConsole:
          type: boolean
        clientAuthenticatorType:
          type: string
        secret:
          type: string
        registrationAccessToken:
          type: string
        defaultRoles:
          type: array
          items:
            type: string
          deprecated: true
        redirectUris:
          type: array
          items:
            type: string
        webOrigins:
          type: array
          items:
            type: string
        notBefore:
          format: int32
          type: integer
        bearerOnly:
          type: boolean
        consentRequired:
          type: boolean
        standardFlowEnabled:
          type: boolean
        implicitFlowEnabled:
          type: boolean
        directAccessGrantsEnabled:
          type: boolean
        serviceAccountsEnabled:
          type: boolean
        authorizationServicesEnabled:
          type: boolean
        directGrantsOnly:
          type: boolean
          deprecated: true
        publicClient:
          type: boolean
        frontchannelLogout:
          type: boolean
        protocol:
          type: string
        attributes:
          type: object
          additionalProperties:
            type: string
        authenticationFlowBindingOverrides:
          type: object
          additionalProperties:
            type: string
        fullScopeAllowed:
          type: boolean
        nodeReRegistrationTimeout:
          format: int32
          type: integer
        registeredNodes:
          type: object
          additionalProperties:
            format: int32
            type: integer
        protocolMappers:
          type: array
          items:
            $ref: '#/components/schemas/ProtocolMapperRepresentation'
        clientTemplate:
          type: string
          deprecated: true
        useTemplateConfig:
          type: boolean
          deprecated: true
        useTemplateScope:
          type: boolean
          deprecated: true
        useTemplateMappers:
          type: boolean
          deprecated: true
        defaultClientScopes:
          type: array
          items:
            type: string
        optionalClientScopes:
          type: array
          items:
            type: string
        authorizationSettings:
          $ref: '#/components/schemas/ResourceServerRepresentation'
        access:
          type: object
          additionalProperties:
            type: boolean
        origin:
          type: string
    ClientScopeRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        protocol:
          type: string
        attributes:
          type: object
          additionalProperties:
            type: string
        protocolMappers:
          type: array
          items:
            $ref: '#/components/schemas/ProtocolMapperRepresentation'
    ClientTemplateRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        protocol:
          type: string
        fullScopeAllowed:
          type: boolean
        bearerOnly:
          type: boolean
        consentRequired:
          type: boolean
        standardFlowEnabled:
          type: boolean
        implicitFlowEnabled:
          type: boolean
        directAccessGrantsEnabled:
          type: boolean
        serviceAccountsEnabled:
          type: boolean
        publicClient:
          type: boolean
        frontchannelLogout:
          type: boolean
        attributes:
          type: object
          additionalProperties:
            type: string
        protocolMappers:
          type: array
          items:
            $ref: '#/components/schemas/ProtocolMapperRepresentation'
      deprecated: true
    ComponentExportRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        providerId:
          type: string
        subType:
          type: string
        subComponents:
          $ref: '#/components/schemas/MultivaluedHashMapStringComponentExportRepresentation'
        config:
          $ref: '#/components/schemas/MultivaluedHashMapStringString'
    ComponentRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        providerId:
          type: string
        providerType:
          type: string
        parentId:
          type: string
        subType:
          type: string
        config:
          $ref: '#/components/schemas/MultivaluedHashMapStringString'
    ComponentTypeRepresentation:
      type: object
      properties:
        id:
          type: string
        helpText:
          type: string
        properties:
          type: array
          items:
            $ref: '#/components/schemas/ConfigPropertyRepresentation'
        metadata:
          type: object
          additionalProperties: {}
    Composites:
      type: object
      properties:
        realm:
          uniqueItems: true
          type: array
          items:
            type: string
        client:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        application:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          deprecated: true
    ConfigPropertyRepresentation:
      type: object
      properties:
        name:
          type: string
        label:
          type: string
        helpText:
          type: string
        type:
          type: string
        defaultValue: {}
        options:
          type: array
          items:
            type: string
        secret:
          type: boolean
        required:
          type: boolean
        readOnly:
          type: boolean
    Confirmation:
      type: object
      properties:
        x5t#S256:
          type: string
        jkt:
          type: string
    CredentialRepresentation:
      type: object
      properties:
        id:
          type: string
        type:
          type: string
        userLabel:
          type: string
        createdDate:
          format: int64
          type: integer
        secretData:
          type: string
        credentialData:
          type: string
        priority:
          format: int32
          type: integer
        value:
          type: string
        temporary:
          type: boolean
        device:
          type: string
          deprecated: true
        hashedSaltedValue:
          type: string
          deprecated: true
        salt:
          type: string
          deprecated: true
        hashIterations:
          format: int32
          type: integer
          deprecated: true
        counter:
          format: int32
          type: integer
          deprecated: true
        algorithm:
          type: string
          deprecated: true
        digits:
          format: int32
          type: integer
          deprecated: true
        period:
          format: int32
          type: integer
          deprecated: true
        config:
          type: object
          allOf:
          - $ref: '#/components/schemas/MultivaluedHashMapStringString'
          deprecated: true
    DecisionEffect:
      enum:
      - PERMIT
      - DENY
      type: string
    DecisionStrategy:
      enum:
      - AFFIRMATIVE
      - UNANIMOUS
      - CONSENSUS
      type: string
    EnforcementMode:
      enum:
      - PERMISSIVE
      - ENFORCING
      - DISABLED
      type: string
    EvaluationResultRepresentation:
      type: object
      properties:
        resource:
          $ref: '#/components/schemas/ResourceRepresentation'
        scopes:
          type: array
          items:
            $ref: '#/components/schemas/ScopeRepresentation'
        policies:
          type: array
          items:
            $ref: '#/components/schemas/PolicyResultRepresentation'
        status:
          $ref: '#/components/schemas/DecisionEffect'
        allowedScopes:
          type: array
          items:
            $ref: '#/components/schemas/ScopeRepresentation'
    EventRepresentation:
      type: object
      properties:
        time:
          format: int64
          type: integer
        type:
          type: string
        realmId:
          type: string
        clientId:
          type: string
        userId:
          type: string
        sessionId:
          type: string
        ipAddress:
          type: string
        error:
          type: string
        details:
          type: object
          additionalProperties:
            type: string
    FederatedIdentityRepresentation:
      type: object
      properties:
        identityProvider:
          type: string
        userId:
          type: string
        userName:
          type: string
    GlobalRequestResult:
      type: object
      properties:
        successRequests:
          type: array
          items:
            type: string
        failedRequests:
          type: array
          items:
            type: string
    GroupRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        path:
          type: string
        parentId:
          type: string
        subGroupCount:
          format: int64
          type: integer
        subGroups:
          type: array
          items:
            $ref: '#/components/schemas/GroupRepresentation'
        attributes:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        realmRoles:
          type: array
          items:
            type: string
        clientRoles:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        access:
          type: object
          additionalProperties:
            type: boolean
    IDToken:
      type: object
      properties:
        jti:
          type: string
        exp:
          format: int64
          type: integer
        nbf:
          format: int64
          type: integer
        iat:
          format: int64
          type: integer
        iss:
          type: string
        sub:
          type: string
        typ:
          type: string
        azp:
          type: string
        otherClaims:
          type: object
          additionalProperties: {}
        nonce:
          type: string
        auth_time:
          format: int64
          type: integer
        session_state:
          type: string
        at_hash:
          type: string
        c_hash:
          type: string
        name:
          type: string
        given_name:
          type: string
        family_name:
          type: string
        middle_name:
          type: string
        nickname:
          type: string
        preferred_username:
          type: string
        profile:
          type: string
        picture:
          type: string
        website:
          type: string
        email:
          type: string
        email_verified:
          type: boolean
        gender:
          type: string
        birthdate:
          type: string
        zoneinfo:
          type: string
        locale:
          type: string
        phone_number:
          type: string
        phone_number_verified:
          type: boolean
        address:
          $ref: '#/components/schemas/AddressClaimSet'
        updated_at:
          format: int64
          type: integer
        claims_locales:
          type: string
        acr:
          type: string
        s_hash:
          type: string
        authTime:
          format: int32
          type: integer
          writeOnly: true
          deprecated: true
        sid:
          type: string
    IdentityProviderMapperRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        identityProviderAlias:
          type: string
        identityProviderMapper:
          type: string
        config:
          type: object
          additionalProperties:
            type: string
    IdentityProviderMapperTypeRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        category:
          type: string
        helpText:
          type: string
        properties:
          type: array
          items:
            $ref: '#/components/schemas/ConfigPropertyRepresentation'
    IdentityProviderRepresentation:
      type: object
      properties:
        alias:
          type: string
        displayName:
          type: string
        internalId:
          type: string
        providerId:
          type: string
        enabled:
          type: boolean
        updateProfileFirstLoginMode:
          type: string
          deprecated: true
        trustEmail:
          type: boolean
        storeToken:
          type: boolean
        addReadTokenRoleOnCreate:
          type: boolean
        authenticateByDefault:
          type: boolean
        linkOnly:
          type: boolean
        firstBrokerLoginFlowAlias:
          type: string
        postBrokerLoginFlowAlias:
          type: string
        config:
          type: object
          additionalProperties:
            type: string
        updateProfileFirstLogin:
          type: boolean
          deprecated: true
    InstallationAdapterConfig:
      type: object
      properties:
        realm:
          type: string
        realm-public-key:
          type: string
        auth-server-url:
          type: string
        ssl-required:
          type: string
        bearer-only:
          type: boolean
        resource:
          type: string
        public-client:
          type: boolean
        verify-token-audience:
          type: boolean
        credentials:
          type: object
          additionalProperties: {}
        use-resource-role-mappings:
          type: boolean
        confidential-port:
          format: int32
          type: integer
        policy-enforcer:
          $ref: '#/components/schemas/PolicyEnforcerConfig'
    KeyMetadataRepresentation:
      type: object
      properties:
        providerId:
          type: string
        providerPriority:
          format: int64
          type: integer
        kid:
          type: string
        status:
          type: string
        type:
          type: string
        algorithm:
          type: string
        publicKey:
          type: string
        certificate:
          type: string
        use:
          $ref: '#/components/schemas/KeyUse'
        validTo:
          format: int64
          type: integer
    KeyStoreConfig:
      type: object
      properties:
        realmCertificate:
          type: boolean
        storePassword:
          type: string
        keyPassword:
          type: string
        keyAlias:
          type: string
        realmAlias:
          type: string
        format:
          type: string
    KeyUse:
      enum:
      - SIG
      - ENC
      type: string
    KeysMetadataRepresentation:
      type: object
      properties:
        active:
          type: object
          additionalProperties:
            type: string
        keys:
          type: array
          items:
            $ref: '#/components/schemas/KeyMetadataRepresentation'
    Logic:
      enum:
      - POSITIVE
      - NEGATIVE
      type: string
    ManagementPermissionReference:
      type: object
      properties:
        enabled:
          type: boolean
        resource:
          type: string
        scopePermissions:
          type: object
          additionalProperties:
            type: string
    MappingsRepresentation:
      type: object
      properties:
        realmMappings:
          type: array
          items:
            $ref: '#/components/schemas/RoleRepresentation'
        clientMappings:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ClientMappingsRepresentation'
    MethodConfig:
      type: object
      properties:
        method:
          type: string
        scopes:
          type: array
          items:
            type: string
        scopes-enforcement-mode:
          $ref: '#/components/schemas/ScopeEnforcementMode'
    MultivaluedHashMapStringComponentExportRepresentation:
      type: object
      additionalProperties:
        type: array
        items:
          $ref: '#/components/schemas/ComponentExportRepresentation'
    MultivaluedHashMapStringString:
      type: object
      additionalProperties:
        type: array
        items:
          type: string
    MultivaluedMapStringString:
      type: object
      additionalProperties:
        type: array
        items:
          type: string
    OAuthClientRepresentation:
      type: object
      properties:
        id:
          type: string
        clientId:
          type: string
        description:
          type: string
        rootUrl:
          type: string
        adminUrl:
          type: string
        baseUrl:
          type: string
        surrogateAuthRequired:
          type: boolean
        enabled:
          type: boolean
        alwaysDisplayInConsole:
          type: boolean
        clientAuthenticatorType:
          type: string
        secret:
          type: string
        registrationAccessToken:
          type: string
        defaultRoles:
          type: array
          items:
            type: string
          deprecated: true
        redirectUris:
          type: array
          items:
            type: string
        webOrigins:
          type: array
          items:
            type: string
        notBefore:
          format: int32
          type: integer
        bearerOnly:
          type: boolean
        consentRequired:
          type: boolean
        standardFlowEnabled:
          type: boolean
        implicitFlowEnabled:
          type: boolean
        directAccessGrantsEnabled:
          type: boolean
        serviceAccountsEnabled:
          type: boolean
        authorizationServicesEnabled:
          type: boolean
        directGrantsOnly:
          type: boolean
          deprecated: true
        publicClient:
          type: boolean
        frontchannelLogout:
          type: boolean
        protocol:
          type: string
        attributes:
          type: object
          additionalProperties:
            type: string
        authenticationFlowBindingOverrides:
          type: object
          additionalProperties:
            type: string
        fullScopeAllowed:
          type: boolean
        nodeReRegistrationTimeout:
          format: int32
          type: integer
        registeredNodes:
          type: object
          additionalProperties:
            format: int32
            type: integer
        protocolMappers:
          type: array
          items:
            $ref: '#/components/schemas/ProtocolMapperRepresentation'
        clientTemplate:
          type: string
          deprecated: true
        useTemplateConfig:
          type: boolean
          deprecated: true
        useTemplateScope:
          type: boolean
          deprecated: true
        useTemplateMappers:
          type: boolean
          deprecated: true
        defaultClientScopes:
          type: array
          items:
            type: string
        optionalClientScopes:
          type: array
          items:
            type: string
        authorizationSettings:
          $ref: '#/components/schemas/ResourceServerRepresentation'
        access:
          type: object
          additionalProperties:
            type: boolean
        origin:
          type: string
        name:
          type: string
        claims:
          type: object
          allOf:
          - $ref: '#/components/schemas/ClaimRepresentation'
          deprecated: true
      deprecated: true
    PathCacheConfig:
      type: object
      properties:
        max-entries:
          format: int32
          type: integer
        lifespan:
          format: int64
          type: integer
    PathConfig:
      type: object
      properties:
        name:
          type: string
        type:
          type: string
        path:
          type: string
        methods:
          type: array
          items:
            $ref: '#/components/schemas/MethodConfig'
        scopes:
          type: array
          items:
            type: string
        id:
          type: string
        enforcement-mode:
          $ref: '#/components/schemas/EnforcementMode'
        claim-information-point:
          type: object
          additionalProperties:
            type: object
            additionalProperties: {}
        invalidated:
          type: boolean
        staticPath:
          type: boolean
        static:
          type: boolean
    PathSegment:
      type: object
      properties:
        path:
          type: string
        matrixParameters:
          $ref: '#/components/schemas/MultivaluedMapStringString'
    Permission:
      type: object
      properties:
        rsid:
          type: string
        rsname:
          type: string
        scopes:
          uniqueItems: true
          type: array
          items:
            type: string
        claims:
          type: object
          additionalProperties:
            uniqueItems: true
            type: array
            items:
              type: string
    PolicyEnforcementMode:
      enum:
      - ENFORCING
      - PERMISSIVE
      - DISABLED
      type: string
    PolicyEnforcerConfig:
      type: object
      properties:
        enforcement-mode:
          $ref: '#/components/schemas/EnforcementMode'
        paths:
          type: array
          items:
            $ref: '#/components/schemas/PathConfig'
        path-cache:
          $ref: '#/components/schemas/PathCacheConfig'
        lazy-load-paths:
          type: boolean
        on-deny-redirect-to:
          type: string
        user-managed-access:
          $ref: '#/components/schemas/UserManagedAccessConfig'
        claim-information-point:
          type: object
          additionalProperties:
            type: object
            additionalProperties: {}
        http-method-as-scope:
          type: boolean
        realm:
          type: string
        auth-server-url:
          type: string
        credentials:
          type: object
          additionalProperties: {}
        resource:
          type: string
    PolicyEvaluationRequest:
      type: object
      properties:
        context:
          type: object
          additionalProperties:
            type: object
            additionalProperties:
              type: string
        resources:
          type: array
          items:
            $ref: '#/components/schemas/ResourceRepresentation'
        clientId:
          type: string
        userId:
          type: string
        roleIds:
          type: array
          items:
            type: string
        entitlements:
          type: boolean
    PolicyEvaluationResponse:
      type: object
      properties:
        results:
          type: array
          items:
            $ref: '#/components/schemas/EvaluationResultRepresentation'
        entitlements:
          type: boolean
        status:
          $ref: '#/components/schemas/DecisionEffect'
        rpt:
          $ref: '#/components/schemas/AccessToken'
    PolicyProviderRepresentation:
      type: object
      properties:
        type:
          type: string
        name:
          type: string
        group:
          type: string
    PolicyRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        type:
          type: string
        policies:
          uniqueItems: true
          type: array
          items:
            type: string
        resources:
          uniqueItems: true
          type: array
          items:
            type: string
        scopes:
          uniqueItems: true
          type: array
          items:
            type: string
        logic:
          $ref: '#/components/schemas/Logic'
        decisionStrategy:
          $ref: '#/components/schemas/DecisionStrategy'
        owner:
          type: string
        resourcesData:
          uniqueItems: true
          type: array
          items:
            $ref: '#/components/schemas/ResourceRepresentation'
        scopesData:
          uniqueItems: true
          type: array
          items:
            $ref: '#/components/schemas/ScopeRepresentation'
        config:
          type: object
          additionalProperties:
            type: string
    PolicyResultRepresentation:
      type: object
      properties:
        policy:
          $ref: '#/components/schemas/PolicyRepresentation'
        status:
          $ref: '#/components/schemas/DecisionEffect'
        associatedPolicies:
          type: array
          items:
            $ref: '#/components/schemas/PolicyResultRepresentation'
        scopes:
          uniqueItems: true
          type: array
          items:
            type: string
    ProtocolMapperEvaluationRepresentation:
      type: object
      properties:
        mapperId:
          type: string
        mapperName:
          type: string
        containerId:
          type: string
        containerName:
          type: string
        containerType:
          type: string
        protocolMapper:
          type: string
    ProtocolMapperRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        protocol:
          type: string
        protocolMapper:
          type: string
        consentRequired:
          type: boolean
          deprecated: true
        consentText:
          type: string
          deprecated: true
        config:
          type: object
          additionalProperties:
            type: string
    PublishedRealmRepresentation:
      type: object
      properties:
        realm:
          type: string
        public_key:
          type: string
        token-service:
          type: string
        account-service:
          type: string
        tokens-not-before:
          format: int32
          type: integer
    RealmEventsConfigRepresentation:
      type: object
      properties:
        eventsEnabled:
          type: boolean
        eventsExpiration:
          format: int64
          type: integer
        eventsListeners:
          type: array
          items:
            type: string
        enabledEventTypes:
          type: array
          items:
            type: string
        adminEventsEnabled:
          type: boolean
        adminEventsDetailsEnabled:
          type: boolean
    RealmRepresentation:
      type: object
      properties:
        id:
          type: string
        realm:
          type: string
        displayName:
          type: string
        displayNameHtml:
          type: string
        notBefore:
          format: int32
          type: integer
        defaultSignatureAlgorithm:
          type: string
        revokeRefreshToken:
          type: boolean
        refreshTokenMaxReuse:
          format: int32
          type: integer
        accessTokenLifespan:
          format: int32
          type: integer
        accessTokenLifespanForImplicitFlow:
          format: int32
          type: integer
        ssoSessionIdleTimeout:
          format: int32
          type: integer
        ssoSessionMaxLifespan:
          format: int32
          type: integer
        ssoSessionIdleTimeoutRememberMe:
          format: int32
          type: integer
        ssoSessionMaxLifespanRememberMe:
          format: int32
          type: integer
        offlineSessionIdleTimeout:
          format: int32
          type: integer
        offlineSessionMaxLifespanEnabled:
          type: boolean
        offlineSessionMaxLifespan:
          format: int32
          type: integer
        clientSessionIdleTimeout:
          format: int32
          type: integer
        clientSessionMaxLifespan:
          format: int32
          type: integer
        clientOfflineSessionIdleTimeout:
          format: int32
          type: integer
        clientOfflineSessionMaxLifespan:
          format: int32
          type: integer
        accessCodeLifespan:
          format: int32
          type: integer
        accessCodeLifespanUserAction:
          format: int32
          type: integer
        accessCodeLifespanLogin:
          format: int32
          type: integer
        actionTokenGeneratedByAdminLifespan:
          format: int32
          type: integer
        actionTokenGeneratedByUserLifespan:
          format: int32
          type: integer
        oauth2DeviceCodeLifespan:
          format: int32
          type: integer
        oauth2DevicePollingInterval:
          format: int32
          type: integer
        enabled:
          type: boolean
        sslRequired:
          type: string
        passwordCredentialGrantAllowed:
          type: boolean
          deprecated: true
        registrationAllowed:
          type: boolean
        registrationEmailAsUsername:
          type: boolean
        rememberMe:
          type: boolean
        verifyEmail:
          type: boolean
        loginWithEmailAllowed:
          type: boolean
        duplicateEmailsAllowed:
          type: boolean
        resetPasswordAllowed:
          type: boolean
        editUsernameAllowed:
          type: boolean
        userCacheEnabled:
          type: boolean
          deprecated: true
        realmCacheEnabled:
          type: boolean
          deprecated: true
        bruteForceProtected:
          type: boolean
        permanentLockout:
          type: boolean
        maxTemporaryLockouts:
          format: int32
          type: integer
        maxFailureWaitSeconds:
          format: int32
          type: integer
        minimumQuickLoginWaitSeconds:
          format: int32
          type: integer
        waitIncrementSeconds:
          format: int32
          type: integer
        quickLoginCheckMilliSeconds:
          format: int64
          type: integer
        maxDeltaTimeSeconds:
          format: int32
          type: integer
        failureFactor:
          format: int32
          type: integer
        privateKey:
          type: string
          deprecated: true
        publicKey:
          type: string
          deprecated: true
        certificate:
          type: string
          deprecated: true
        codeSecret:
          type: string
          deprecated: true
        roles:
          $ref: '#/components/schemas/RolesRepresentation'
        groups:
          type: array
          items:
            $ref: '#/components/schemas/GroupRepresentation'
        defaultRoles:
          type: array
          items:
            type: string
          deprecated: true
        defaultRole:
          $ref: '#/components/schemas/RoleRepresentation'
        defaultGroups:
          type: array
          items:
            type: string
        requiredCredentials:
          uniqueItems: true
          type: array
          items:
            type: string
          deprecated: true
        passwordPolicy:
          type: string
        otpPolicyType:
          type: string
        otpPolicyAlgorithm:
          type: string
        otpPolicyInitialCounter:
          format: int32
          type: integer
        otpPolicyDigits:
          format: int32
          type: integer
        otpPolicyLookAheadWindow:
          format: int32
          type: integer
        otpPolicyPeriod:
          format: int32
          type: integer
        otpPolicyCodeReusable:
          type: boolean
        otpSupportedApplications:
          type: array
          items:
            type: string
        localizationTexts:
          type: object
          additionalProperties:
            type: object
            additionalProperties:
              type: string
        webAuthnPolicyRpEntityName:
          type: string
        webAuthnPolicySignatureAlgorithms:
          type: array
          items:
            type: string
        webAuthnPolicyRpId:
          type: string
        webAuthnPolicyAttestationConveyancePreference:
          type: string
        webAuthnPolicyAuthenticatorAttachment:
          type: string
        webAuthnPolicyRequireResidentKey:
          type: string
        webAuthnPolicyUserVerificationRequirement:
          type: string
        webAuthnPolicyCreateTimeout:
          format: int32
          type: integer
        webAuthnPolicyAvoidSameAuthenticatorRegister:
          type: boolean
        webAuthnPolicyAcceptableAaguids:
          type: array
          items:
            type: string
        webAuthnPolicyExtraOrigins:
          type: array
          items:
            type: string
        webAuthnPolicyPasswordlessRpEntityName:
          type: string
        webAuthnPolicyPasswordlessSignatureAlgorithms:
          type: array
          items:
            type: string
        webAuthnPolicyPasswordlessRpId:
          type: string
        webAuthnPolicyPasswordlessAttestationConveyancePreference:
          type: string
        webAuthnPolicyPasswordlessAuthenticatorAttachment:
          type: string
        webAuthnPolicyPasswordlessRequireResidentKey:
          type: string
        webAuthnPolicyPasswordlessUserVerificationRequirement:
          type: string
        webAuthnPolicyPasswordlessCreateTimeout:
          format: int32
          type: integer
        webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister:
          type: boolean
        webAuthnPolicyPasswordlessAcceptableAaguids:
          type: array
          items:
            type: string
        webAuthnPolicyPasswordlessExtraOrigins:
          type: array
          items:
            type: string
        clientProfiles:
          $ref: '#/components/schemas/ClientProfilesRepresentation'
        clientPolicies:
          $ref: '#/components/schemas/ClientPoliciesRepresentation'
        users:
          type: array
          items:
            $ref: '#/components/schemas/UserRepresentation'
        federatedUsers:
          type: array
          items:
            $ref: '#/components/schemas/UserRepresentation'
        scopeMappings:
          type: array
          items:
            $ref: '#/components/schemas/ScopeMappingRepresentation'
        clientScopeMappings:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/ScopeMappingRepresentation'
        clients:
          type: array
          items:
            $ref: '#/components/schemas/ClientRepresentation'
        clientScopes:
          type: array
          items:
            $ref: '#/components/schemas/ClientScopeRepresentation'
        defaultDefaultClientScopes:
          type: array
          items:
            type: string
        defaultOptionalClientScopes:
          type: array
          items:
            type: string
        browserSecurityHeaders:
          type: object
          additionalProperties:
            type: string
        smtpServer:
          type: object
          additionalProperties:
            type: string
        userFederationProviders:
          type: array
          items:
            $ref: '#/components/schemas/UserFederationProviderRepresentation'
        userFederationMappers:
          type: array
          items:
            $ref: '#/components/schemas/UserFederationMapperRepresentation'
        loginTheme:
          type: string
        accountTheme:
          type: string
        adminTheme:
          type: string
        emailTheme:
          type: string
        eventsEnabled:
          type: boolean
        eventsExpiration:
          format: int64
          type: integer
        eventsListeners:
          type: array
          items:
            type: string
        enabledEventTypes:
          type: array
          items:
            type: string
        adminEventsEnabled:
          type: boolean
        adminEventsDetailsEnabled:
          type: boolean
        identityProviders:
          type: array
          items:
            $ref: '#/components/schemas/IdentityProviderRepresentation'
        identityProviderMappers:
          type: array
          items:
            $ref: '#/components/schemas/IdentityProviderMapperRepresentation'
        protocolMappers:
          type: array
          items:
            $ref: '#/components/schemas/ProtocolMapperRepresentation'
        components:
          $ref: '#/components/schemas/MultivaluedHashMapStringComponentExportRepresentation'
        internationalizationEnabled:
          type: boolean
        supportedLocales:
          uniqueItems: true
          type: array
          items:
            type: string
        defaultLocale:
          type: string
        authenticationFlows:
          type: array
          items:
            $ref: '#/components/schemas/AuthenticationFlowRepresentation'
        authenticatorConfig:
          type: array
          items:
            $ref: '#/components/schemas/AuthenticatorConfigRepresentation'
        requiredActions:
          type: array
          items:
            $ref: '#/components/schemas/RequiredActionProviderRepresentation'
        browserFlow:
          type: string
        registrationFlow:
          type: string
        directGrantFlow:
          type: string
        resetCredentialsFlow:
          type: string
        clientAuthenticationFlow:
          type: string
        dockerAuthenticationFlow:
          type: string
        firstBrokerLoginFlow:
          type: string
        attributes:
          type: object
          additionalProperties:
            type: string
        keycloakVersion:
          type: string
        userManagedAccessAllowed:
          type: boolean
        social:
          type: boolean
          deprecated: true
        updateProfileOnInitialSocialLogin:
          type: boolean
          deprecated: true
        socialProviders:
          type: object
          additionalProperties:
            type: string
          deprecated: true
        applicationScopeMappings:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/ScopeMappingRepresentation'
          deprecated: true
        applications:
          type: array
          items:
            $ref: '#/components/schemas/ApplicationRepresentation'
          deprecated: true
        oauthClients:
          type: array
          items:
            $ref: '#/components/schemas/OAuthClientRepresentation'
          deprecated: true
        clientTemplates:
          type: array
          items:
            $ref: '#/components/schemas/ClientTemplateRepresentation'
          deprecated: true
        oAuth2DeviceCodeLifespan:
          format: int32
          type: integer
        oAuth2DevicePollingInterval:
          format: int32
          type: integer
    RequiredActionProviderRepresentation:
      type: object
      properties:
        alias:
          type: string
        name:
          type: string
        providerId:
          type: string
        enabled:
          type: boolean
        defaultAction:
          type: boolean
        priority:
          format: int32
          type: integer
        config:
          type: object
          additionalProperties:
            type: string
    ResourceOwnerRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
    ResourceRepresentation:
      type: object
      properties:
        _id:
          type: string
        name:
          type: string
        uris:
          uniqueItems: true
          type: array
          items:
            type: string
        type:
          type: string
        scopes:
          uniqueItems: true
          type: array
          items:
            $ref: '#/components/schemas/ScopeRepresentation'
        icon_uri:
          type: string
        owner:
          type: object
          allOf:
          - $ref: '#/components/schemas/ResourceOwnerRepresentation'
          readOnly: true
        ownerManagedAccess:
          type: boolean
        displayName:
          type: string
        attributes:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        uri:
          type: string
          writeOnly: true
          deprecated: true
        scopesUma:
          uniqueItems: true
          type: array
          items:
            $ref: '#/components/schemas/ScopeRepresentation'
    ResourceServerRepresentation:
      type: object
      properties:
        id:
          type: string
        clientId:
          type: string
        name:
          type: string
        allowRemoteResourceManagement:
          type: boolean
        policyEnforcementMode:
          $ref: '#/components/schemas/PolicyEnforcementMode'
        resources:
          type: array
          items:
            $ref: '#/components/schemas/ResourceRepresentation'
        policies:
          type: array
          items:
            $ref: '#/components/schemas/PolicyRepresentation'
        scopes:
          type: array
          items:
            $ref: '#/components/schemas/ScopeRepresentation'
        decisionStrategy:
          $ref: '#/components/schemas/DecisionStrategy'
    RoleRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        description:
          type: string
        scopeParamRequired:
          type: boolean
          deprecated: true
        composite:
          type: boolean
        composites:
          $ref: '#/components/schemas/Composites'
        clientRole:
          type: boolean
        containerId:
          type: string
        attributes:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
    RolesRepresentation:
      type: object
      properties:
        realm:
          type: array
          items:
            $ref: '#/components/schemas/RoleRepresentation'
        client:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/RoleRepresentation'
        application:
          type: object
          additionalProperties:
            type: array
            items:
              $ref: '#/components/schemas/RoleRepresentation'
          deprecated: true
    ScopeEnforcementMode:
      enum:
      - ALL
      - ANY
      - DISABLED
      type: string
    ScopeMappingRepresentation:
      type: object
      properties:
        self:
          type: string
        client:
          type: string
        clientTemplate:
          type: string
          deprecated: true
        clientScope:
          type: string
        roles:
          uniqueItems: true
          type: array
          items:
            type: string
    ScopeRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        iconUri:
          type: string
        policies:
          type: array
          items:
            $ref: '#/components/schemas/PolicyRepresentation'
        resources:
          type: array
          items:
            $ref: '#/components/schemas/ResourceRepresentation'
        displayName:
          type: string
    SocialLinkRepresentation:
      type: object
      properties:
        socialProvider:
          type: string
        socialUserId:
          type: string
        socialUsername:
          type: string
    UPAttribute:
      type: object
      properties:
        name:
          type: string
        displayName:
          type: string
        validations:
          type: object
          additionalProperties:
            type: object
            additionalProperties: {}
        annotations:
          type: object
          additionalProperties: {}
        required:
          $ref: '#/components/schemas/UPAttributeRequired'
        permissions:
          $ref: '#/components/schemas/UPAttributePermissions'
        selector:
          $ref: '#/components/schemas/UPAttributeSelector'
        group:
          type: string
        multivalued:
          type: boolean
    UPAttributePermissions:
      type: object
      properties:
        view:
          uniqueItems: true
          type: array
          items:
            type: string
        edit:
          uniqueItems: true
          type: array
          items:
            type: string
    UPAttributeRequired:
      type: object
      properties:
        roles:
          uniqueItems: true
          type: array
          items:
            type: string
        scopes:
          uniqueItems: true
          type: array
          items:
            type: string
    UPAttributeSelector:
      type: object
      properties:
        scopes:
          uniqueItems: true
          type: array
          items:
            type: string
    UPConfig:
      type: object
      properties:
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/UPAttribute'
        groups:
          type: array
          items:
            $ref: '#/components/schemas/UPGroup'
        unmanagedAttributePolicy:
          $ref: '#/components/schemas/UnmanagedAttributePolicy'
    UPGroup:
      type: object
      properties:
        name:
          type: string
        displayHeader:
          type: string
        displayDescription:
          type: string
        annotations:
          type: object
          additionalProperties: {}
    UnmanagedAttributePolicy:
      enum:
      - ENABLED
      - ADMIN_VIEW
      - ADMIN_EDIT
      type: string
    UserConsentRepresentation:
      type: object
      properties:
        clientId:
          type: string
        grantedClientScopes:
          type: array
          items:
            type: string
        createdDate:
          format: int64
          type: integer
        lastUpdatedDate:
          format: int64
          type: integer
        grantedRealmRoles:
          type: array
          items:
            type: string
          deprecated: true
    UserFederationMapperRepresentation:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        federationProviderDisplayName:
          type: string
        federationMapperType:
          type: string
        config:
          type: object
          additionalProperties:
            type: string
    UserFederationProviderRepresentation:
      type: object
      properties:
        id:
          type: string
        displayName:
          type: string
        providerName:
          type: string
        config:
          type: object
          additionalProperties:
            type: string
        priority:
          format: int32
          type: integer
        fullSyncPeriod:
          format: int32
          type: integer
        changedSyncPeriod:
          format: int32
          type: integer
        lastSync:
          format: int32
          type: integer
    UserManagedAccessConfig:
      type: object
    UserProfileAttributeGroupMetadata:
      type: object
      properties:
        name:
          type: string
        displayHeader:
          type: string
        displayDescription:
          type: string
        annotations:
          type: object
          additionalProperties: {}
    UserProfileAttributeMetadata:
      type: object
      properties:
        name:
          type: string
        displayName:
          type: string
        required:
          type: boolean
        readOnly:
          type: boolean
        annotations:
          type: object
          additionalProperties: {}
        validators:
          type: object
          additionalProperties:
            type: object
            additionalProperties: {}
        group:
          type: string
        multivalued:
          type: boolean
    UserProfileMetadata:
      type: object
      properties:
        attributes:
          type: array
          items:
            $ref: '#/components/schemas/UserProfileAttributeMetadata'
        groups:
          type: array
          items:
            $ref: '#/components/schemas/UserProfileAttributeGroupMetadata'
    UserRepresentation:
      type: object
      properties:
        id:
          type: string
        username:
          type: string
        firstName:
          type: string
        lastName:
          type: string
        email:
          type: string
        emailVerified:
          type: boolean
        attributes:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        userProfileMetadata:
          $ref: '#/components/schemas/UserProfileMetadata'
        self:
          type: string
        origin:
          type: string
        createdTimestamp:
          format: int64
          type: integer
        enabled:
          type: boolean
        totp:
          type: boolean
        federationLink:
          type: string
        serviceAccountClientId:
          type: string
        credentials:
          type: array
          items:
            $ref: '#/components/schemas/CredentialRepresentation'
        disableableCredentialTypes:
          uniqueItems: true
          type: array
          items:
            type: string
        requiredActions:
          type: array
          items:
            type: string
        federatedIdentities:
          type: array
          items:
            $ref: '#/components/schemas/FederatedIdentityRepresentation'
        realmRoles:
          type: array
          items:
            type: string
        clientRoles:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
        clientConsents:
          type: array
          items:
            $ref: '#/components/schemas/UserConsentRepresentation'
        notBefore:
          format: int32
          type: integer
        applicationRoles:
          type: object
          additionalProperties:
            type: array
            items:
              type: string
          deprecated: true
        socialLinks:
          type: array
          items:
            $ref: '#/components/schemas/SocialLinkRepresentation'
          deprecated: true
        groups:
          type: array
          items:
            type: string
        access:
          type: object
          additionalProperties:
            type: boolean
    UserSessionRepresentation:
      type: object
      properties:
        id:
          type: string
        username:
          type: string
        userId:
          type: string
        ipAddress:
          type: string
        start:
          format: int64
          type: integer
        lastAccess:
          format: int64
          type: integer
        rememberMe:
          type: boolean
        clients:
          type: object
          additionalProperties:
            type: string
