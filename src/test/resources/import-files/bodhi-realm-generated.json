{
  "realm": "bodhi",
  "enabled": true,
  "registrationAllowed": true,
  "registrationEmailAsUsername": true,
  "rememberMe": true,
  "verifyEmail": true,
  "loginWithEmailAllowed": true,
  "clientScopes": [
    {
      "name": "roles",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "true",
        "gui.order": "5",
        "consent.screen.text": "Roles"
      }
    },
    {
      "name": "scope_token_user",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "false"
      }
    },
    {
      "name": "scope_token_power_user",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "false"
      }
    },
    {
      "name": "scope_user_user",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "true",
        "gui.order": "10",
        "consent.screen.text": "Basic/Inference/Read-Only APIs"
      }
    },
    {
      "name": "scope_user_power_user",
      "protocol": "openid-connect",
      "attributes": {
        "include.in.token.scope": "true",
        "display.on.consent.screen": "true",
        "gui.order": "20",
        "consent.screen.text": "Basic/Inference/Read-Only + Write/Download-Models/Configure-Models APIs"
      }
    }
  ],
  "clients": [
    {
      "clientId": "client-bodhi-dev-console",
      "enabled": true,
      "clientAuthenticatorType": "client-secret",
      "secret": "change-me",
      "redirectUris": ["http://localhost:8888/index.html"],
      "webOrigins": ["+"],
      "bearerOnly": false,
      "consentRequired": false,
      "standardFlowEnabled": true,
      "implicitFlowEnabled": false,
      "directAccessGrantsEnabled": true,
      "serviceAccountsEnabled": true,
      "publicClient": false,
      "protocol": "openid-connect",
      "fullScopeAllowed": false
    }
  ],
  "users": [
        {
      "username": "<EMAIL>",
      "email": "<EMAIL>",
      "enabled": true,
      "emailVerified": true,
      "firstName": "Seed",
      "lastName": "User",
      "credentials": [
        {
          "type": "password",
          "value": "pass"
        }
      ]
    }
  ],
  "defaultDefaultClientScopes": [
    "basic"
  ],
  "defaultOptionalClientScopes": [
    "acr",
    "address",
    "email",
    "microprofile-jwt",
    "offline_access",
    "organization",
    "phone",
    "profile",
    "role_list",
    "roles",
    "saml_organization",
    "scope_token_power_user",
    "scope_token_user",
    "scope_user_power_user",
    "scope_user_user",
    "service_account",
    "web-origins",
  ]
}
