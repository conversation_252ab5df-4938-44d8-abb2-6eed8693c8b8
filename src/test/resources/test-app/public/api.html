<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="REST API testing interface for OAuth-protected endpoints">
  <meta name="keywords" content="api, rest, test, oauth, bearer, token">
  <title>API Test Interface</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 1000px;
      margin: 20px auto;
      padding: 20px;
      background-color: #f5f5f5;
    }

    .container {
      background-color: white;
      padding: 30px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
      color: #333;
    }

    input[type="text"],
    input[type="url"],
    select,
    textarea {
      width: 100%;
      padding: 12px;
      border: 2px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
      box-sizing: border-box;
      font-family: inherit;
    }

    textarea {
      min-height: 100px;
      resize: vertical;
      font-family: 'Courier New', monospace;
    }

    .btn {
      background-color: #007bff;
      color: white;
      padding: 12px 24px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      margin-right: 10px;
      margin-bottom: 10px;
    }

    .btn:hover {
      background-color: #0056b3;
    }

    .btn-secondary {
      background-color: #6c757d;
    }

    .btn-secondary:hover {
      background-color: #545b62;
    }

    .hidden {
      display: none;
    }

    .success {
      background-color: #d4edda;
      color: #155724;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #c3e6cb;
      margin: 20px 0;
    }

    .error {
      background-color: #f8d7da;
      color: #721c24;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #f5c6cb;
      margin: 20px 0;
    }

    .response-display {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 15px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      white-space: pre-wrap;
      word-break: break-all;
      max-height: 400px;
      overflow-y: auto;
      margin: 10px 0;
    }

    .status-info {
      background-color: #e7f3ff;
      border: 1px solid #b3d9ff;
      border-radius: 4px;
      padding: 10px;
      margin: 10px 0;
      font-weight: bold;
    }

    .loading {
      text-align: center;
      padding: 20px;
      font-style: italic;
      color: #666;
    }

    .token-info {
      background-color: #fff3cd;
      border: 1px solid #ffeaa7;
      border-radius: 4px;
      padding: 10px;
      margin-bottom: 20px;
      font-size: 14px;
    }

    .grid-2col {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
    }

    @media (max-width: 768px) {
      .grid-2col {
        grid-template-columns: 1fr;
      }
    }

    .clear-token-btn {
      float: right;
      padding: 5px 10px;
      font-size: 12px;
    }
  </style>
</head>

<body>
  <div class="container">
    <h1>API Test Interface</h1>
    <div id="api-section">
      <form id="api-form">
        <div class="grid-2col">
          <div class="form-group">
            <label for="http-method">HTTP Method:</label>
            <select id="http-method" required>
              <option value="GET">GET</option>
              <option value="POST" selected>POST</option>
              <option value="PUT">PUT</option>
              <option value="PATCH">PATCH</option>
              <option value="DELETE">DELETE</option>
            </select>
          </div>

          <div class="form-group">
            <label for="content-type">Content-Type:</label>
            <input type="text" id="content-type" value="application/json" />
          </div>
        </div>

        <div class="form-group">
          <label for="endpoint">API Endpoint URL:</label>
          <input type="url" id="endpoint" placeholder="http://localhost:8080/realms/bodhi/bodhi/apps" required />
        </div>

        <div class="form-group">
          <label for="headers">Additional Headers (one per line, format: Header: Value):</label>
          <textarea id="headers" placeholder="X-Custom-Header: value&#10;Another-Header: another-value"></textarea>
        </div>

        <div class="form-group">
          <label for="request-body">Request Body:</label>
          <textarea id="request-body"
            placeholder='{"name": "Test App", "description": "A test application", "redirectUris": ["http://localhost/callback"]}'></textarea>
        </div>

        <button type="submit" class="btn">Send Request</button>
        <button type="button" class="btn btn-secondary" onclick="resetForm()">Reset Form</button>
        <button type="button" class="btn btn-secondary" onclick="window.location.href='index.html'">Back to
          OAuth</button>
      </form>
    </div>

    <div id="loading-section">
      <div id="loading-message" class="loading">Ready</div>
    </div>

    <div id="response-section">
      <h2>API Response</h2>

      <div class="status-info">
        <strong>HTTP Status:</strong> <span id="response-status"></span>
      </div>

      <h3>Response Headers:</h3>
      <div id="response-headers" class="response-display"></div>

      <h3>Response Body:</h3>
      <div id="response-body" class="response-display"></div>

      <button class="btn" onclick="makeAnotherRequest()">Make Another Request</button>
    </div>
  </div>

  <script>
    // Initialize app on page load
    window.addEventListener('load', function () {
      // Pre-fill endpoint and request body
      document.getElementById('endpoint').value = 'http://localhost:8080/realms/bodhi/bodhi/apps';
      document.getElementById('request-body').value = JSON.stringify({
        name: "Test App",
        description: "A test application",
        redirectUris: ["http://localhost/callback"]
      }, null, 2);
      document.getElementById('loading-message').textContent = 'Ready';
    });

    // Handle API form submission
    document.getElementById('api-form').addEventListener('submit', function (e) {
      e.preventDefault();
      makeApiRequest();
    });

    async function makeApiRequest() {
      document.getElementById('loading-message').textContent = 'Sending API request...';

      // Clear previous error/success classes
      document.getElementById('response-section').classList.remove('error', 'success');

      try {
        const method = document.getElementById('http-method').value;
        const endpoint = document.getElementById('endpoint').value.trim();
        const contentType = document.getElementById('content-type').value.trim();
        const additionalHeaders = document.getElementById('headers').value.trim();
        const requestBody = document.getElementById('request-body').value.trim();

        if (!endpoint) {
          throw new Error('Please provide an API endpoint URL');
        }

        // Build headers
        const headers = {};

        if (contentType) {
          headers['Content-Type'] = contentType;
        }

        // Parse additional headers
        if (additionalHeaders) {
          const headerLines = additionalHeaders.split('\n');
          for (const line of headerLines) {
            const trimmedLine = line.trim();
            if (trimmedLine) {
              const colonIndex = trimmedLine.indexOf(':');
              if (colonIndex > 0) {
                const headerName = trimmedLine.substring(0, colonIndex).trim();
                const headerValue = trimmedLine.substring(colonIndex + 1).trim();
                headers[headerName] = headerValue;
              }
            }
          }
        }

        // Build request payload for backend API
        const requestPayload = {
          method: method,
          url: endpoint,
          headers: headers,
          body: (method !== 'GET' && method !== 'DELETE' && requestBody) ? requestBody : undefined
        };

        console.log('Making API request via backend proxy:', requestPayload);

        // Make the API request via backend proxy
        const response = await fetch('/api/proxy', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestPayload)
        });

        let responseData;
        try {
          responseData = await response.json();
        } catch (e) {
          responseData = { status: response.status, statusText: response.statusText, headers: {}, body: await response.text() };
        }

        // Show response regardless of status
        document.getElementById('response-status').textContent = `${responseData.status} ${responseData.statusText}`;

        const headerText = responseData.headers && typeof responseData.headers === 'object'
          ? Object.entries(responseData.headers).map(([key, value]) => `${key}: ${value}`).join('\n')
          : '';
        document.getElementById('response-headers').textContent = headerText;

        // Try to parse response body as JSON for pretty printing
        let formattedBody;
        try {
          const responseJson = JSON.parse(responseData.body);
          formattedBody = JSON.stringify(responseJson, null, 2);
        } catch (e) {
          // Not JSON, use as-is
          formattedBody = responseData.body;
        }
        document.getElementById('response-body').textContent = formattedBody;
        document.getElementById('loading-message').textContent = 'Ready';

        // Add error or success styling
        if (!response.ok || responseData.status >= 400) {
          document.getElementById('response-section').classList.add('error');
        } else {
          document.getElementById('response-section').classList.add('success');
        }

      } catch (error) {
        console.error('API request error:', error);
        document.getElementById('loading-message').textContent = `Error: ${error.message}`;
        document.getElementById('response-status').textContent = 'Error';
        document.getElementById('response-headers').textContent = '';
        document.getElementById('response-body').textContent = '';
        document.getElementById('response-section').classList.add('error');
      }
    }

    function makeAnotherRequest() {
      resetForm();
      document.getElementById('loading-message').textContent = 'Ready';
      document.getElementById('response-status').textContent = '';
      document.getElementById('response-headers').textContent = '';
      document.getElementById('response-body').textContent = '';
    }

    function resetForm() {
      document.getElementById('api-form').reset();
      document.getElementById('http-method').value = 'POST';
      document.getElementById('content-type').value = 'application/json';
      document.getElementById('endpoint').value = 'http://localhost:8080/realms/bodhi/bodhi/apps';
      document.getElementById('request-body').value = JSON.stringify({
        name: "Test App",
        description: "A test application",
        redirectUris: ["http://localhost/callback"]
      }, null, 2);
    }
  </script>
</body>

</html>