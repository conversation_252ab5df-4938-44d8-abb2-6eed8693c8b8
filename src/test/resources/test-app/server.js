const express = require('express');
const path = require('path');
const http = require('http');
const https = require('https');
const { URL } = require('url');

const app = express();

// Middleware to parse JSON bodies
app.use(express.json());

// Serve static files from public directory
app.use(express.static(path.join(__dirname, 'public')));

// API endpoint to proxy requests
app.post('/api/proxy', async (req, res) => {
  try {
    const { method, url, headers, body } = req.body;

    if (!method || !url) {
      return res.status(400).json({ error: 'Method and URL are required' });
    }

    // Parse the URL to determine if it's HTTP or HTTPS
    const parsedUrl = new URL(url);
    const isHttps = parsedUrl.protocol === 'https:';
    const httpModule = isHttps ? https : http;

    // Prepare request options
    const options = {
      hostname: parsedUrl.hostname,
      port: parsedUrl.port || (isHttps ? 443 : 80),
      path: parsedUrl.pathname + parsedUrl.search,
      method: method.toUpperCase(),
      headers: headers || {}
    };

    // Make the HTTP request
    const proxyReq = httpModule.request(options, (proxyRes) => {
      // Collect response headers
      const responseHeaders = {};
      Object.keys(proxyRes.headers).forEach(key => {
        responseHeaders[key] = proxyRes.headers[key];
      });

      // Collect response body
      let responseBody = '';
      proxyRes.on('data', chunk => {
        responseBody += chunk;
      });

      proxyRes.on('end', () => {
        // Send response back to client
        res.status(proxyRes.statusCode).json({
          status: proxyRes.statusCode,
          statusText: proxyRes.statusMessage,
          headers: responseHeaders,
          body: responseBody
        });
      });
    });

    // Handle request errors
    proxyReq.on('error', (error) => {
      console.error('Proxy request error:', error);
      res.status(500).json({ error: 'Request failed: ' + error.message });
    });

    // Send request body if present
    if (body && (method.toUpperCase() === 'POST' || method.toUpperCase() === 'PUT' || method.toUpperCase() === 'PATCH')) {
      proxyReq.write(typeof body === 'string' ? body : JSON.stringify(body));
    }

    proxyReq.end();

  } catch (error) {
    console.error('API proxy error:', error);
    res.status(500).json({ error: 'Internal server error: ' + error.message });
  }
});

// Start server
const PORT = process.env.PORT || 0; // Use 0 for dynamic port allocation
const server = app.listen(PORT, () => {
  const actualPort = server.address().port;
  console.log(`Test app server started on port ${actualPort}`);
  console.log(`Server URL: http://localhost:${actualPort}`);
});

// Export server for external access
module.exports = server; 