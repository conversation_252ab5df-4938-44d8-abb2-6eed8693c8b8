package com.bodhisearch;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.notNullValue;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.bodhisearch.util.ClientPair;
import com.bodhisearch.util.TokenPair;

/**
 * Integration test class for complete end-to-end workflows in the Bodhi
 * Extension.
 * 
 * This class focuses on testing full business processes and complex scenarios
 * that involve multiple components working together, such as:
 * - Complete token exchange flows
 * - End-to-end user workflows
 * - Complex permission scenarios
 * - Multi-step business processes
 */
public class OfflineTokenTest extends BaseTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(OfflineTokenTest.class);

  // ========================================
  // TOKEN EXCHANGE INTEGRATION TESTS
  // ========================================

  @Test
  public void testCompleteOfflineTokenFlow() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    TestUser adminUser = createUser();

    String clientToken = getTokenForClient(clientPair);
    makeResourceAdmin(clientToken, adminUser.email)
        .then()
        .statusCode(201);

    TokenPair userTokenPair = getUserTokenPairWith(clientPair,
        adminUser.email,
        adminUser.password,
        Arrays.asList("openid", "email", "profile", "roles", "offline_access", "scope_token_power_user"));

    // Phase 1: Token Exchange Flow - Convert regular token to offline token
    DecodedJWT originalJwt = JWT.decode(userTokenPair.access);
    String keyId = originalJwt.getKeyId();

    String[] offlineTokenPair = exchangeToOfflineToken(clientPair, userTokenPair.access,
        Arrays.asList("offline_access", "scope_token_power_user"));
    String offlineAccessToken = offlineTokenPair[0];
    String offlineRefreshToken = offlineTokenPair[1];

    // Phase 2: Verify offline token structure and properties
    DecodedJWT offlineJwt = JWT.decode(offlineAccessToken);
    String offlineClaimScope = offlineJwt.getClaim("scope").asString();
    List<String> offlineClaimScopes = Arrays.asList(offlineClaimScope.split(" "));
    assertEquals(new HashSet<>(Arrays.asList("offline_access", "scope_token_power_user")),
        new HashSet<>(offlineClaimScopes));
    assertTrue(offlineJwt.getClaim("resource_access").isMissing());

    // Phase 3: Test refresh token flow before logout
    TokenPair initialRefreshTokens = refreshTokenFlow(clientPair, offlineRefreshToken);
    DecodedJWT initialRefreshJwt = JWT.decode(initialRefreshTokens.access);
    String initialRefreshClaimScope = initialRefreshJwt.getClaim("scope").asString();
    List<String> initialRefreshClaimScopes = Arrays.asList(initialRefreshClaimScope.split(" "));
    assertEquals(new HashSet<>(Arrays.asList("offline_access", "scope_token_power_user")),
        new HashSet<>(initialRefreshClaimScopes));
    assertTrue(initialRefreshJwt.getClaim("resource_access").isMissing());
    assertEquals(keyId, initialRefreshJwt.getKeyId());

    // Phase 4: Logout the user - this should NOT invalidate offline tokens
    logoutUser(adminUser.email);

    TokenPair firstRefreshTokens = refreshTokenFlow(clientPair, initialRefreshTokens.refresh);
    assertThat("Should be able to refresh offline token after user logout",
        firstRefreshTokens.access, notNullValue());
    assertThat("Should receive new refresh token",
        firstRefreshTokens.refresh, notNullValue());

    // Phase 6: Verify the new access token has correct properties after logout
    DecodedJWT firstRefreshJwt = JWT.decode(firstRefreshTokens.access);
    String firstRefreshClaimScope = firstRefreshJwt.getClaim("scope").asString();
    List<String> firstRefreshClaimScopes = Arrays.asList(firstRefreshClaimScope.split(" "));
    assertEquals(new HashSet<>(Arrays.asList("offline_access", "scope_token_power_user")),
        new HashSet<>(firstRefreshClaimScopes));
    assertTrue(firstRefreshJwt.getClaim("resource_access").isMissing());
    assertEquals(keyId, firstRefreshJwt.getKeyId());
    assertEquals("RS256", firstRefreshJwt.getAlgorithm());

    // Phase 7: Verify the new offline token hasn't expired
    long now = System.currentTimeMillis() / 1000;
    assertTrue(firstRefreshJwt.getExpiresAt().getTime() / 1000 > now);

    // Phase 8: Verify we can refresh again with the new refresh token (multiple
    // refresh cycles)
    TokenPair secondRefreshTokens = refreshTokenFlow(clientPair, firstRefreshTokens.refresh);
    DecodedJWT secondRefreshJwt = JWT.decode(secondRefreshTokens.access);
    String secondRefreshClaimScope = secondRefreshJwt.getClaim("scope").asString();
    List<String> secondRefreshClaimScopes = Arrays.asList(secondRefreshClaimScope.split(" "));
    assertEquals(new HashSet<>(Arrays.asList("offline_access", "scope_token_power_user")),
        new HashSet<>(secondRefreshClaimScopes));
    assertTrue(secondRefreshJwt.getClaim("resource_access").isMissing());
    assertEquals(keyId, secondRefreshJwt.getKeyId());
    assertEquals("RS256", secondRefreshJwt.getAlgorithm());

    // Phase 9: Verify the second refresh token is also valid and not expired
    long nowAfterSecondRefresh = System.currentTimeMillis() / 1000;
    assertTrue(secondRefreshJwt.getExpiresAt().getTime() / 1000 > nowAfterSecondRefresh);
  }
}