package com.bodhisearch;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.Test;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.bodhisearch.util.ClientPair;
import com.bodhisearch.util.TokenPair;

/**
 * Test class for validating Keycloak configuration features.
 * 
 * This class tests Keycloak-specific behavior and configuration that affects
 * the Bodhi Extension functionality:
 * - Default scope configuration
 * - Token claim behavior
 * - Realm-level settings
 */
public class KeycloakConfigurationTest extends BaseTest {

  @Test
  public void testDefaultScopesAreOptional() {
    // Test that default scopes are properly configured as optional in the seeded
    // realm
    ClientPair clientPair = registerClientAndReturnClientPair();
    TestUser testUser = createUser();

    String clientId = clientPair.clientId;
    String clientSecret = clientPair.clientSecret;
    assertNotNull(clientId, "Client ID should not be null");
    assertNotNull(clientSecret, "Client secret should not be null");

    // Make test user a resource admin to ensure they have roles
    String serviceAccountToken = getTokenForClient(clientPair);
    makeResourceAdmin(serviceAccountToken, testUser.email)
        .then()
        .statusCode(201);

    // First: Request token with only basic scopes (no optional scopes)
    List<String> basicScopes = Arrays.asList("openid");
    TokenPair basicTokenPair = getUserTokenPairWith(clientPair, testUser.email, testUser.password, basicScopes);
    DecodedJWT basicJwt = JWT.decode(basicTokenPair.access);

    // Verify that optional scopes are not present when not requested
    List<String> optionalClaims = Arrays.asList(
        "acr",
        "allowed-origins",
        "web-origins",
        "roles",
        "email_verified",
        "name",
        "preferred_username",
        "given_name",
        "family_name",
        "email");

    for (String claim : optionalClaims) {
      assertTrue(basicJwt.getClaim(claim).isMissing(),
          claim + " claim should not be present when not requested (should be optional scope)");
    }

    // Second: Request token with optional scopes to verify they work when requested
    List<String> fullScopes = Arrays.asList("openid", "email", "profile", "roles");
    TokenPair fullTokenPair = getUserTokenPairWith(clientPair, testUser.email, testUser.password, fullScopes);
    DecodedJWT fullJwt = JWT.decode(fullTokenPair.access);

    // Verify that requested scopes ARE present when explicitly requested
    List<String> requestedClaims = Arrays.asList(
        "email_verified",
        "name",
        "preferred_username",
        "given_name",
        "family_name",
        "email");

    for (String claim : requestedClaims) {
      assertTrue(!fullJwt.getClaim(claim).isMissing(),
          claim + " claim should be present when email/profile scopes are requested");
    }

    // Verify that resource_access IS present since the user has roles
    assertTrue(!fullJwt.getClaim("resource_access").isMissing(),
        "resource_access should be present for users with roles");
  }
}