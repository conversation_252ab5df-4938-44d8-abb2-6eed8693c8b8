package com.bodhisearch.util;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.AriaRole;
import com.microsoft.playwright.options.LoadState;

/**
 * Utility class containing common UI test patterns for Playwright-based
 * integration tests.
 * This class provides reusable methods for:
 * - User creation through OAuth flows
 * - OAuth authentication flows
 * - JWT token verification
 * - Role verification in tokens
 */
public class UITestUtils {

  /**
   * Creates a new user through the UI OAuth registration flow.
   * 
   * @param page            The Playwright page instance
   * @param testAppUrl      The base URL of the test application
   * @param keycloakBaseUrl The base URL of the Keycloak server
   * @param clientPair      The client credentials to use
   * @param email           The email for the new user
   * @param password        The password for the new user
   * @param firstName       The first name for the new user
   * @param lastName        The last name for the new user
   * @return The access token obtained after successful registration
   */
  public static String createUserThroughUI(Page page, String testAppUrl, String keycloakBaseUrl,
      String realm, ClientPair clientPair, String email, String password, String firstName, String lastName) {

    page.navigate(testAppUrl);
    page.waitForLoadState(LoadState.NETWORKIDLE);

    // Configure OAuth client in test app
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Keycloak URL:")).fill(keycloakBaseUrl);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Client ID:")).fill(clientPair.clientId);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Client Secret:"))
        .fill(clientPair.clientSecret);

    // Start OAuth flow
    page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Start OAuth Flow")).click();
    page.waitForURL(url -> url.contains(keycloakBaseUrl + "/realms/" + realm + "/protocol/openid-connect/auth"));

    // Register new user
    page.getByRole(AriaRole.LINK, new Page.GetByRoleOptions().setName("Register")).click();
    page.waitForLoadState(LoadState.NETWORKIDLE);

    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("First name")).fill(firstName);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Last name")).fill(lastName);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Email")).fill(email);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Password").setExact(true)).fill(password);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Confirm password")).fill(password);

    page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Register")).click();

    // Wait for redirect back to test app and get access token
    page.waitForURL(url -> url.startsWith(testAppUrl));
    page.waitForSelector("#access-token", new Page.WaitForSelectorOptions().setTimeout(10000));

    return page.textContent("#access-token");
  }

  /**
   * Performs OAuth login flow for an existing user.
   * 
   * @param page            The Playwright page instance
   * @param testAppUrl      The base URL of the test application
   * @param keycloakBaseUrl The base URL of the Keycloak server
   * @param realm           The Keycloak realm
   * @param clientPair      The client credentials to use
   * @param email           The user's email
   * @param password        The user's password
   * @return The access token obtained after successful login
   */
  public static String performOAuthLogin(Page page, String testAppUrl, String keycloakBaseUrl,
      String realm, ClientPair clientPair, String email, String password) {

    page.navigate(testAppUrl);
    page.waitForLoadState(LoadState.NETWORKIDLE);

    // Configure OAuth client in test app
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Keycloak URL:")).fill(keycloakBaseUrl);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Client ID:")).fill(clientPair.clientId);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Client Secret:"))
        .fill(clientPair.clientSecret);

    // Start OAuth flow
    page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Start OAuth Flow")).click();
    page.waitForURL(url -> url.contains(keycloakBaseUrl + "/realms/" + realm + "/protocol/openid-connect/auth"));

    // Login with existing credentials
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Username or email")).fill(email);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Password")).fill(password);
    page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Sign In")).click();

    // Wait for redirect back to test app and get access token
    page.waitForURL(url -> url.startsWith(testAppUrl));
    page.waitForSelector("#access-token", new Page.WaitForSelectorOptions().setTimeout(10000));

    return page.textContent("#access-token");
  }

  /**
   * Verifies that an access token contains the expected resource roles.
   * 
   * @param accessToken   The JWT access token to verify
   * @param clientId      The client ID to check roles for
   * @param expectedRoles The list of roles that should be present
   */
  public static void verifyTokenRoles(String accessToken, String clientId, List<String> expectedRoles) {
    assertThat("Access token should not be null", accessToken, notNullValue());

    DecodedJWT jwt = JWT.decode(accessToken);

    if (expectedRoles.isEmpty()) {
      assertThat("User should have no resource_access claim when no roles expected",
          jwt.getClaim("resource_access").isMissing(), is(true));
      return;
    }

    @SuppressWarnings("unchecked")
    java.util.Map<String, Object> resourceAccess = (java.util.Map<String, Object>) jwt.getClaim("resource_access")
        .asMap().get(clientId);

    @SuppressWarnings("unchecked")
    java.util.List<String> actualRoles = (java.util.List<String>) resourceAccess.get("roles");

    assertThat("Should have expected number of roles", actualRoles.size(), is(expectedRoles.size()));
    assertThat("Should contain all expected roles", actualRoles.containsAll(expectedRoles), is(true));
  }

  /**
   * Verifies that an access token does not contain any resource roles.
   * 
   * @param accessToken The JWT access token to verify
   */
  public static void verifyNoResourceRoles(String accessToken) {
    assertThat("Access token should not be null", accessToken, notNullValue());

    DecodedJWT jwt = JWT.decode(accessToken);
    assertThat("User should have no resource_access claim",
        jwt.getClaim("resource_access").isMissing(), is(true));
  }

  /**
   * Verifies that an access token contains specific claims and properties.
   * 
   * @param accessToken      The JWT access token to verify
   * @param expectedAudience The expected audience claim
   * @param expectedUsername The expected username/subject
   */
  public static void verifyTokenProperties(String accessToken, String expectedAudience, String expectedUsername) {
    assertThat("Access token should not be null", accessToken, notNullValue());

    DecodedJWT jwt = JWT.decode(accessToken);

    if (expectedAudience != null) {
      assertThat("Token should have correct audience",
          jwt.getAudience().contains(expectedAudience), is(true));
    }

    if (expectedUsername != null) {
      assertThat("Token should have correct subject",
          jwt.getClaim("preferred_username").asString(), is(expectedUsername));
    }
  }

  /**
   * Common resource role constants for testing.
   */
  public static class Roles {
    public static final String RESOURCE_USER = "resource_user";
    public static final String RESOURCE_POWER_USER = "resource_power_user";
    public static final String RESOURCE_MANAGER = "resource_manager";
    public static final String RESOURCE_ADMIN = "resource_admin";

    public static final List<String> USER_ROLES = Arrays.asList(RESOURCE_USER);
    public static final List<String> POWER_USER_ROLES = Arrays.asList(RESOURCE_USER, RESOURCE_POWER_USER);
    public static final List<String> MANAGER_ROLES = Arrays.asList(RESOURCE_USER, RESOURCE_POWER_USER,
        RESOURCE_MANAGER);
    public static final List<String> ADMIN_ROLES = Arrays.asList(RESOURCE_USER, RESOURCE_POWER_USER, RESOURCE_MANAGER,
        RESOURCE_ADMIN);
  }

  /**
   * Helper class for generating test user data.
   */
  public static class TestUserGenerator {
    public static TestUserData generateUser(String prefix) {
      int randomSuffix = java.util.concurrent.ThreadLocalRandom.current().nextInt(1000, 9999);
      return new TestUserData(
          prefix + "-" + randomSuffix + "@example.com",
          prefix.substring(0, 1).toUpperCase() + prefix.substring(1) + "Password123!",
          prefix.substring(0, 1).toUpperCase() + prefix.substring(1),
          "User");
    }
  }

  /**
   * Data class for test user information.
   */
  public static class TestUserData {
    public final String email;
    public final String password;
    public final String firstName;
    public final String lastName;

    public TestUserData(String email, String password, String firstName, String lastName) {
      this.email = email;
      this.password = password;
      this.firstName = firstName;
      this.lastName = lastName;
    }
  }
}