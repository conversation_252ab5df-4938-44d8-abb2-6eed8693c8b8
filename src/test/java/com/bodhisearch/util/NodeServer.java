package com.bodhisearch.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.ServerSocket;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * A Node.js server wrapper for testing purposes.
 * Manages a Node.js process that serves static files and provides API
 * endpoints.
 */
public class NodeServer {
    private static final Logger LOGGER = LoggerFactory.getLogger(NodeServer.class);

    private Process nodeProcess;
    private int port;
    private String baseUrl;
    private final String appDirectory;
    private CompletableFuture<Void> serverStartFuture;

    public NodeServer(String appDirectory) {
        this.appDirectory = appDirectory;
    }

    /**
     * Start the Node.js server on an available port
     *
     * @return the port number the server is listening on
     * @throws Exception if server fails to start
     */
    public int start() throws Exception {
        // Find an available port
        this.port = findAvailablePort();

        // Resolve the app directory path
        Path appPath = Paths.get(appDirectory).toAbsolutePath();
        LOGGER.info("Starting Node.js server in directory: {}", appPath);

        // Install dependencies first
        installDependencies(appPath);

        // Start the Node.js server process
        ProcessBuilder processBuilder = new ProcessBuilder("node", "server.js");
        processBuilder.directory(appPath.toFile());
        processBuilder.environment().put("PORT", String.valueOf(port));

        // Redirect error stream to output stream for easier monitoring
        processBuilder.redirectErrorStream(true);

        nodeProcess = processBuilder.start();

        // Monitor the process output to determine when server is ready
        serverStartFuture = CompletableFuture.runAsync(() -> {
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(nodeProcess.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    LOGGER.info("Node.js server: {}", line);
                    if (line.contains("Test app server started on port")) {
                        // Server is ready
                        break;
                    }
                }
            } catch (IOException e) {
                LOGGER.error("Error reading Node.js server output", e);
            }
        });

        // Wait for server to start (with timeout)
        try {
            serverStartFuture.get(30, TimeUnit.SECONDS);
        } catch (Exception e) {
            stop();
            throw new Exception("Node.js server failed to start within 30 seconds", e);
        }

        this.baseUrl = "http://localhost:" + port;

        LOGGER.info("Node.js server started on port {} at URL: {}", port, baseUrl);

        return port;
    }

    /**
     * Install Node.js dependencies using npm
     *
     * @param appPath the path to the Node.js application
     * @throws Exception if dependency installation fails
     */
    private void installDependencies(Path appPath) throws Exception {
        LOGGER.info("Installing Node.js dependencies...");

        ProcessBuilder npmInstall = new ProcessBuilder("npm", "install");
        npmInstall.directory(appPath.toFile());
        npmInstall.redirectErrorStream(true);

        Process npmProcess = npmInstall.start();

        // Monitor npm install output
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(npmProcess.getInputStream()))) {
            String line;
            while ((line = reader.readLine()) != null) {
                LOGGER.debug("npm install: {}", line);
            }
        }

        int exitCode = npmProcess.waitFor();
        if (exitCode != 0) {
            throw new Exception("npm install failed with exit code: " + exitCode);
        }

        LOGGER.info("Node.js dependencies installed successfully");
    }

    /**
     * Stop the Node.js server
     */
    public void stop() {
        if (nodeProcess != null) {
            try {
                // Cancel the server monitoring future
                if (serverStartFuture != null) {
                    serverStartFuture.cancel(true);
                }

                // Terminate the Node.js process
                nodeProcess.destroy();

                // Wait for process to terminate (with timeout)
                if (!nodeProcess.waitFor(10, TimeUnit.SECONDS)) {
                    LOGGER.warn("Node.js server did not terminate gracefully, forcing termination");
                    nodeProcess.destroyForcibly();
                }

                LOGGER.info("Node.js server stopped");
            } catch (Exception e) {
                LOGGER.error("Error stopping Node.js server", e);
            }
        }
    }

    /**
     * Get the port the server is listening on
     * 
     * @return the port number
     */
    public int getPort() {
        return port;
    }

    /**
     * Get the base URL of the server
     * 
     * @return the base URL (e.g., "http://localhost:8090")
     */
    public String getBaseUrl() {
        return baseUrl;
    }

    /**
     * Check if the server is running
     * 
     * @return true if server is running, false otherwise
     */
    public boolean isRunning() {
        return nodeProcess != null && nodeProcess.isAlive();
    }

    /**
     * Find an available port by creating a temporary ServerSocket
     * 
     * @return an available port number
     * @throws IOException if no port is available
     */
    private int findAvailablePort() throws IOException {
        try (ServerSocket socket = new ServerSocket(0)) {
            socket.setReuseAddress(true);
            return socket.getLocalPort();
        }
    }
}