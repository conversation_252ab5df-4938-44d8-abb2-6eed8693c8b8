package com.bodhisearch.util;

import java.util.Arrays;
import java.util.List;

import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

import static io.restassured.RestAssured.given;

import com.bodhisearch.BodhiResourceProvider;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;

public class BodhiProviderClient {
  public final String newResourceUrl;
  public final String addToGroupUrl;
  public final String hasAdminUrl;
  public final String makeResourceAdminUrl;
  public final String requestAccessUrl;
  public final String newAppUrl;

  public BodhiProviderClient(String keycloakBaseUrl, String realm, String providerId) {
    this.newResourceUrl = String.format("%s/realms/%s/%s/resources", keycloakBaseUrl, realm, providerId);
    this.makeResourceAdminUrl = String.format("%s/realms/%s/%s/resources/make-resource-admin", keycloakBaseUrl, realm,
        providerId);
    this.addToGroupUrl = String.format("%s/realms/%s/%s/resources/add-user-to-group", keycloakBaseUrl, realm,
        providerId);
    this.hasAdminUrl = String.format("%s/realms/%s/%s/resources/has-resource-admin", keycloakBaseUrl, realm,
        providerId);
    this.requestAccessUrl = String.format("%s/realms/%s/%s/resources/request-access", keycloakBaseUrl, realm,
        providerId);
    this.newAppUrl = String.format("%s/realms/%s/%s/apps", keycloakBaseUrl, realm, providerId);
  }

  public JsonPath registerClient() {
    return registerClient("http://localhost:8090/");
  }

  public JsonPath registerClient(String redirectUri) {
    return registerClient(Arrays.asList(redirectUri));
  }

  public JsonPath registerClient(List<String> redirectUris) {
    ObjectMapper mapper = new ObjectMapper();
    String jsonBody;
    try {
      BodhiResourceProvider.ClientRequest clientRequest = new BodhiResourceProvider.ClientRequest("Test Resource",
          "A test resource", redirectUris);
      jsonBody = mapper.writeValueAsString(clientRequest);
    } catch (JsonProcessingException ex) {
      throw new RuntimeException("Failed to serialize ClientRequest", ex);
    }
    JsonPath response = given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .contentType(ContentType.JSON)
        .body(jsonBody)
        .when()
        .post(newResourceUrl)
        .then()
        .body("error_description", nullValue())
        .body("error", nullValue())
        .statusCode(201)
        .body("client_id", notNullValue())
        .body("client_secret", notNullValue())
        .body("scope", notNullValue())
        .extract()
        .jsonPath();
    return response;
  }

  public ClientPair registerClientAndReturnClientPair() {
    JsonPath response = registerClient();
    return new ClientPair(response.getString("client_id"), response.getString("client_secret"));
  }

  public ClientPair registerClientAndReturnClientPair(String redirectUri) {
    JsonPath response = registerClient(redirectUri);
    return new ClientPair(response.getString("client_id"), response.getString("client_secret"));
  }

  public ClientPair registerClientAndReturnClientPair(List<String> redirectUris) {
    JsonPath response = registerClient(redirectUris);
    return new ClientPair(response.getString("client_id"), response.getString("client_secret"));
  }

  public Response makeResourceAdmin(String token, String userToMakeFirstAdmin) {
    return given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", userToMakeFirstAdmin))
        .when()
        .post(makeResourceAdminUrl);
  }

  public Response addUserToGroup(String token, String username, String group) {
    return given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"%s\", \"add\": true}", username, group))
        .when()
        .post(addToGroupUrl);
  }

  public Response hasResourceAdmin(String token, String username) {
    return given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", username))
        .when()
        .post(hasAdminUrl);
  }

  public Response registerClientWithTestPrefix(List<String> redirectUris) {
    // Build JSON body with redirect URIs
    StringBuilder jsonBuilder = new StringBuilder();
    jsonBuilder.append("{\"redirect_uris\": [");
    for (int i = 0; i < redirectUris.size(); i++) {
      if (i > 0) {
        jsonBuilder.append(", ");
      }
      jsonBuilder.append("\"").append(redirectUris.get(i)).append("\"");
    }
    jsonBuilder.append("]}");

    return given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .contentType(ContentType.JSON)
        .body(jsonBuilder.toString())
        .when()
        .post(newResourceUrl + "?live_test=true");
  }

  public ClientPair registerClientWithTestPrefixAndReturnClientPair(List<String> redirectUris) {
    Response response = registerClientWithTestPrefix(redirectUris);
    response.then()
        .statusCode(201)
        .body("error", nullValue())
        .body("client_id", notNullValue())
        .body("client_secret", notNullValue());

    return new ClientPair(
        response.jsonPath().getString("client_id"),
        response.jsonPath().getString("client_secret"));
  }

  public String registerApp(String userAccessToken, BodhiResourceProvider.ClientRequest appRequest) {
    Response response = registerAppResponse(userAccessToken, appRequest);

    response.then()
        .statusCode(201)
        .body("error", nullValue())
        .body("client_id", notNullValue());

    return response.jsonPath().getString("client_id");
  }

  public Response registerAppResponse(String userAccessToken, BodhiResourceProvider.ClientRequest appRequest) {
    try {
      ObjectMapper mapper = new ObjectMapper();
      String jsonBody = mapper.writeValueAsString(appRequest);

      return given()
          .relaxedHTTPSValidation()
          .redirects().follow(false)
          .header("Authorization", "Bearer " + userAccessToken)
          .contentType(ContentType.JSON)
          .body(jsonBody)
          .when()
          .post(newAppUrl);
    } catch (JsonProcessingException e) {
      throw new RuntimeException("Failed to serialize AppRequest", e);
    }
  }

  public String requestAccess(String serviceAccountToken, String appClientId) {
    Response response = requestAccessResponse(serviceAccountToken, appClientId);

    response.then()
        .body("error", nullValue())
        .body("scope", notNullValue());

    return response.jsonPath().getString("scope");
  }

  public Response requestAccessResponse(String serviceAccountToken, String appClientId) {
    String jsonBody = String.format("{\"app_client_id\": \"%s\"}", appClientId);

    return given()
        .relaxedHTTPSValidation()
        .redirects().follow(false)
        .header("Authorization", "Bearer " + serviceAccountToken)
        .contentType(ContentType.JSON)
        .body(jsonBody)
        .when()
        .post(requestAccessUrl);
  }
}