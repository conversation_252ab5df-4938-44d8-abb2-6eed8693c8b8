package com.bodhisearch.integration;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsString;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.startsWith;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.keycloak.representations.idm.ClientRepresentation;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.bodhisearch.BodhiResourceProvider;
import com.bodhisearch.util.ClientPair;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.AriaRole;
import com.microsoft.playwright.options.LoadState;

public class AppRegistrationIntegrationTest extends BaseIntegrationTest {

  private static final Logger LOGGER = LoggerFactory.getLogger(AppRegistrationIntegrationTest.class);
  private static final ObjectMapper mapper = new ObjectMapper();

  // Shared test resources
  private static String sharedAccessToken;
  private static String sharedUserEmail;
  private static ClientPair devConsoleClient;
  private static String appsEndpoint;

  @BeforeAll
  public static void setupAppRegistrationTest() throws Exception {
    // Setup dev console client
    devConsoleClient = new ClientPair(BodhiResourceProvider.CLIENT_BODHI_DEV_CONSOLE, "change-me");

    // Generate shared test user details
    String username = "shareduser" + ThreadLocalRandom.current().nextInt(1000, 9999);
    String password = "SharedPassword123!";
    sharedUserEmail = username + "@example.com";
    String firstName = "Shared";
    String lastName = "User";
    // Create shared user and get access token
    String testAppUrl = getTestAppUrl();
    sharedAccessToken = oauthFlowSignUp(context, testAppUrl, devConsoleClient,
        sharedUserEmail, password, firstName, lastName, List.of("openid", "email", "profile", "roles"));
    appsEndpoint = keycloakBaseUrl + "/realms/" + REALM + "/bodhi/apps";
  }

  @Test
  public void testCompleteAppRegistrationWorkflow() throws Exception {
    String testAppUrl = getTestAppUrl();
    Page page = context.newPage();
    String apiUrl = testAppUrl + "/api.html";
    page.navigate(apiUrl);
    page.waitForLoadState(LoadState.NETWORKIDLE);
    assertThat(page.textContent("#loading-message"), containsString("Ready"));

    page.fill("#headers", "Authorization: Bearer " + sharedAccessToken);
    page.selectOption("#http-method", "POST");
    page.fill("#content-type", "application/json");
    page.fill("#endpoint", appsEndpoint);
    String requestBody = String.format(
        "{\"name\": \"Test App\", \"description\": \"Integration test app\", \"redirect_uris\": [\"http://localhost:3000/callback\"]}");
    page.fill("#request-body", requestBody);
    page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Send Request")).click();

    // Wait for API response and verify success
    page.waitForFunction("() => document.querySelector('#loading-message').textContent === 'Ready'");
    String responseStatus = page.textContent("#response-status");
    assertThat("Response should be 201 Created", responseStatus, containsString("201"));

    // Extract response data and validate
    String responseBody = page.textContent("#response-body");
    JsonNode responseJson = mapper.readTree(responseBody);

    assertThat("Response should contain clientId", responseJson.has("client_id"), is(true));
    String appClientId = responseJson.get("client_id").asText();
    assertThat("Client ID should start with app-", appClientId, startsWith("app-"));

    validateCreatedAppClient(appClientId, "Test App", "Integration test app",
        List.of("http://localhost:3000/callback"));

    validateAppClientRolesAndGroups(appClientId, sharedUserEmail);
  }

  @Test
  public void testAppRegistrationWithInvalidToken() throws Exception {
    String testAppUrl = getTestAppUrl();
    Page page = context.newPage();
    String apiUrl = testAppUrl + "/api.html";
    page.navigate(apiUrl);
    page.waitForLoadState(LoadState.NETWORKIDLE);
    assertThat(page.textContent("#loading-message"), containsString("Ready"));

    page.fill("#headers", "Authorization: Bearer invalid-token." + sharedAccessToken);
    page.selectOption("#http-method", "POST");
    page.fill("#content-type", "application/json");
    page.fill("#endpoint", appsEndpoint);
    String requestBody = String.format(
        "{\"name\": \"Test App\", \"description\": \"Integration test app\", \"redirect_uris\": [\"http://localhost:3000/callback\"]}");
    page.fill("#request-body", requestBody);
    page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Send Request")).click();

    page.waitForFunction(
        "() => document.querySelector('#loading-message').textContent.startsWith('Error') || document.querySelector('#loading-message').textContent === 'Ready'",
        null, new Page.WaitForFunctionOptions().setTimeout(5000));
    String responseBody = page.textContent("#response-body");
    JsonNode responseJson = mapper.readTree(responseBody);
    String error = responseJson.get("error").asText();
    assertThat("Should show error", error, is("invalid session"));
  }

  @Test
  public void testAppRegistrationErrorHandling() throws Exception {
    String testAppUrl = getTestAppUrl();
    Page page = context.newPage();
    String apiUrl = testAppUrl + "/api.html";
    page.navigate(apiUrl);
    page.waitForLoadState(LoadState.NETWORKIDLE);
    assertThat(page.textContent("#loading-message"), containsString("Ready"));

    page.fill("#headers", "Authorization: Bearer " + sharedAccessToken);
    page.selectOption("#http-method", "POST");
    page.fill("#content-type", "application/json");
    page.fill("#endpoint", appsEndpoint);
    page.fill("#request-body", "");
    page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Send Request")).click();

    page.waitForFunction(
        "() => document.querySelector('#loading-message').textContent.startsWith('Error') || document.querySelector('#loading-message').textContent === 'Ready'",
        null, new Page.WaitForFunctionOptions().setTimeout(5000));
    String responseBody = page.textContent("#response-body");
    JsonNode responseJson = mapper.readTree(responseBody);
    String error = responseJson.get("error").asText();
    assertThat("Should show error", error, is("Cannot read field \"name\" because \"request\" is null"));
  }

  private static void validateCreatedAppClient(String clientId, String expectedName, String expectedDescription,
      List<String> expectedRedirectUris) {

    // Fetch client from Keycloak using the container's admin client
    List<ClientRepresentation> clients = keycloak.getKeycloakAdminClient()
        .realm(REALM).clients().findByClientId(clientId);
    assertThat("Client should exist", clients.size(), is(1));

    ClientRepresentation client = clients.get(0);

    // Validate client configuration
    assertThat(client.isEnabled(), is(true));
    assertThat(client.isPublicClient(), is(true));
    assertThat(client.isServiceAccountsEnabled(), is(false));
    assertThat(client.isDirectAccessGrantsEnabled(), is(false));
    assertThat(client.isStandardFlowEnabled(), is(true));
    assertThat(client.isConsentRequired(), is(true));
    assertThat(client.isFullScopeAllowed(), is(false));

    // Validate redirect URIs
    for (String expectedUri : expectedRedirectUris) {
      assertThat("Client should have expected redirect URI: " + expectedUri,
          client.getRedirectUris().contains(expectedUri), is(true));
    }

    // Validate web origins
    assertThat("Client should have web origins configured",
        client.getWebOrigins().contains("+"), is(true));

    LOGGER.info("App client validation passed for client: {}", clientId);
  }

  private static void validateAppClientRolesAndGroups(String clientId, String userEmail) {
    // Get the admin client from the container
    var adminClient = keycloak.getKeycloakAdminClient();

    // Validate client has admin role
    List<ClientRepresentation> clientList = adminClient.realm(REALM).clients().findByClientId(clientId);
    assertThat("Client should exist", clientList.size(), is(1));
    String clientUuid = clientList.get(0).getId();

    List<RoleRepresentation> roles = adminClient.realm(REALM).clients().get(clientUuid).roles().list();
    boolean hasAdminRole = roles.stream().anyMatch(role -> "admin".equals(role.getName()));
    assertThat("Client should have admin role", hasAdminRole, is(true));

    // Validate group structure
    String expectedGroupPath = "/users-" + clientId;
    List<GroupRepresentation> groups = adminClient.realm(REALM).groups().groups();

    GroupRepresentation topLevelGroup = groups.stream()
        .filter(g -> expectedGroupPath.equals(g.getPath()))
        .findFirst()
        .orElse(null);
    assertThat("Top-level group should exist", topLevelGroup, notNullValue());
    assertThat("Top-level group should have subgroups", topLevelGroup.getSubGroupCount() > 0, is(true));

    // Validate admins subgroup
    List<GroupRepresentation> subGroups = adminClient.realm(REALM).groups()
        .group(topLevelGroup.getId()).getSubGroups(0, -1, false);

    GroupRepresentation adminsGroup = subGroups.stream()
        .filter(g -> "admins".equals(g.getName()))
        .findFirst()
        .orElse(null);
    assertThat("Admins subgroup should exist", adminsGroup, notNullValue());

    // Validate user is in admins group
    List<UserRepresentation> users = adminClient.realm(REALM).users().search(userEmail);
    assertThat("User should exist", users.size(), is(1));

    UserRepresentation user = users.get(0);
    List<GroupRepresentation> userGroups = adminClient.realm(REALM).users()
        .get(user.getId()).groups(null, null);

    boolean isInAdminsGroup = userGroups.stream()
        .anyMatch(g -> (expectedGroupPath + "/admins").equals(g.getPath()));
    assertThat("User should be in admins group", isInAdminsGroup, is(true));

    LOGGER.info("App client roles and groups validation passed for client: {}", clientId);
  }
}