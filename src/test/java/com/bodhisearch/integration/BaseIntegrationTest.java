package com.bodhisearch.integration;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;

import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import com.bodhisearch.templates.RealmConfigGenerator;
import com.bodhisearch.util.BodhiProviderClient;
import com.bodhisearch.util.ClientPair;
import com.bodhisearch.util.KeycloakAdminClient;
import com.bodhisearch.util.KeycloakTestUtils;
import com.bodhisearch.util.NodeServer;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.AriaRole;
import com.microsoft.playwright.options.LoadState;

import dasniko.testcontainers.keycloak.KeycloakContainer;
import io.restassured.RestAssured;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;

@Testcontainers
public class BaseIntegrationTest {
  public static final String REALM = "bodhi";
  public static final String PROVIDER_ID = "bodhi";
  public static final Logger LOGGER = LoggerFactory.getLogger(BaseIntegrationTest.class);

  protected static BodhiProviderClient bodhiProviderClient;
  protected static KeycloakAdminClient keycloakAdminClient;
  protected static String keycloakBaseUrl;
  protected static String registerClientUrl;
  protected static NodeServer testAppServer;

  protected static Playwright playwright;
  protected static Browser browser;
  protected static BrowserContext context;

  @Container
  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:26.2.5")
      .withFeaturesEnabled("token-exchange")
      .withProviderClassesFrom("target/classes");

  @BeforeAll
  public static void setupKeycloakForIntegration() throws Exception {
    // Start test app server
    int port = startTestAppServer();

    // Generate realm configuration specifically for integration tests
    String filename = "./src/test/resources/import-files/bodhi-realm-integration-generated.json";
    RealmConfigGenerator.generateForIntegrationTests(filename, port);

    // Import realm configuration
    keycloakBaseUrl = keycloak.getAuthServerUrl();
    KeycloakTestUtils.importFile(keycloakBaseUrl, keycloak.getAdminUsername(),
        keycloak.getAdminPassword(), filename);

    // Setup utility classes
    bodhiProviderClient = new BodhiProviderClient(keycloakBaseUrl, REALM, PROVIDER_ID);
    keycloakAdminClient = new KeycloakAdminClient(keycloakBaseUrl, REALM, keycloak.getKeycloakAdminClient());
    registerClientUrl = String.format("%s/realms/%s/%s/clients", keycloakBaseUrl, REALM, PROVIDER_ID);

    // Initialize shared test resources
    playwright = Playwright.create();
    boolean isHeadless = System.getenv("CI") != null;
    browser = playwright.chromium().launch(new BrowserType.LaunchOptions().setHeadless(isHeadless));
    context = browser.newContext();
  }

  @AfterAll
  public static void tearDownAppRegistrationTest() {
    stopTestAppServer();
    if (context != null) {
      context.close();
    }
    if (browser != null) {
      browser.close();
    }
    if (playwright != null) {
      playwright.close();
    }
  }

  protected static int startTestAppServer() {
    try {
      // Use Node.js server to serve the test app
      testAppServer = new NodeServer("src/test/resources/test-app");
      int port = testAppServer.start();
      LOGGER.info("Test app server started on port {} at URL: {}", port, testAppServer.getBaseUrl());
      return port;
    } catch (Exception e) {
      LOGGER.error("Failed to start test app server", e);
      throw new RuntimeException(e);
    }
  }

  protected static void stopTestAppServer() {
    if (testAppServer != null) {
      testAppServer.stop();
      LOGGER.info("Test app server stopped");
    }
  }

  protected static String getTestAppUrl() {
    return testAppServer != null ? testAppServer.getBaseUrl() : null;
  }

  protected static int getTestAppPort() {
    return testAppServer != null ? testAppServer.getPort() : 0;
  }

  protected ClientPair registerClientAndReturnClientPair(int port) {
    String redirectUri = "http://localhost:" + port + "/index.html";
    return bodhiProviderClient.registerClientAndReturnClientPair(redirectUri);
  }

  // Delegate methods to appropriate clients
  protected ClientPair registerClientAndReturnClientPair(String redirectUri) {
    return bodhiProviderClient.registerClientAndReturnClientPair(redirectUri);
  }

  protected Response makeResourceAdmin(String token, String userToMakeFirstAdmin) {
    return bodhiProviderClient.makeResourceAdmin(token, userToMakeFirstAdmin);
  }

  protected Response addUserToGroupViaAPI(String token, String testUser, String group) {
    return bodhiProviderClient.addUserToGroup(token, testUser, group);
  }

  protected String getTokenForClient(ClientPair clientPair) {
    return keycloakAdminClient.getTokenForClient(clientPair);
  }

  protected static String oauthFlowSignUp(BrowserContext context, String testAppUrl, ClientPair clientPair,
      String email, String password, String firstName, String lastName, List<String> scopes) throws Exception {
    context.clearCookies();
    Page page = context.newPage();
    launchOAuthFlow(page, testAppUrl, clientPair, scopes);

    // Register new user
    page.getByRole(AriaRole.LINK, new Page.GetByRoleOptions().setName("Register")).click();
    page.waitForLoadState(LoadState.NETWORKIDLE);

    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("First name")).fill(firstName);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Last name")).fill(lastName);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Email")).fill(email);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Password").setExact(true)).fill(password);
    page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Confirm password")).fill(password);

    page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Register")).click();

    // Wait for redirect back to test app and get access token
    page.waitForURL(url -> url.startsWith(testAppUrl));
    page.waitForSelector(".success", new Page.WaitForSelectorOptions().setTimeout(10000));

    String accessToken = page.textContent("#access-token");
    page.close();
    assertThat("Access token should not be null", accessToken, notNullValue());
    assertThat("Access token should not be empty", accessToken.length() > 0, is(true));
    return accessToken;
  }

  protected static String oauthFlowLogin(BrowserContext context, String testAppUrl, ClientPair clientPair,
      String email, String password, List<String> scopes, boolean consent) throws Exception {
    context.clearCookies();
    try (Page page = context.newPage()) {
      launchOAuthFlow(page, testAppUrl, clientPair, scopes);
      page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Email")).fill(email);
      page.getByRole(AriaRole.TEXTBOX, new Page.GetByRoleOptions().setName("Password").setExact(true)).fill(password);
      page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Sign In")).click();
      if (consent) {
        page.waitForFunction("() => document.querySelector('#kc-page-title').textContent.includes('Grant Access to')");
        page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Yes")).click();
      } // Wait for redirect back to test app and get access token
      page.waitForURL(url -> url.startsWith(testAppUrl));
      page.waitForSelector(".success", new Page.WaitForSelectorOptions().setTimeout(10000));
      String accessToken = page.textContent("#access-token");
      assertThat("Access token should not be null", accessToken, notNullValue());
      assertThat("Access token should not be empty", accessToken.length() > 0, is(true));
      return accessToken;
    }
  }

  protected static void launchOAuthFlow(Page page, String testAppUrl, ClientPair clientPair, List<String> scopes) {
    page.navigate(testAppUrl);
    page.waitForLoadState(LoadState.NETWORKIDLE);
    page.fill("#auth-server-url", keycloakBaseUrl);
    page.fill("#realm", REALM);
    page.fill("#client-id", clientPair.clientId);
    page.fill("#redirect-uri", testAppUrl + "/index.html");
    page.fill("#scope", String.join(" ", scopes));

    if (clientPair.clientSecret != null && !clientPair.clientSecret.isEmpty()) {
      page.check("#confidential-client");
      page.fill("#client-secret", clientPair.clientSecret);
    } else {
      page.uncheck("#confidential-client");
    }
    page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("Start OAuth Flow")).click();
    page.waitForURL(url -> url.contains(keycloakBaseUrl + "/realms/" + REALM + "/protocol/openid-connect/auth"));
  }

  public static RequestSpecification given() {
    return RestAssured.given().relaxedHTTPSValidation().redirects().follow(false);
  }
}