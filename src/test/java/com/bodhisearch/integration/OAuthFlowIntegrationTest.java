package com.bodhisearch.integration;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.is;

import java.util.Base64;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import org.junit.jupiter.api.Test;

import com.bodhisearch.util.ClientPair;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

public class OAuthFlowIntegrationTest extends BaseIntegrationTest {
  private static final ObjectMapper mapper = new ObjectMapper();

  @Test
  public void testCompleteOAuthFlow() throws Exception {
    String testAppUrl = getTestAppUrl();
    String redirectUri = testAppUrl + "/index.html";
    ClientPair clientPair = registerClientAndReturnClientPair(redirectUri);

    String username = "testuser" + ThreadLocalRandom.current().nextInt(1000, 9999);
    String password = "TestPassword123!";
    String email = username + "@example.com";
    String firstName = "Test";
    String lastName = "User";

    String accessToken = oauthFlowSignUp(context, testAppUrl, clientPair,
        email, password, firstName, lastName, List.of("openid", "email", "profile", "roles"));
    validateJWTToken(accessToken);
  }

  private void validateJWTToken(String token) throws JsonMappingException, JsonProcessingException {
    String[] parts = token.split("\\.");
    assertThat("JWT should have 3 parts", parts.length, is(3));
    String payload = new String(Base64.getUrlDecoder().decode(parts[1]));
    JsonNode payloadNode = mapper.readTree(payload);
    assertThat("Token should have 'sub' claim", payloadNode.has("sub"), is(true));
    assertThat("Token should have 'iss' claim", payloadNode.has("iss"), is(true));
    assertThat("Token should have 'exp' claim", payloadNode.has("exp"), is(true));
    String expectedIssuer = keycloakBaseUrl + "/realms/" + REALM;
    assertThat("Token issuer should match", payloadNode.get("iss").asText(), is(expectedIssuer));
  }
}