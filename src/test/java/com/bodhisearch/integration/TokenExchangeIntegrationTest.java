package com.bodhisearch.integration;

import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.hasItem;
import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.bodhisearch.BodhiResourceProvider.ClientRequest;
import com.bodhisearch.util.ClientPair;

public class TokenExchangeIntegrationTest extends BaseIntegrationTest {
  @Test
  public void testTokenExchangeWorkflow() throws Exception {
    String testAppUrl = getTestAppUrl();
    ClientPair devConsoleClient = new ClientPair("client-bodhi-dev-console", "change-me");
    String devConsoleAccessToken = oauthFlowLogin(context, testAppUrl, devConsoleClient, "<EMAIL>", "pass",
        Arrays.asList("openid", "email", "profile", "roles"), false);
    String clientAppId = bodhiProviderClient.registerApp(devConsoleAccessToken,
        new ClientRequest("Token Test App", "Token Test App for testing", Arrays.asList(testAppUrl + "/index.html")));
    ClientPair clientResource = registerClientAndReturnClientPair(getTestAppPort());
    String tokenServiceResource = getTokenForClient(clientResource);
    bodhiProviderClient.makeResourceAdmin(tokenServiceResource, "<EMAIL>")
        .then()
        .statusCode(201);
    String adminToken = oauthFlowLogin(context, testAppUrl, clientResource, "<EMAIL>", "pass",
        Arrays.asList("openid", "email", "profile", "roles"), false);
    bodhiProviderClient.addUserToGroup(adminToken, "<EMAIL>", "power-users").then()
        .body("error", nullValue())
        .statusCode(201);
    String resourceScope = bodhiProviderClient.requestAccessResponse(tokenServiceResource, clientAppId)
        .then()
        .body("error", nullValue())
        .statusCode(201)
        .body("scope", notNullValue())
        .extract()
        .jsonPath()
        .getString("scope");
    assertThat(resourceScope, equalTo("scope_" + clientResource.clientId));
    String powerUserToken = oauthFlowLogin(context, testAppUrl, new ClientPair(clientAppId, null),
        "<EMAIL>", "pass",
        List.of("openid", "email", "profile", "roles", "scope_user_user", resourceScope), true);
    DecodedJWT jwt = JWT.decode(powerUserToken);
    List<String> scope = Arrays.asList(jwt.getClaim("scope").asString().split(" ")).stream()
        .filter(s -> s.startsWith("scope_"))
        .toList();
    assertThat(scope, hasSize(2));
    assertThat(scope, hasItem(resourceScope));
    assertThat(scope, hasItem("scope_user_user"));

    String exchangedToken = keycloakAdminClient.exchangeToken(clientResource, powerUserToken,
        Arrays.asList("openid", "email", "profile", "roles", "scope_user_power_user"));
    jwt = JWT.decode(exchangedToken);
    String azp = jwt.getClaim("azp").asString();
    assertThat(clientResource.clientId, is(azp));
    List<String> scopes = Arrays.asList(jwt.getClaim("scope").asString().split(" "));
    assertThat(scopes, hasItem("scope_user_power_user"));
    Map<String, Object> resourceAccess = jwt.getClaim("resource_access").asMap();
    List<String> roles = ((Map<String, List<String>>) resourceAccess.get(clientResource.clientId)).get("roles");
    assertThat(roles, hasItem("resource_power_user"));
    assertThat(roles, hasItem("resource_user"));
  }
}