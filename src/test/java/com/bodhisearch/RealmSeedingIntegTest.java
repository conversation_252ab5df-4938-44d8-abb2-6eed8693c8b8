package com.bodhisearch;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.bodhisearch.templates.RealmConfigGenerator;

public class RealmSeedingIntegTest extends BaseSeedingTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(RealmSeedingIntegTest.class);

  private static final String GENERATED_JSON_PATH = "./src/test/resources/import-files/bodhi-realm-integration-generated.json";
  private static final int DEFAULT_APP_PORT = 8888;

  @BeforeAll
  public static void setupRealmSeedingStatic() {
    RealmSeedingIntegTest test = new RealmSeedingIntegTest();
    test.setupRealmSeeding();
  }

  @Test
  public void testTemplateGenerationWorksCorrectly() {
    String generatedFile = getGeneratedJsonPath();
    java.io.File file = new java.io.File(generatedFile);
    assertTrue(file.exists(), "Generated file should exist");
    assertTrue(file.length() > 0, "Generated file should not be empty");

    LOGGER.info("Template generation test completed successfully for: {}", generatedFile);
  }

  @Override
  protected void generateRealmConfig() {
    // Generate using the integration realm setup template
    RealmConfigGenerator.generateForIntegrationTests(GENERATED_JSON_PATH, DEFAULT_APP_PORT);
  }

  @Override
  protected String getGeneratedJsonPath() {
    return GENERATED_JSON_PATH;
  }

  @Override
  protected void validateSpecificConfiguration() {
    assertFalse(realm.toRepresentation().isVerifyEmail(),
        "Integration setup should have email verification disabled");
    LOGGER.info("Integration realm setup validation completed successfully");
  }
}