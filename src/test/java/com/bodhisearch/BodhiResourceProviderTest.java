package com.bodhisearch;

import static com.bodhisearch.BodhiResourceProvider.RESOURCE_ADMIN;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_MANAGER;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_POWER_USER;
import static com.bodhisearch.BodhiResourceProvider.RESOURCE_USER;
import static org.hamcrest.MatcherAssert.assertThat;
import static org.hamcrest.Matchers.containsInAnyOrder;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.is;
import static org.hamcrest.Matchers.notNullValue;
import static org.hamcrest.Matchers.nullValue;
import static org.hamcrest.Matchers.startsWith;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.keycloak.admin.client.resource.GroupResource;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.admin.client.resource.RoleScopeResource;
import org.keycloak.admin.client.resource.UserResource;
import org.keycloak.representations.idm.ClientRepresentation;
import org.keycloak.representations.idm.GroupRepresentation;
import org.keycloak.representations.idm.RoleRepresentation;
import org.keycloak.representations.idm.UserRepresentation;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.bodhisearch.util.ClientPair;
import com.bodhisearch.util.TokenPair;

import io.restassured.http.ContentType;
import io.restassured.path.json.JsonPath;
import io.restassured.response.Response;

/**
 * Test class for BodhiResourceProvider functionality.
 *
 * This class tests all endpoints and methods provided by BodhiResourceProvider:
 * - Client registration and creation (newResource endpoint)
 * - Admin management (makeFirstResourceAdmin endpoint)
 * - User group management (addUserToGroup endpoint)
 * - Client permission validation (hasResourceAdmin endpoint)
 * - Client permission restrictions and security
 * - Error handling and edge cases
 */
public class BodhiResourceProviderTest extends BaseTest {

  // ========================================
  // CLIENT REGISTRATION TESTS
  // ========================================

  @Test
  public void testRegisterResourceCreatesClientWithCorrectConfiguration() {
    JsonPath jsonPath = registerClient();
    String clientId = jsonPath.getString("client_id");
    String clientSecret = jsonPath.getString("client_secret");
    String scopeName = jsonPath.getString("scope");

    // Verify client configuration
    ClientRepresentation fetchedClient = realm.clients().findByClientId(clientId).get(0);
    assertThat(fetchedClient.isEnabled(), is(true));
    assertThat(fetchedClient.getRedirectUris(), containsInAnyOrder("http://bodhiapp.localhost/app/callback"));
    assertThat(fetchedClient.getWebOrigins(), containsInAnyOrder("+"));
    assertThat(fetchedClient.isConsentRequired(), is(false));
    assertThat(fetchedClient.isStandardFlowEnabled(), is(true));
    assertThat(fetchedClient.isDirectAccessGrantsEnabled(), is(true));
    assertThat(fetchedClient.isServiceAccountsEnabled(), is(true));
    assertThat(fetchedClient.isPublicClient(), is(false));
    assertThat(fetchedClient.isFullScopeAllowed(), is(false));
    assertThat(fetchedClient.getAttributes().get("standard.token.exchange.enabled"), equalTo("true"));

    // Verify group structure and roles
    Optional<GroupRepresentation> group = realm.groups().groups().stream()
        .filter(g -> g.getPath().equals(String.format("/users-%s", clientId))).findFirst();
    assertThat(group.isPresent(), is(true));
    assertThat(group.get().getSubGroupCount(), is(4l));

    GroupResource topLevelGroupResource = realm.groups().group(group.get().getId());
    Map<String, Map<String, Set<String>>> groupRoleMap = topLevelGroupResource.getSubGroups(0, -1, false).stream()
        .collect(Collectors.toConcurrentMap(g -> {
          return ((GroupRepresentation) g).getPath();
        }, g -> {
          return g.getClientRoles().keySet().stream()
              .collect(Collectors.toMap(k -> k, v -> new HashSet<>(g.getClientRoles().get(v))));
        }));

    String topLevelGroup = String.format("/users-%s", clientId);
    String usersGroup = String.format("%s/users", topLevelGroup);
    String powerUsersGroup = String.format("%s/power-users", topLevelGroup);
    String managersGroup = String.format("%s/managers", topLevelGroup);
    String adminsGroup = String.format("%s/admins", topLevelGroup);

    Map<String, Map<String, Set<String>>> expected = new HashMap<String, Map<String, Set<String>>>() {
      {
        put(usersGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId, new HashSet<String>(Arrays.asList("resource_user")));
          }
        });
        put(powerUsersGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId, new HashSet<String>(Arrays.asList("resource_user", "resource_power_user")));
          }
        });
        put(managersGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId,
                new HashSet<String>(Arrays.asList("resource_user", "resource_power_user", "resource_manager")));
          }
        });
        put(adminsGroup, new HashMap<String, Set<String>>() {
          {
            put(clientId, new HashSet<String>(
                Arrays.asList("resource_user", "resource_power_user", "resource_manager", "resource_admin")));
          }
        });
      }
    };
    assertThat(groupRoleMap, equalTo(expected));

    // Verify client scope is created
    assertThat(scopeName, notNullValue());
    assertThat(scopeName, equalTo("scope_" + clientId));

    // Verify the client scope exists in the realm
    Optional<org.keycloak.representations.idm.ClientScopeRepresentation> clientScope = realm.clientScopes().findAll()
        .stream()
        .filter(scope -> scope.getName().equals(scopeName))
        .findFirst();
    assertThat(clientScope.isPresent(), is(true));
    assertThat(clientScope.get().getDescription(), equalTo("Access to Test Resource APIs"));
    assertThat(clientScope.get().getProtocol(), equalTo("openid-connect"));
    assertThat(clientScope.get().getAttributes().get("display.on.consent.screen"), equalTo("true"));
    assertThat(clientScope.get().getAttributes().get("include.in.token.scope"), equalTo("true"));

    // Verify the client scope has the audience mapper
    List<org.keycloak.representations.idm.ProtocolMapperRepresentation> mappers = realm.clientScopes()
        .get(clientScope.get().getId())
        .getProtocolMappers()
        .getMappers();
    Optional<org.keycloak.representations.idm.ProtocolMapperRepresentation> audienceMapper = mappers.stream()
        .filter(mapper -> mapper.getName().equals("resource-audience-mapper"))
        .findFirst();
    assertThat(audienceMapper.isPresent(), is(true));
    assertThat(audienceMapper.get().getProtocol(), equalTo("openid-connect"));
    assertThat(audienceMapper.get().getProtocolMapper(), equalTo("oidc-audience-mapper"));
    assertThat(audienceMapper.get().getConfig().get("included.client.audience"), equalTo(clientId));
    assertThat(audienceMapper.get().getConfig().get("access.token.claim"), equalTo("true"));
    assertThat(audienceMapper.get().getConfig().get("id.token.claim"), equalTo("false"));

    // Verify token generation works
    String resourceToken = getTokenForClient(new ClientPair(clientId, clientSecret));
    assertThat(resourceToken, notNullValue());
  }

  @Test
  public void testResourceClientMetadataStoredCorrectly() {
    JsonPath jsonPath = registerClient();
    String clientId = jsonPath.getString("client_id");
    String scopeName = jsonPath.getString("scope");

    // Verify client metadata attributes are stored
    ClientRepresentation fetchedClient = realm.clients().findByClientId(clientId).get(0);
    Map<String, String> attributes = fetchedClient.getAttributes();

    // Verify top-level group metadata
    assertThat("Top level group name should be stored",
        attributes.get("bodhi.top_level_group.name"), equalTo("users-" + clientId));
    assertThat("Top level group ID should be stored",
        attributes.get("bodhi.top_level_group.id"), notNullValue());

    // Verify client scope metadata
    assertThat("Client scope name should be stored",
        attributes.get("bodhi.client_scope.name"), equalTo(scopeName));
    assertThat("Client scope ID should be stored",
        attributes.get("bodhi.client_scope.id"), notNullValue());

    // Verify the group actually exists with the stored ID
    String groupId = attributes.get("bodhi.top_level_group.id");
    GroupRepresentation group = realm.groups().group(groupId).toRepresentation();
    assertThat("Group should exist with stored ID", group, notNullValue());
    assertThat("Group name should match stored name",
        group.getName(), equalTo("users-" + clientId));

    // Verify the client scope actually exists with the stored ID
    String scopeId = attributes.get("bodhi.client_scope.id");
    org.keycloak.representations.idm.ClientScopeRepresentation clientScope = realm.clientScopes().get(scopeId)
        .toRepresentation();
    assertThat("Client scope should exist with stored ID", clientScope, notNullValue());
    assertThat("Client scope name should match stored name",
        clientScope.getName(), equalTo(scopeName));

    Optional<org.keycloak.representations.idm.ClientScopeRepresentation> defaultScope = realm
        .getDefaultDefaultClientScopes().stream().filter(scope -> scope.getName().equals(scopeName)).findFirst();
    assertThat(defaultScope.isPresent(), is(false));

    Optional<org.keycloak.representations.idm.ClientScopeRepresentation> defaultOptionalScope = realm
        .getDefaultOptionalClientScopes().stream().filter(scope -> scope.getName().equals(scopeName)).findFirst();
    assertThat(defaultOptionalScope.isPresent(), is(false));

    // Verify the resource client has its own scope as optional
    List<org.keycloak.representations.idm.ClientScopeRepresentation> optionalScopes = realm.clients()
        .get(fetchedClient.getId()).getOptionalClientScopes();
    boolean hasScopeAsOptional = optionalScopes.stream()
        .anyMatch(scope -> scope.getName().equals(scopeName));
    assertThat("Resource client should have its scope as optional",
        hasScopeAsOptional, is(true));
  }

  @Test
  public void testClientCreationWithTestPrefix() {
    // Test that client creation works with test prefix parameter when
    // live_test=true
    JsonPath jsonPath = given()
        .contentType(ContentType.JSON)
        .body(
            "{\"redirect_uris\": [\"http://bodhiapp.localhost/app/callback\"], \"name\": \"Test Resource\", \"description\": \"A test resource\"}")
        .when()
        .post(String.format("%s?live_test=true", bodhiProviderClient.newResourceUrl))
        .then()
        .body("error_description", nullValue())
        .body("error", nullValue())
        .statusCode(201)
        .body("client_id", notNullValue())
        .body("client_secret", notNullValue())
        .extract()
        .jsonPath();

    String clientId = jsonPath.getString("client_id");
    assertNotNull(clientId, "Client ID should not be null");
    assertTrue(clientId.startsWith("test-resource-"), "Client ID should have test prefix when live_test=true");
  }

  @Test
  public void testRegisterAppCreatesClientWithCorrectConfiguration() {
    ClientPair devConsoleClient = new ClientPair(BodhiResourceProvider.CLIENT_BODHI_DEV_CONSOLE, "change-me");
    TestUser devConsoleUser = createUser();
    String userAccessToken = getUserTokenWith(devConsoleClient, devConsoleUser.email, devConsoleUser.password);

    BodhiResourceProvider.ClientRequest appRequest = new BodhiResourceProvider.ClientRequest("Test App",
        "A test application",
        Arrays.asList("http://testapp.localhost/callback"));

    String appClientId = bodhiProviderClient.registerApp(userAccessToken, appRequest);

    // Verify client configuration
    ClientRepresentation fetchedClient = realm.clients().findByClientId(appClientId).get(0);
    assertThat(fetchedClient.isEnabled(), is(true));
    assertThat(fetchedClient.getName(), equalTo(appRequest.name));
    assertThat(fetchedClient.getDescription(), equalTo(appRequest.description));
    assertThat(fetchedClient.getRedirectUris(), containsInAnyOrder("http://testapp.localhost/callback"));
    assertThat(fetchedClient.getWebOrigins(), containsInAnyOrder("+"));
    assertThat(fetchedClient.isStandardFlowEnabled(), is(true));
    assertThat(fetchedClient.isDirectAccessGrantsEnabled(), is(true));
    assertThat(fetchedClient.isServiceAccountsEnabled(), is(false)); // No service account for app clients
    assertThat(fetchedClient.isPublicClient(), is(true)); // Public client, no client secret
    assertThat(fetchedClient.isFullScopeAllowed(), is(false));
    assertThat(fetchedClient.getProtocol(), equalTo("openid-connect"));
    assertThat(fetchedClient.isConsentRequired(), is(false));
    assertThat(fetchedClient.getAttributes().get("consent.screen.text"),
        equalTo("Application will obtain your information as detailed"));
    assertThat(fetchedClient.getAttributes().get("display.on.consent.screen"), is("true"));
    assertThat(fetchedClient.getAttributes().get("gui.order"), equalTo("1"));

    // Verify client-level admin role is created for app clients
    List<RoleRepresentation> clientRoles = realm.clients().get(fetchedClient.getId()).roles().list();
    Optional<RoleRepresentation> adminRole = clientRoles.stream()
        .filter(role -> role.getName().equals("admin")).findFirst();
    assertThat(adminRole.isPresent(), is(true));

    // Verify group structure is created for app clients
    Optional<GroupRepresentation> group = realm.groups().groups().stream()
        .filter(g -> g.getPath().equals(String.format("/users-%s", appClientId))).findFirst();
    assertThat(group.isPresent(), is(true));
    assertThat(group.get().getSubGroupCount(), is(1l));

    // Verify admins subgroup exists and has the admin role
    GroupResource topLevelGroupResource = realm.groups().group(group.get().getId());
    List<GroupRepresentation> subGroups = topLevelGroupResource.getSubGroups(0, -1, false);
    Optional<GroupRepresentation> adminsGroup = subGroups.stream()
        .filter(g -> g.getName().equals("admins")).findFirst();
    assertThat(adminsGroup.isPresent(), is(true));
    assertThat(adminsGroup.get().getPath(), equalTo(String.format("/users-%s/admins", appClientId)));

    // Verify the admins group has the admin client role
    Map<String, List<String>> clientRolesMap = adminsGroup.get().getClientRoles();
    assertThat(clientRolesMap.containsKey(appClientId), is(true));
    assertThat(clientRolesMap.get(appClientId), containsInAnyOrder("admin"));

    // Verify the requesting user is added to the admins group
    UserRepresentation user = realm.users().search(devConsoleUser.email).get(0);
    UserResource userResource = realm.users().get(user.getId());
    List<GroupRepresentation> userGroups = userResource.groups(null, null);
    Optional<GroupRepresentation> userAdminGroup = userGroups.stream()
        .filter(g -> g.getPath().equals(String.format("/users-%s/admins", appClientId))).findFirst();
    assertThat(userAdminGroup.isPresent(), is(true));

    // Verify the user has the admin role on the app client
    RoleScopeResource clientRoleScope = userResource.roles().clientLevel(fetchedClient.getId());
    List<RoleRepresentation> userClientRoles = clientRoleScope.listEffective();
    Optional<RoleRepresentation> userAdminRole = userClientRoles.stream()
        .filter(role -> role.getName().equals("admin")).findFirst();
    assertThat(userAdminRole.isPresent(), is(true));

    // Verify client ID has correct prefix (test-app- in test environment)
    assertThat(appClientId, startsWith("test-app-"));
  }

  @Test
  public void testAppClientMetadataStoredCorrectly() {
    ClientPair devConsoleClient = new ClientPair(BodhiResourceProvider.CLIENT_BODHI_DEV_CONSOLE, "change-me");
    TestUser devConsoleUser = createUser();
    String userAccessToken = getUserTokenWith(devConsoleClient, devConsoleUser.email, devConsoleUser.password);

    BodhiResourceProvider.ClientRequest appRequest = new BodhiResourceProvider.ClientRequest("Test App",
        "A test application",
        Arrays.asList("http://testapp.localhost/callback"));

    String appClientId = bodhiProviderClient.registerApp(userAccessToken, appRequest);

    // Verify client metadata attributes are stored
    ClientRepresentation fetchedClient = realm.clients().findByClientId(appClientId).get(0);
    Map<String, String> attributes = fetchedClient.getAttributes();

    // Verify top-level group metadata
    assertThat("Top level group name should be stored",
        attributes.get("bodhi.top_level_group.name"), equalTo("users-" + appClientId));
    assertThat("Top level group ID should be stored",
        attributes.get("bodhi.top_level_group.id"), notNullValue());

    // Verify the group actually exists with the stored ID
    String groupId = attributes.get("bodhi.top_level_group.id");
    GroupRepresentation group = realm.groups().group(groupId).toRepresentation();
    assertThat("Group should exist with stored ID", group, notNullValue());
    assertThat("Group name should match stored name",
        group.getName(), equalTo("users-" + appClientId));

    // App clients should not have client scope metadata (only resource clients do)
    assertThat("App client should not have client scope name metadata",
        attributes.get("bodhi.client_scope.name"), nullValue());
    assertThat("App client should not have client scope ID metadata",
        attributes.get("bodhi.client_scope.id"), nullValue());
  }

  @Test
  public void testRegisterAppFailsWithoutAuthentication() {
    BodhiResourceProvider.ClientRequest appRequest = new BodhiResourceProvider.ClientRequest("Test App",
        "A test application",
        Arrays.asList("http://testapp.localhost/callback"));

    Response response = bodhiProviderClient.registerAppResponse(null, appRequest);
    response.then()
        .statusCode(401)
        .body("error", equalTo("invalid session"));
  }

  @Test
  public void testRegisterAppFailsWithServiceAccountToken() {
    ClientPair resourceClient = registerClientAndReturnClientPair();
    String serviceAccountToken = getTokenForClient(resourceClient);

    BodhiResourceProvider.ClientRequest appRequest = new BodhiResourceProvider.ClientRequest("Test App",
        "A test application",
        Arrays.asList("http://testapp.localhost/callback"));

    Response response = bodhiProviderClient.registerAppResponse(serviceAccountToken, appRequest);
    response.then()
        .statusCode(401)
        .body("error", equalTo("service account tokens not allowed"));
  }

  @Test
  public void testRegisterAppFailsWithWrongClientToken() {
    ClientPair wrongClient = createPublicClientForUser();
    TestUser user = createUser();
    String userAccessToken = getUserTokenWith(wrongClient, user.email, user.password);

    BodhiResourceProvider.ClientRequest appRequest = new BodhiResourceProvider.ClientRequest("Test App",
        "A test application",
        Arrays.asList("http://testapp.localhost/callback"));

    Response response = bodhiProviderClient.registerAppResponse(userAccessToken, appRequest);
    response.then()
        .statusCode(401)
        .body("error", equalTo("unauthorized client"));
  }

  @Test
  public void testRegisterAppFailsWithInvalidToken() {
    String invalidToken = "invalid.jwt.token";
    BodhiResourceProvider.ClientRequest appRequest = new BodhiResourceProvider.ClientRequest("Test App",
        "A test application",
        Arrays.asList("http://testapp.localhost/callback"));

    Response response = bodhiProviderClient.registerAppResponse(invalidToken, appRequest);
    response.then()
        .statusCode(401)
        .body("error", equalTo("invalid session"));
  }

  @Test
  public void testRegisterAppWithMultipleRedirectUris() {
    ClientPair devConsoleClient = new ClientPair(BodhiResourceProvider.CLIENT_BODHI_DEV_CONSOLE, "change-me");
    TestUser devConsoleUser = createUser();
    String userAccessToken = getUserTokenWith(devConsoleClient, devConsoleUser.email, devConsoleUser.password);

    BodhiResourceProvider.ClientRequest appRequest = new BodhiResourceProvider.ClientRequest("Multi-Redirect App",
        "An app with multiple redirect URIs", Arrays.asList(
            "http://testapp.localhost/callback",
            "http://testapp.localhost/auth/callback",
            "http://localhost:3000/callback"));

    String appClientId = bodhiProviderClient.registerApp(userAccessToken, appRequest);

    // Verify all redirect URIs are configured
    ClientRepresentation fetchedClient = realm.clients().findByClientId(appClientId).get(0);
    assertThat(fetchedClient.getRedirectUris(), containsInAnyOrder(appRequest.redirectUris.toArray()));
  }

  // ========================================
  // REQUEST ACCESS TESTS
  // ========================================

  @Test
  public void testResourceRequestAccess() {
    // Create a resource client
    ClientPair resourceClient = registerClientAndReturnClientPair();
    String resourceServiceToken = getTokenForClient(resourceClient);

    // Create an app client
    ClientPair devConsoleClient = new ClientPair(BodhiResourceProvider.CLIENT_BODHI_DEV_CONSOLE, "change-me");
    TestUser devConsoleUser = createUser();
    String userAccessToken = getUserTokenWith(devConsoleClient, devConsoleUser.email, devConsoleUser.password);

    BodhiResourceProvider.ClientRequest appRequest = new BodhiResourceProvider.ClientRequest("Test App",
        "A test application",
        Arrays.asList("http://testapp.localhost/callback"));

    String appClientId = bodhiProviderClient.registerApp(userAccessToken, appRequest);

    // Request access from resource client to app client
    String resourceScope = requestAudienceAccess(resourceServiceToken, appClientId);

    // Verify the scope name is correct
    assertThat(resourceScope, equalTo("scope_" + resourceClient.clientId));

    // Verify the app client now has the resource scope as an optional scope
    org.keycloak.representations.idm.ClientRepresentation appClient = realm.clients().findByClientId(appClientId)
        .get(0);
    List<org.keycloak.representations.idm.ClientScopeRepresentation> optionalScopes = realm.clients()
        .get(appClient.getId())
        .getOptionalClientScopes();
    boolean hasScopeAssigned = optionalScopes.stream()
        .anyMatch(scope -> scope.getName().equals(resourceScope));
    assertThat(hasScopeAssigned, is(true));
  }

  @Test
  public void testRequestAccessReturnsOkIfScopeAlreadyAdded() {
    // Create an app client
    ClientPair devConsoleClient = new ClientPair(BodhiResourceProvider.CLIENT_BODHI_DEV_CONSOLE, "change-me");
    TestUser devConsoleUser = createUser();
    String userAccessToken = getUserTokenWith(devConsoleClient, devConsoleUser.email, devConsoleUser.password);

    BodhiResourceProvider.ClientRequest appRequest = new BodhiResourceProvider.ClientRequest("Test App",
        "A test application",
        Arrays.asList("http://testapp.localhost/callback"));

    String appClientId = bodhiProviderClient.registerApp(userAccessToken, appRequest);

    // Create a resource client
    ClientPair resourceClient = registerClientAndReturnClientPair();
    String resourceServiceToken = getTokenForClient(resourceClient);

    // Request access first time
    bodhiProviderClient.requestAccessResponse(resourceServiceToken, appClientId)
        .then()
        .statusCode(201)
        .body("scope", equalTo("scope_" + resourceClient.clientId));

    // Request access second time - should return OK
    requestAudienceAccessResponse(resourceServiceToken, appClientId)
        .then()
        .statusCode(200)
        .body("scope", equalTo("scope_" + resourceClient.clientId));
  }

  @Test
  public void testRequestAccessFailsWithoutAuthentication() {
    String appClientId = "some-app-client";

    requestAudienceAccessResponse(null, appClientId)
        .then()
        .statusCode(401)
        .body("error", equalTo("invalid session"));
  }

  @Test
  public void testRequestAccessFailsWithInvalidToken() {
    String invalidToken = "invalid.jwt.token";
    String appClientId = "some-app-client";

    requestAudienceAccessResponse(invalidToken, appClientId)
        .then()
        .statusCode(401)
        .body("error", equalTo("invalid session"));
  }

  @Test
  public void testRequestAccessFailsWithUserToken() {
    // Create a resource client
    ClientPair resourceClient = registerClientAndReturnClientPair();
    TestUser adminUser = createUser();
    String clientToken = getTokenForClient(resourceClient);
    makeResourceAdmin(clientToken, adminUser.email);

    // Get user token instead of service account token
    String userToken = getUserTokenWith(resourceClient, adminUser.email, adminUser.password);
    String appClientId = "some-app-client";

    requestAudienceAccessResponse(userToken, appClientId)
        .then()
        .statusCode(401)
        .body("error", equalTo("not a service account token"));
  }

  @Test
  public void testRequestAccessFailsWithoutTokenExchangeEnabled() {
    // Create a resource client without token exchange enabled
    ClientPair resourceClientWithoutExchange = createClientWithoutTokenExchange();
    String resourceServiceToken = getTokenForClient(resourceClientWithoutExchange);

    String appClientId = "some-app-client";

    requestAudienceAccessResponse(resourceServiceToken, appClientId)
        .then()
        .statusCode(400)
        .body("error", equalTo("Resource client does not have token exchange enabled"));
  }

  @Test
  public void testRequestAccessFailsWithNonExistentAppClient() {
    ClientPair resourceClient = registerClientAndReturnClientPair();
    String resourceServiceToken = getTokenForClient(resourceClient);

    String nonExistentAppClientId = "non-existent-app-client";

    requestAudienceAccessResponse(resourceServiceToken, nonExistentAppClientId)
        .then()
        .statusCode(400)
        .body("error", equalTo("App client not found"));
  }

  @Test
  public void testRequestAccessFailsWithConfidentialAppClient() {
    ClientPair resourceClient = registerClientAndReturnClientPair();
    String resourceServiceToken = getTokenForClient(resourceClient);

    // Create a confidential client (not a public app client)
    ClientPair confidentialClient = registerClientAndReturnClientPair();

    requestAudienceAccessResponse(resourceServiceToken, confidentialClient.clientId)
        .then()
        .statusCode(400)
        .body("error", equalTo("Only public app clients can request access"));
  }

  @Test
  public void testRequestAccessFailsWithMissingResourceScope() {
    // Create a resource client manually without the scope
    ClientPair resourceClientWithoutScope = createClientWithoutScope();
    String resourceServiceToken = getTokenForClient(resourceClientWithoutScope);

    // Create an app client
    ClientPair devConsoleClient = new ClientPair(BodhiResourceProvider.CLIENT_BODHI_DEV_CONSOLE, "change-me");
    TestUser devConsoleUser = createUser();
    String userAccessToken = getUserTokenWith(devConsoleClient, devConsoleUser.email, devConsoleUser.password);

    BodhiResourceProvider.ClientRequest appRequest = new BodhiResourceProvider.ClientRequest("Test App",
        "A test application",
        Arrays.asList("http://testapp.localhost/callback"));

    String appClientId = bodhiProviderClient.registerApp(userAccessToken, appRequest);

    requestAudienceAccessResponse(resourceServiceToken, appClientId)
        .then()
        .statusCode(400)
        .body("error", equalTo("Resource client scope metadata not found. Client may not be properly configured."));
  }

  // ========================================
  // CLIENT PERMISSION TESTS
  // ========================================

  @Test
  public void testResourceClientCannotListOtherClients() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    String resourceToken = getTokenForClient(clientPair);

    given()
        .header("Authorization", "Bearer " + resourceToken)
        .when()
        .get(String.format("%s/admin/realms/%s/clients", keycloak.getAuthServerUrl(), REALM))
        .then()
        .statusCode(403);
  }

  @Test
  public void testResourceClientCannotViewItself() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    String resourceToken = getTokenForClient(clientPair);

    given()
        .header("Authorization", "Bearer " + resourceToken)
        .when()
        .get(String.format("%s/admin/realms/bodhi/clients/%s", keycloak.getAuthServerUrl(), clientPair.clientId))
        .then()
        .statusCode(403);
  }

  @Test
  public void testResourceClientCannotUpdateItself() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    String resourceToken = getTokenForClient(clientPair);

    given()
        .header("Authorization", "Bearer " + resourceToken)
        .contentType(ContentType.JSON)
        .body("{\"name\": \"Updated Name\"}")
        .when()
        .put(String.format("%s/admin/realms/bodhi/clients/%s", keycloak.getAuthServerUrl(), clientPair.clientId))
        .then()
        .statusCode(403);
  }

  @Test
  public void testResourceClientCannotConfigureScopes() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    String resourceToken = getTokenForClient(clientPair);
    String scopeId = realm.clientScopes().findAll().stream().filter(scope -> scope.getName().equals("phone"))
        .findFirst().get().getId();

    given()
        .header("Authorization", "Bearer " + resourceToken)
        .when()
        .put(String.format("%s/admin/realms/bodhi/clients/%s/default-client-scopes/%s", keycloak.getAuthServerUrl(),
            clientPair.clientId, scopeId))
        .then()
        .statusCode(403);
  }

  // ========================================
  // ADMIN MANAGEMENT TESTS
  // ========================================

  @Test
  public void testMakeResourceAdminAssignsAllRoles() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    TestUser adminUser = createUser();

    String clientToken = getTokenForClient(clientPair);
    makeResourceAdmin(clientToken, adminUser.email)
        .then()
        .statusCode(201);

    TokenPair userTokenPair = getUserTokenPairWith(clientPair, adminUser.email, adminUser.password,
        Arrays.asList("openid", "email", "profile", "roles"));
    DecodedJWT jwt = JWT.decode(userTokenPair.access);
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split("\\s+"));
    assertEquals(new HashSet<>(Arrays.asList("openid", "email", "profile", "roles")), new HashSet<>(claimScopes));
    assertEquals("RS256", jwt.getAlgorithm());
    String keyId = jwt.getKeyId();

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> resourceRoles = (List) ((Map) jwt.getClaim("resource_access").asMap().get(clientPair.clientId))
        .get("roles");
    List<String> allRoles = Arrays.asList(RESOURCE_USER, RESOURCE_POWER_USER, RESOURCE_MANAGER, RESOURCE_ADMIN);
    assertThat(allRoles, containsInAnyOrder(resourceRoles.toArray()));

    // Test refresh token maintains roles
    TokenPair newTokenPair = refreshTokenFlow(clientPair, userTokenPair.refresh);
    DecodedJWT newJwt = JWT.decode(newTokenPair.access);
    String newClaimScope = newJwt.getClaim("scope").asString();
    List<String> newClaimScopes = Arrays.asList(newClaimScope.split(" "));
    assertEquals(new HashSet<>(Arrays.asList("openid", "email", "profile", "roles")), new HashSet<>(newClaimScopes));
    assertEquals(keyId, newJwt.getKeyId());
    assertEquals("RS256", newJwt.getAlgorithm());

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> newResourceRoles = (List) ((Map) newJwt.getClaim("resource_access").asMap().get(clientPair.clientId))
        .get("roles");
    assertThat(allRoles, containsInAnyOrder(newResourceRoles.toArray()));
  }

  // ========================================
  // USER GROUP MANAGEMENT TESTS
  // ========================================

  @Test
  public void testAddUserToGroupGrantsUserRole() {
    ClientPair clientPair = registerClientAndReturnClientPair();
    TestUser adminUser = createUser();
    TestUser regularUser = createUser();

    String clientToken = getTokenForClient(clientPair);
    makeResourceAdmin(clientToken, adminUser.email)
        .then()
        .statusCode(201);

    String adminAccessToken = getUserTokenWith(clientPair, adminUser.email, adminUser.password);
    addUserToGroup(addToGroupUrl, adminAccessToken, regularUser.email, "users").then()
        .body("error", nullValue())
        .statusCode(201);

    List<String> scopes = Arrays.asList("openid", "email", "profile", "roles");
    TokenPair userTokenPair = getUserTokenPairWith(clientPair, regularUser.email, regularUser.password, scopes);
    DecodedJWT jwt = JWT.decode(userTokenPair.access);
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split(" "));
    assertEquals(new HashSet<>(scopes), new HashSet<>(claimScopes));

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> resourceRoles = (List) ((Map) jwt.getClaim("resource_access").asMap().get(clientPair.clientId))
        .get("roles");
    List<String> userRoles = Arrays.asList(RESOURCE_USER);
    assertThat(userRoles, containsInAnyOrder(resourceRoles.toArray()));
    String keyId = jwt.getKeyId();
    assertEquals("RS256", jwt.getAlgorithm());

    // Test refresh token maintains roles
    TokenPair newTokenPair = refreshTokenFlow(clientPair, userTokenPair.refresh);
    DecodedJWT newJwt = JWT.decode(newTokenPair.access);
    String newClaimScope = newJwt.getClaim("scope").asString();
    List<String> newClaimScopes = Arrays.asList(newClaimScope.split(" "));
    assertEquals(new HashSet<>(scopes), new HashSet<>(newClaimScopes));
    assertEquals(keyId, newJwt.getKeyId());
    assertEquals("RS256", newJwt.getAlgorithm());

    @SuppressWarnings({ "rawtypes", "unchecked" })
    List<String> newResourceRoles = (List) ((Map) newJwt.getClaim("resource_access").asMap().get(clientPair.clientId))
        .get("roles");
    assertThat(userRoles, containsInAnyOrder(newResourceRoles.toArray()));
  }

  // ========================================
  // EXISTING TESTS (refactored without try-finally)
  // ========================================

  @Test
  public void testMakeFirstAdmin() {
    ClientPair makeFirstAdminClient = registerClientAndReturnClientPair();
    TestUser makeFirstAdminUser = createUser();

    String token = getTokenForClient(makeFirstAdminClient);
    makeResourceAdmin(token, makeFirstAdminUser.email)
        .then()
        .statusCode(201);

    ClientRepresentation client = realm.clients().findByClientId(makeFirstAdminClient.clientId).get(0);
    UserRepresentation user = realm.users().search(makeFirstAdminUser.email).get(0);
    assertNotNull(user);
    UserResource userResource = realm.users().get(user.getId());
    RoleScopeResource clientRoles = userResource.roles().clientLevel(client.getId());
    RoleRepresentation adminRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(RESOURCE_ADMIN)).collect(Collectors.toList()).get(0);
    assertNotNull(adminRole);
    RoleRepresentation managerRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(RESOURCE_MANAGER)).collect(Collectors.toList()).get(0);
    assertNotNull(managerRole);
    RoleRepresentation userRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(RESOURCE_USER)).collect(Collectors.toList()).get(0);
    assertNotNull(userRole);
    List<GroupRepresentation> userGroups = userResource.groups(null, null).stream().collect(Collectors.toList());
    Optional<GroupRepresentation> adminGroup = userGroups.stream().filter(g -> g.getName().equals("admins"))
        .findFirst();
    assertTrue(adminGroup.isPresent());
    String adminGroupPath = String.format("/users-%s/admins", makeFirstAdminClient.clientId);
    assertEquals(adminGroupPath, adminGroup.get().getPath());
    assertEquals(1, userGroups.size());

    // Verify the admin user is a member of the admin group
    GroupResource adminGroupResource = realm.groups().group(adminGroup.get().getId());
    List<UserRepresentation> adminGroupMembers = adminGroupResource.members();
    Optional<UserRepresentation> adminUserInGroup = adminGroupMembers.stream()
        .filter(u -> u.getEmail().equals(makeFirstAdminUser.email))
        .findFirst();
    assertThat("Admin user should be a member of the admin group", adminUserInGroup.isPresent(), is(true));
  }

  @Test
  public void testMakeFirstAdminUnauthorized() {
    TestUser someUser = createUser();
    given()
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", someUser.email))
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("invalid session"))
        .statusCode(401);
  }

  @Test
  public void testMakeFirstAdminErrorIfNotServiceToken() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getUserTokenWith(mainResourceClient, mainAdminUser.email, mainAdminUser.password);
    TestUser testUser = createUser();
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", testUser.email))
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("not a service account token"))
        .statusCode(401);
  }

  @Test
  public void testMakeFirstAdminInvalidToken() {
    ClientPair emptyResourceClient = registerClientAndReturnClientPair();
    String token = getTokenForClient(emptyResourceClient) + "foobar";
    TestUser testUser = createUser();
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", testUser.email))
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("invalid session"))
        .statusCode(401);
  }

  @Test
  public void testMakeFirstAdminFailsIfAlreadyHaveAdmin() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getTokenForClient(mainResourceClient);
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", mainAdminUser.email))
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("resource already has a admin user"))
        .statusCode(400);
  }

  @Test
  public void testMakeFirstAdminFailsIfTopLevelGroupNotConfigured() {
    ClientPair missingGroupClient = createClientWithoutGroups();
    TestUser mainAdminUser = createUser();

    String token = getTokenForClient(missingGroupClient);
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\"}", mainAdminUser.email))
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("resource group not configured for client"))
        .statusCode(500);
  }

  @Test
  public void testMakeFirstAdminUserNotFound() {
    ClientPair emptyResourceClient = registerClientAndReturnClientPair();

    String token = getTokenForClient(emptyResourceClient);
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body("{\"username\": \"<EMAIL>\"}")
        .when()
        .post(resourceAdminUrl)
        .then()
        .body("error", equalTo("user not found"))
        .statusCode(400);
  }

  @Test
  public void testAddUserToGroupByResourceAdmin() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    TestUser testApproveUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getUserTokenWith(mainResourceClient, mainAdminUser.email, mainAdminUser.password);
    addUserToGroup(addToGroupUrl, token, testApproveUser.email, "users")
        .then()
        .body("message", equalTo("added to group"))
        .statusCode(201);

    ClientRepresentation client = realm.clients().findByClientId(mainResourceClient.clientId).get(0);
    UserRepresentation user = realm.users().search(testApproveUser.email).get(0);
    RoleScopeResource clientRoles = realm.users().get(user.getId()).roles().clientLevel(client.getId());
    RoleRepresentation clientRole = clientRoles.listEffective().stream()
        .filter(i -> i.getName().equals(RESOURCE_USER)).collect(Collectors.toList()).get(0);
    assertNotNull(clientRole);
    UserResource userResource = realm.users().get(user.getId());
    List<GroupRepresentation> userGroups = userResource.groups(null, null).stream().collect(Collectors.toList());
    assertEquals(String.format("/users-%s/users", mainResourceClient.clientId), userGroups.get(0).getPath());
    assertEquals(1, userGroups.size());
  }

  @Test
  public void testAddUserToGroupAlreadyMember() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getUserTokenWith(mainResourceClient, mainAdminUser.email, mainAdminUser.password);
    given()
        .header("Authorization", "Bearer " + token)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"admins\", \"add\": true}", mainAdminUser.email))
        .when()
        .post(addToGroupUrl)
        .then()
        .body("message", equalTo("user already member of group"))
        .statusCode(200);
  }

  @Test
  public void testAddUserToGroupUnauthorized() {
    TestUser testUser = createUser();
    given()
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"users\", \"add\": true}", testUser.email))
        .when()
        .post(addToGroupUrl)
        .then()
        .body("error", equalTo("invalid session"))
        .statusCode(401);
  }

  @Test
  public void testAddUserToGroupByOtherClientAdmin() {
    ClientPair otherResourceClient = registerClientAndReturnClientPair();
    TestUser otherAdminUser = createUser();
    TestUser userToAddByOtherAdmin = createUser();
    setupAdminUser(otherResourceClient, otherAdminUser);

    TestUser otherUser = createUser();
    TokenPair otherAdminToken = getUserTokenWith(otherResourceClient, otherAdminUser.email, otherAdminUser.password,
        Arrays.asList("openid", "email", "profile", "roles"));
    addUserToGroup(addToGroupUrl, otherAdminToken.access, otherUser.email, "users")
        .then()
        .body("error", nullValue())
        .statusCode(201);
    TokenPair otherUserToken = getUserTokenWith(otherResourceClient, otherUser.email, otherUser.password,
        Arrays.asList("openid", "email", "profile", "roles"));
    given()
        .header("Authorization", "Bearer " + otherUserToken.access)
        .contentType(ContentType.JSON)
        .body(String.format("{\"username\": \"%s\", \"group\": \"users\", \"add\": true}", userToAddByOtherAdmin.email))
        .when()
        .post(addToGroupUrl)
        .then()
        .body("error", equalTo("User does not have resource-admin role"))
        .statusCode(403);
  }

  @Test
  public void testHasAdminOnEmptyClient() {
    ClientPair emptyResourceClient = registerClientAndReturnClientPair();

    String token = getTokenForClient(emptyResourceClient);
    given()
        .header("Authorization", "Bearer " + token)
        .when()
        .get(hasAdminUrl)
        .then()
        .body("error", nullValue())
        .statusCode(200)
        .body("has_admin", equalTo(false));
  }

  @Test
  public void testHasAdminOnClientWithAdmin() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getTokenForClient(mainResourceClient);
    given()
        .header("Authorization", "Bearer " + token)
        .when()
        .get(hasAdminUrl)
        .then()
        .statusCode(200)
        .body("has_admin", equalTo(true));
  }

  @Test
  public void testHasAdminReturnsUnauthorizedIfCalledWithNonServiceToken() {
    ClientPair mainResourceClient = registerClientAndReturnClientPair();
    TestUser mainAdminUser = createUser();
    setupAdminUser(mainResourceClient, mainAdminUser);

    String token = getUserTokenWith(mainResourceClient, mainAdminUser.email, mainAdminUser.password);
    given()
        .header("Authorization", "Bearer " + token)
        .when()
        .get(hasAdminUrl)
        .then()
        .statusCode(401)
        .body("error", equalTo("not a service account token"));
  }

  // ========================================
  // HIERARCHICAL GROUP ROLE TESTS
  // ========================================

  @Test
  public void testUsersWithNoRolesHaveNoResourceAccess() {
    ClientPair testClient = registerClientAndReturnClientPair();
    TestUser userWithNoRoles = createUser();
    List<String> scopes = Arrays.asList("openid", "email", "profile", "roles");

    TokenPair tokenPair = getUserTokenWith(testClient, userWithNoRoles.email, userWithNoRoles.password, scopes);
    DecodedJWT jwt = JWT.decode(tokenPair.access);

    // Verify scope claim is correct
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split("\\s+"));
    assertEquals(new HashSet<>(scopes), new HashSet<>(claimScopes));

    // Verify no resource_access claim exists for users with no roles
    Claim resourceAccessClaim = jwt.getClaim("resource_access");
    assertTrue(resourceAccessClaim.isMissing(), "Users with no roles should not have resource_access claim");

    // Test refresh token flow maintains the same behavior
    TokenPair newTokenPair = refreshTokenFlow(testClient, tokenPair.refresh);
    DecodedJWT newJwt = JWT.decode(newTokenPair.access);
    Claim newResourceAccessClaim = newJwt.getClaim("resource_access");
    assertTrue(newResourceAccessClaim.isMissing(),
        "Users with no roles should not have resource_access claim after refresh");
  }

  @ParameterizedTest
  @MethodSource("userRoleProvider")
  @SuppressWarnings("unchecked")
  public void testUsersWithRolesHaveCorrectResourceAccess(String roleLevel, List<String> expectedRoles) {
    ClientPair testClient = registerClientAndReturnClientPair();
    TestUser userWithRole = createUserWithRole(testClient.clientId, roleLevel);
    List<String> scopes = Arrays.asList("openid", "email", "profile", "roles");

    TokenPair tokenPair = getUserTokenWith(testClient, userWithRole.email, userWithRole.password, scopes);
    DecodedJWT jwt = JWT.decode(tokenPair.access);

    // Verify scope claim is correct
    String claimScope = jwt.getClaim("scope").asString();
    List<String> claimScopes = Arrays.asList(claimScope.split("\\s+"));
    assertEquals(new HashSet<>(scopes), new HashSet<>(claimScopes));

    // Verify resource_access claim exists and has correct roles
    Claim resourceAccessClaim = jwt.getClaim("resource_access");
    assertTrue(!resourceAccessClaim.isMissing(), "Users with roles should have resource_access claim");

    Map<String, Claim> resourceAccess = (Map<String, Claim>) resourceAccessClaim
        .asMap().get(testClient.clientId);
    List<String> resourceRoles = (List<String>) resourceAccess.get("roles");
    assertEquals(expectedRoles.size(), resourceRoles.size(),
        "Should have exactly " + expectedRoles.size() + " roles for " + roleLevel);
    assertTrue(resourceRoles.containsAll(expectedRoles),
        "Should contain all expected roles: " + expectedRoles + " but got: " + resourceRoles);

    // Test refresh token flow maintains the same roles
    TokenPair newTokenPair = refreshTokenFlow(testClient, tokenPair.refresh);
    DecodedJWT newJwt = JWT.decode(newTokenPair.access);
    Claim newResourceAccessClaim = newJwt.getClaim("resource_access");
    Map<String, Claim> newResourceAccess = (Map<String, Claim>) newResourceAccessClaim
        .asMap().get(testClient.clientId);
    List<String> newResourceRoles = (List<String>) newResourceAccess.get("roles");
    assertEquals(expectedRoles.size(), newResourceRoles.size(),
        "Should maintain same number of roles after refresh");
    assertTrue(newResourceRoles.containsAll(expectedRoles),
        "Should maintain same roles after refresh");
  }

  private static Stream<Arguments> userRoleProvider() {
    return Stream.of(
        Arguments.of(RESOURCE_USER, Arrays.asList("resource_user")), // Basic user role
        Arguments.of(RESOURCE_POWER_USER, Arrays.asList("resource_user", "resource_power_user")),
        Arguments.of(RESOURCE_MANAGER, Arrays.asList("resource_user", "resource_power_user", "resource_manager")),
        Arguments.of(RESOURCE_ADMIN, Arrays.asList("resource_user", "resource_power_user", "resource_manager",
            "resource_admin")));
  }

  // ========================================
  // HELPER METHODS
  // ========================================

  private RealmResource realm = keycloak.getKeycloakAdminClient().realm(REALM);

  private ClientPair createClientWithoutGroups() {
    long timestamp = System.currentTimeMillis();
    String clientId = "test-missing-groups-" + timestamp;

    org.keycloak.representations.idm.ClientRepresentation client = new org.keycloak.representations.idm.ClientRepresentation();
    client.setClientId(clientId);
    client.setEnabled(true);
    client.setClientAuthenticatorType("client-secret");
    client.setSecret("change-me");
    client.setRedirectUris(Arrays.asList("http://localhost/callback"));
    client.setWebOrigins(Arrays.asList("+"));
    client.setBearerOnly(false);
    client.setConsentRequired(false);
    client.setStandardFlowEnabled(true);
    client.setImplicitFlowEnabled(false);
    client.setDirectAccessGrantsEnabled(true);
    client.setServiceAccountsEnabled(true);
    client.setPublicClient(false);
    client.setProtocol("openid-connect");
    client.setFullScopeAllowed(false);

    // Create the client without groups (this is the key difference)
    jakarta.ws.rs.core.Response response = realm.clients().create(client);
    assertThat("Failed to create client without groups: " + response.getStatus() + " " + response.getStatusInfo(),
        response.getStatus(), is(201));

    return new ClientPair(clientId, "change-me");
  }

  private void setupAdminUser(ClientPair clientPair, TestUser user) {
    String serviceAccountToken = getTokenForClient(clientPair);
    makeResourceAdmin(serviceAccountToken, user.email)
        .then()
        .statusCode(201);
  }

  private ClientPair createClientWithoutTokenExchange() {
    long timestamp = System.currentTimeMillis();
    String clientId = "test-no-exchange-" + timestamp;

    org.keycloak.representations.idm.ClientRepresentation client = new org.keycloak.representations.idm.ClientRepresentation();
    client.setClientId(clientId);
    client.setEnabled(true);
    client.setClientAuthenticatorType("client-secret");
    client.setSecret("change-me");
    client.setRedirectUris(Arrays.asList("http://localhost/callback"));
    client.setWebOrigins(Arrays.asList("+"));
    client.setBearerOnly(false);
    client.setConsentRequired(false);
    client.setStandardFlowEnabled(true);
    client.setImplicitFlowEnabled(false);
    client.setDirectAccessGrantsEnabled(true);
    client.setServiceAccountsEnabled(true);
    client.setPublicClient(false);
    client.setProtocol("openid-connect");
    client.setFullScopeAllowed(false);
    // NOTE: NOT setting standard.token.exchange.enabled attribute

    // Create the client
    jakarta.ws.rs.core.Response response = realm.clients().create(client);
    assertThat(
        "Failed to create client without token exchange: " + response.getStatus() + " " + response.getStatusInfo(),
        response.getStatus(), is(201));

    return new ClientPair(clientId, "change-me");
  }

  private ClientPair createClientWithoutScope() {
    long timestamp = System.currentTimeMillis();
    String clientId = "test-no-scope-" + timestamp;

    org.keycloak.representations.idm.ClientRepresentation client = new org.keycloak.representations.idm.ClientRepresentation();
    client.setClientId(clientId);
    client.setEnabled(true);
    client.setClientAuthenticatorType("client-secret");
    client.setSecret("change-me");
    client.setRedirectUris(Arrays.asList("http://localhost/callback"));
    client.setWebOrigins(Arrays.asList("+"));
    client.setBearerOnly(false);
    client.setConsentRequired(false);
    client.setStandardFlowEnabled(true);
    client.setImplicitFlowEnabled(false);
    client.setDirectAccessGrantsEnabled(true);
    client.setServiceAccountsEnabled(true);
    client.setPublicClient(false);
    client.setProtocol("openid-connect");
    client.setFullScopeAllowed(false);

    // Set token exchange enabled but don't create the scope
    Map<String, String> attributes = new HashMap<>();
    attributes.put("standard.token.exchange.enabled", "true");
    client.setAttributes(attributes);

    // Create the client
    jakarta.ws.rs.core.Response response = realm.clients().create(client);
    assertThat("Failed to create client without scope: " + response.getStatus() + " " + response.getStatusInfo(),
        response.getStatus(), is(201));

    return new ClientPair(clientId, "change-me");
  }
}