package com.bodhisearch;

import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.bodhisearch.templates.RealmConfigGenerator;

public class RealmSeedingTest extends BaseSeedingTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(RealmSeedingTest.class);

  private static final String GENERATED_JSON_PATH = "./src/test/resources/import-files/bodhi-realm-generated.json";

  @BeforeAll
  public static void setupRealmSeedingStatic() {
    RealmSeedingTest test = new RealmSeedingTest();
    test.setupRealmSeeding();
  }

  @Test
  public void testTemplateGenerationWorksCorrectly() {
    String generatedFile = getGeneratedJsonPath();
    java.io.File file = new java.io.File(generatedFile);
    assertTrue(file.exists(), "Generated file should exist");
    assertTrue(file.length() > 0, "Generated file should not be empty");

    LOGGER.info("Template generation test completed successfully for: {}", generatedFile);
  }

  @Override
  protected void generateRealmConfig() {
    // Generate using the standard realm setup template
    RealmConfigGenerator.generate(GENERATED_JSON_PATH);
  }

  @Override
  protected String getGeneratedJsonPath() {
    return GENERATED_JSON_PATH;
  }

  @Override
  protected void validateSpecificConfiguration() {
    // Validate specific aspects of the standard realm setup

    // Verify email verification is enabled (standard setup)
    assertTrue(realm.toRepresentation().isVerifyEmail(),
        "Standard setup should have email verification enabled");

    LOGGER.info("Standard realm setup validation completed successfully");
  }
}