package com.bodhisearch;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.List;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.admin.client.resource.RealmResource;
import org.keycloak.representations.idm.UserRepresentation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.testcontainers.junit.jupiter.Container;
import org.testcontainers.junit.jupiter.Testcontainers;

import dasniko.testcontainers.keycloak.KeycloakContainer;

@Testcontainers
public abstract class BaseSeedingTest {
  public static final Logger LOGGER = LoggerFactory.getLogger(BaseSeedingTest.class);

  public static final String REALM = "bodhi";
  public static final String PROVIDER_ID = "bodhi-resource";

  @Container
  public static final KeycloakContainer keycloak = new KeycloakContainer("quay.io/keycloak/keycloak:26.2.5")
      .withFeaturesEnabled("token-exchange")
      .withProviderClassesFrom("target/classes");

  protected static RealmResource realm;

  /**
   * Subclasses should implement this to generate their specific realm
   * configuration
   */
  protected abstract void generateRealmConfig();

  /**
   * Subclasses should implement this to return the path to their generated JSON
   * file
   */
  protected abstract String getGeneratedJsonPath();

  /**
   * Subclasses can override this to provide additional validation specific to
   * their configuration
   */
  protected void validateSpecificConfiguration() {
    // Default implementation does nothing
  }

  @BeforeAll
  public static void setupKeycloak() {
    // Keycloak container is started automatically by @Container annotation
  }

  protected void setupRealmSeeding() {
    // Generate the realm configuration
    generateRealmConfig();

    // Import the generated configuration
    importFile(keycloak.getAuthServerUrl(), keycloak.getAdminUsername(),
        keycloak.getAdminPassword(), getGeneratedJsonPath());

    // Setup admin client
    Keycloak adminClient = KeycloakBuilder.builder()
        .serverUrl(keycloak.getAuthServerUrl())
        .realm("master")
        .clientId("admin-cli")
        .username(keycloak.getAdminUsername())
        .password(keycloak.getAdminPassword())
        .build();
    realm = adminClient.realm(REALM);
  }

  @Test
  public void testTemplateGenerationAndCliImport() {
    assertNotNull(keycloak.getAuthServerUrl(), "Auth server URL should be available");
    assertTrue(keycloak.getAuthServerUrl().contains("localhost"), "Should be running on localhost");
  }

  @Test
  public void testCliImportValidation() {
    List<UserRepresentation> users = realm.users().list();
    assertNotNull(users, "Should be able to list users from seeded realm");
    assertTrue(realm.toRepresentation().isEnabled(), "Realm should be enabled");
    assertEquals(REALM, realm.toRepresentation().getRealm(), "Realm name should match");
  }

  @Test
  public void testSeedingGeneratedCorrectRealm() {
    assertNotNull(realm.toRepresentation(), "Should have realm representation");
    assertTrue(realm.toRepresentation().isEnabled(), "Realm should be enabled");
    assertEquals(REALM, realm.toRepresentation().getRealm(), "Realm name should be correct");

    assertTrue(realm.toRepresentation().isRegistrationEmailAsUsername(),
        "Should have email as username enabled");
    assertTrue(realm.toRepresentation().isRememberMe(), "Should have remember me enabled");
    assertTrue(realm.toRepresentation().isLoginWithEmailAllowed(),
        "Should allow login with email");

    validateSpecificConfiguration();
  }

  // ========================================
  // HELPER METHODS
  // ========================================

  public static void importFile(String keycloakUrl, String username, String password, String filename) {
    try {
      String[] command = { "java", "-jar",
          "tools/keycloak-config-cli-26.1.0.jar",
          "--import.files.locations=" + filename,
          "--keycloak.url=" + keycloakUrl,
          "--keycloak.user=" + username,
          "--keycloak.password=" + password,
          "--logging.level.keycloak-config-cli=debug"
      };

      Process process = Runtime.getRuntime().exec(command);
      BufferedReader stdoutReader = new BufferedReader(new InputStreamReader(process.getInputStream()));
      BufferedReader stderrReader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
      String line;

      while ((line = stdoutReader.readLine()) != null) {
        LOGGER.info(line);
      }

      while ((line = stderrReader.readLine()) != null) {
        LOGGER.error(line);
      }

      process.waitFor();
      int exitCode = process.exitValue();

      if (exitCode == 0) {
        LOGGER.info("CLI import executed successfully");
      } else {
        String errMsg = "CLI import failed with exit code: " + exitCode;
        LOGGER.error(errMsg);
        throw new RuntimeException(errMsg);
      }
    } catch (IOException | InterruptedException e) {
      LOGGER.error("Error during CLI import", e);
      throw new RuntimeException(e);
    }
  }
}