name: Server Release Builds

on:
  push:
    tags:
      - 'release/v*'
    branches:
      - 'release/v*'
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - 'ai-docs/**'
      - '.gitignore'
      - 'LICENSE'

  workflow_dispatch:
    inputs:
      tag:
        description: 'Release tag to build (e.g., release/v1.0.0)'
        required: true
        type: string

env:
  REGISTRY: ghcr.io

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.tag || github.ref }}

    - name: Extract version information
      id: version
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          REF_NAME="${{ github.event.inputs.tag }}"
        else
          REF_NAME="${{ github.ref_name }}"
        fi
        # Extract version from release/vX.Y.Z format
        if [[ "$REF_NAME" =~ ^release/v([0-9]+\.[0-9]+\.[0-9]+.*)$ ]]; then
          VERSION_TAG="${BASH_REMATCH[1]}"
          DOCKER_TAG="v$VERSION_TAG"
        else
          echo "Error: Invalid release tag format. Expected: release/vX.Y.Z"
          exit 1
        fi
        SHORT_SHA=$(echo "${{ github.sha }}" | cut -c1-7)
        echo "version_tag=$VERSION_TAG" >> $GITHUB_OUTPUT
        echo "docker_tag=$DOCKER_TAG" >> $GITHUB_OUTPUT
        echo "short_sha=$SHORT_SHA" >> $GITHUB_OUTPUT
        echo "ref_name=$REF_NAME" >> $GITHUB_OUTPUT

    - name: Set image name
      run: echo "IMAGE_NAME=$(echo '${{ github.repository_owner }}/bodhi-auth-server' | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Build and push server release Docker image (multi-platform)
      run: make ci.build-release
      env:
        DOCKER_REGISTRY: ${{ env.REGISTRY }}
        IMAGE_NAME: ${{ env.IMAGE_NAME }}
        GIT_SHA: ${{ github.sha }}
        GIT_BRANCH: ${{ steps.version.outputs.ref_name }}
        DOCKER_TAG: ${{ steps.version.outputs.docker_tag }}
        SHORT_SHA: ${{ steps.version.outputs.short_sha }}

    - name: Confirm image push (multi-platform)
      run: make ci.push-release
      env:
        DOCKER_REGISTRY: ${{ env.REGISTRY }}
        IMAGE_NAME: ${{ env.IMAGE_NAME }}
        GIT_SHA: ${{ github.sha }}
        GIT_BRANCH: ${{ steps.version.outputs.ref_name }}
        DOCKER_TAG: ${{ steps.version.outputs.docker_tag }}
        SHORT_SHA: ${{ steps.version.outputs.short_sha }}

    - name: Run security scan
      run: make ci.security-scan
      env:
        IMAGE_TAG: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.docker_tag }}
      continue-on-error: true

    - name: Generate build summary
      run: |
        echo "## 🚀 Server Release Build Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "- **Version:** ${{ steps.version.outputs.version_tag }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Release Tag:** ${{ steps.version.outputs.ref_name }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Docker Tag:** ${{ steps.version.outputs.docker_tag }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Server Image:** ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.docker_tag }}" >> $GITHUB_STEP_SUMMARY
        echo "- **SHA Tag:** ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ steps.version.outputs.short_sha }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Image Naming Convention:" >> $GITHUB_STEP_SUMMARY
        echo "- **Server**: \`bodhi-auth-server\` - Server-ready Keycloak extension" >> $GITHUB_STEP_SUMMARY
        echo "- **Testcontainer**: \`bodhi-auth-testcontainer\` - Simplified builds for testing" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Server Features:" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Multi-platform support (linux/amd64, linux/arm64)" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ PostgreSQL database support" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Server cluster configuration" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Advanced JVM optimizations" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Metrics and health endpoints enabled" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Security scanning completed" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Multi-stage build optimization" >> $GITHUB_STEP_SUMMARY 