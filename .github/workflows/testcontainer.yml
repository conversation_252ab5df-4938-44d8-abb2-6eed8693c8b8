name: Testcontainer Builds

on:
  push:
    tags:
      - 'release/testcontainer-v*'
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - 'ai-docs/**'
      - '.gitignore'
      - 'LICENSE'

  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch to build (will checkout and build from this branch)'
        required: true
        default: 'main'
        type: string

env:
  REGISTRY: ghcr.io

jobs:
  build:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        ref: ${{ github.event.inputs.branch || github.ref }}
    - name: Set branch name
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          echo "BRANCH_NAME=${{ github.event.inputs.branch }}" >> $GITHUB_ENV
        else
          echo "BRANCH_NAME=${{ github.ref_name }}" >> $GITHUB_ENV
        fi
    - name: Set Docker tag
      run: |
        BRANCH_NAME="${{ env.BRANCH_NAME }}"
        SHORT_SHA=$(echo "${{ github.sha }}" | cut -c1-7)
        
        if [ "$BRANCH_NAME" = "main" ]; then
          DOCKER_TAG="main"
        elif [[ "$BRANCH_NAME" =~ ^release/testcontainer-v([0-9]+\.[0-9]+\.[0-9]+.*)$ ]]; then
          # Extract version from release/testcontainer-vX.Y.Z format
          VERSION_TAG="${BASH_REMATCH[1]}"
          DOCKER_TAG="$VERSION_TAG"

        else
          # For all other branches, use short SHA
          DOCKER_TAG="$SHORT_SHA"
        fi
        echo "DOCKER_TAG=$DOCKER_TAG" >> $GITHUB_ENV
        echo "SHORT_SHA=$SHORT_SHA" >> $GITHUB_ENV
    - name: Set image name
      run: echo "IMAGE_NAME=$(echo '${{ github.repository_owner }}/bodhi-auth-testcontainer' | tr '[:upper:]' '[:lower:]')" >> $GITHUB_ENV
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    - name: Build and push Testcontainer Docker image (multi-platform)
      run: make ci.build-testcontainer
      env:
        DOCKER_REGISTRY: ${{ env.REGISTRY }}
        IMAGE_NAME: ${{ env.IMAGE_NAME }}
        GIT_SHA: ${{ github.sha }}
        GIT_BRANCH: ${{ env.BRANCH_NAME }}
        DOCKER_TAG: ${{ env.DOCKER_TAG }}
        SHORT_SHA: ${{ env.SHORT_SHA }}
    - name: Confirm image push (multi-platform)
      run: make ci.push-testcontainer
      env:
        DOCKER_REGISTRY: ${{ env.REGISTRY }}
        IMAGE_NAME: ${{ env.IMAGE_NAME }}
        GIT_SHA: ${{ github.sha }}
        GIT_BRANCH: ${{ env.BRANCH_NAME }}
        DOCKER_TAG: ${{ env.DOCKER_TAG }}
        SHORT_SHA: ${{ env.SHORT_SHA }}
    - name: Run security scan
      run: make ci.security-scan
      env:
        IMAGE_TAG: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.DOCKER_TAG }}
      continue-on-error: true
    - name: Generate build summary
      run: |
        echo "## 🛡️ Testcontainer Build Summary" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "- **Branch:** ${{ env.BRANCH_NAME }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Commit:** ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Docker Tag:** ${{ env.DOCKER_TAG }}" >> $GITHUB_STEP_SUMMARY
        echo "- **Image:** ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.DOCKER_TAG }}" >> $GITHUB_STEP_SUMMARY
        echo "- **SHA Tag:** ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ env.SHORT_SHA }}" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Tagging Strategy:" >> $GITHUB_STEP_SUMMARY
        echo "- **main branch:** uses 'main' tag" >> $GITHUB_STEP_SUMMARY
        echo "- **release/testcontainer-vX.Y.Z:** uses 'X.Y.Z' tag" >> $GITHUB_STEP_SUMMARY

        echo "- **other branches:** uses short SHA tag" >> $GITHUB_STEP_SUMMARY
        echo "" >> $GITHUB_STEP_SUMMARY
        echo "### Container Features:" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Multi-platform support (linux/amd64, linux/arm64)" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Simplified build process for faster testing" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Multi-stage build optimization" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Minimal runtime image" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ In-memory H2 database for fast startup" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Health endpoints enabled" >> $GITHUB_STEP_SUMMARY
        echo "- ✅ Security scanning completed" >> $GITHUB_STEP_SUMMARY 
