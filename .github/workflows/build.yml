name: Build and Test 

on:
  push:
    branches: ["*"]
    paths-ignore:
      - '**.md'
      - 'docs/**'
      - '.gitignore'
      - 'LICENSE'
      - '.github/workflows/release.yml'
      - '.github/workflows/testcontainer.yml'
      - '.cursor/**'
      - '.vscode/**'
      - 'ai-docs/**'
  pull_request:
    branches: ["*"]

permissions:
  contents: read
  packages: write

env:
  CI: true

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: "21"
          distribution: "temurin"
          cache: "maven"

      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Build with <PERSON><PERSON> (no tests)
        run: mvn clean compile package -DskipTests

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '24'

      - name: Cache node_modules and Playwright binaries
        uses: actions/cache@v4
        with:
          path: |
            src/test/resources/test-app/node_modules
            ~/.cache/ms-playwright
          key: ${{ runner.os }}-node-${{ hashFiles('src/test/resources/test-app/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Setup CI environment and Playwright
        run: make ci.setup

      - name: Run all tests
        run: make ci.test

      - name: Archive Surefire Reports
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: surefire-reports
          path: target/surefire-reports/

      - name: Publish JUnit Test Report
        if: always()
        uses: mikepenz/action-junit-report@v4
        with:
          report_paths: 'target/surefire-reports/*.xml'
          require_tests: true

      - name: Publish Surefire HTML summary
        if: always()
        run: |
          echo "### Surefire HTML report available as artifact." >> $GITHUB_STEP_SUMMARY