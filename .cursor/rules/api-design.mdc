---
description: when designing/modifying/interacting with APIs
alwaysApply: false
---
### API Design Patterns

#### RESTful Conventions
- Follow RESTful conventions:
  - POST for creation (`/clients`, `/apps`)
  - GET for retrieval (`/clients/has-resource-admin`)
  - Use proper HTTP status codes (201 for creation, 200 for success, 4xx for client errors)
- Base URL pattern: `{keycloak-url}/realms/{realm}/bodhi/`
- Use consistent endpoint naming and structure

#### Request/Response Patterns
- All endpoints return JSON with proper content-type headers
- Use @JsonProperty for consistent JSON field naming
- Request DTOs: camelCase in Java with @JsonProperty for snake_case JSON
- Response DTOs: structured responses with consistent field names
- Error responses: `{"error": "message"}` format using ErrorResponse class

#### Authentication and Authorization
- Always validate authentication tokens before processing requests
- Use AuthenticationManager.AuthResult for token validation
- Check for service account tokens vs user tokens appropriately:
  - Service account tokens: for admin operations, client management
  - User tokens: for app client registration (must be from client-bodhi-dev-console)
- Implement proper authorization checks with custom methods

#### Error Handling
- Consistent error handling with ErrorResponse class
- Provide meaningful error messages without exposing internal details
- Use appropriate HTTP status codes for different error types
- Log errors with sufficient context for debugging

#### Data Validation
- Validate all input parameters before processing
- Check for required fields and valid formats
- Validate redirect URIs for client registration
- Ensure proper client type validation (public vs confidential)
