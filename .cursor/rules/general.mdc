---
alwaysApply: true
---

### Test Quality Standards
- Fewer, substantial test scenarios over many fine-grained tests
- Do not have try-catch, instead throw error to fail test, even for setup
- Do not have if-else conditionals in test, instead have test deterministic, testing known paths, have expect for those paths, failing otherwise
- Separate test cases for success and error scenarios
- In a single test, test a single flow, do not reuse the test setup for testing alternative paths
- Have the test setup in beforeAll/afterAll/beforeEach/afterEach as appropriate
- If have costly setup like starting server, have in beforeAll and have all similar test for that configuration reuse it
- If cheap setup like setting up mocks etc., have in beforeEach
- Fix root causes rather than using workarounds
- DO NOT MARK THE TEST AS SKIP IF YOU ARE NOT ABLE TO FIX, KEEP IT FAILING, AND END THE TASK WITH FAILING TO HAVE ALL TEST PASS
- Run the test using `mvn surefire:test`, and not `mvn test`

### Context Documentation Management
- Files in `ai-docs/01-features/` contain agile story specifications for implementing features, they define requirements to be implemented
- Files in `ai-docs/03-context/` contain current implementation context derived from actual codebase, they describe "as-is" state
- After major implementation changes, check `ai-docs/03-context/README.md` for context documents that need updates
- Each context document in README.md lists associated files with full paths for easy tracking of what needs updating
- When implementation changes affect tracked files, update the corresponding context document to reflect current state
- Context documents should always reflect the current implementation, not planned changes
