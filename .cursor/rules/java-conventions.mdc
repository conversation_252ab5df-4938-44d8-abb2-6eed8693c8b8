---
alwaysApply: true
---
### Java Coding Conventions

#### Code Style and Formatting
- Use 2-space indentation for all Java code (no tabs)
- Follow standard Java naming conventions:
  - Classes: PascalCase (e.g., `BodhiResourceProvider`)
  - Methods: camelCase (e.g., `newResourceInternal`)
  - Variables: camelCase (e.g., `clientId`)
  - Constants: UPPER_SNAKE_CASE (e.g., `RESOURCE_ADMIN`, `CLIENT_BODHI_DEV_CONSOLE`)
  - Packages: lowercase (e.g., `com.bodhisearch`)
- Import organization: static imports first, then regular imports, grouped by package
- Use final for constants and immutable variables where appropriate
- Empty line between logical code blocks

#### Exception Handling
- Use custom ResourceProviderException for business logic errors
- Provide meaningful error messages without exposing internal details
- Log errors with appropriate levels using SLF4J Logger
- Return structured error responses with ErrorResponse class
- In application code, use try-catch where needed for proper error handling

#### Class Structure
- Use static inner classes for request/response DTOs within provider classes
- Implement proper encapsulation with appropriate access modifiers
- Follow single responsibility principle for methods and classes
- Use @JsonProperty annotations for JSON serialization/deserialization

#### Constants and Configuration
- Define all string constants as static final fields
- Group related constants together (e.g., role names, group names)
- Use environment variables for configuration (e.g., APP_ENV)
- Support environment-specific behavior (test, dev, production)
### Java Coding Conventions

#### Code Style and Formatting
- Use 2-space indentation for all Java code (no tabs)
- Follow standard Java naming conventions:
  - Classes: PascalCase (e.g., `BodhiResourceProvider`)
  - Methods: camelCase (e.g., `newResourceInternal`)
  - Variables: camelCase (e.g., `clientId`)
  - Constants: UPPER_SNAKE_CASE (e.g., `RESOURCE_ADMIN`, `CLIENT_BODHI_DEV_CONSOLE`)
  - Packages: lowercase (e.g., `com.bodhisearch`)
- Import organization: static imports first, then regular imports, grouped by package
- Use final for constants and immutable variables where appropriate
- Empty line between logical code blocks

#### Exception Handling
- Use custom ResourceProviderException for business logic errors
- Provide meaningful error messages without exposing internal details
- Log errors with appropriate levels using SLF4J Logger
- Return structured error responses with ErrorResponse class
- In application code, use try-catch where needed for proper error handling

#### Class Structure
- Use static inner classes for request/response DTOs within provider classes
- Implement proper encapsulation with appropriate access modifiers
- Follow single responsibility principle for methods and classes
- Use @JsonProperty annotations for JSON serialization/deserialization

#### Constants and Configuration
- Define all string constants as static final fields
- Group related constants together (e.g., role names, group names)
- Use environment variables for configuration (e.g., APP_ENV)
- Support environment-specific behavior (test, dev, production)
