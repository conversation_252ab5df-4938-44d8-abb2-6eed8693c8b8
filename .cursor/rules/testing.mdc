### Testing Patterns and Infrastructure

#### Test Architecture
- All tests extend BaseTest for consistent Keycloak testcontainer setup
- Use JUnit 5 with @Test annotations

#### Test Utilities and Helpers
- Use BodhiProviderClient for extension endpoint calls
- Use KeycloakAdminClient for admin operations
- Use TestUser, ClientPair, TokenPair classes for test data
- Use static imports for assertion methods (assertThat, is, notNullValue)
- Follow pattern: create test data → execute operation → verify results

#### Integration Testing
- Use Testcontainers for Keycloak integration with proper configuration
- Use RealmConfigGenerator for dynamic test realm creation
- Environment configuration: APP_ENV=test for test-specific features
- Container configuration: enable required features (admin-fine-grained-authz, token-exchange)

#### UI Testing with Playwright
- Use headless mode for CI environments (System.getenv("CI"))
- Implement proper page waiting strategies
- Use consistent selector patterns for UI elements
- Handle authentication flows through proper OAuth sequences
- Use UITestUtils for common OAuth flow automation

#### Test Data Management
- Use realistic test data that represents production scenarios
- Implement proper test cleanup to avoid state pollution
- Use consistent test user patterns across test suites
- Validate all test assumptions explicitly
- Support parallel test execution where possible

#### Node.js Test Application
- Keep test apps simple and focused on specific testing scenarios
- Use Express.js for test servers with minimal dependencies
- Implement proper error handling in async operations
- Use dynamic port allocation for parallel test execution
- Serve static files for UI testing scenarios

#### Mock and Assertion Patterns
- Use REST-assured for HTTP endpoint testing
- Implement proper JSON path assertions for response validation
- Use JWT token decoding for token content validation
- Verify HTTP status codes and response structure
- Test both success and error scenarios comprehensively
