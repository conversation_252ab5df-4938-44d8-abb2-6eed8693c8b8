---
globs: *.http
alwaysApply: false
---
# HTTPBook Scripts Rules

## Purpose
HTTPBook scripts (.http files) are developer debugging tools for Keycloak Bodhi Extension APIs. Focus on simplicity, debugging utility, and developer productivity.

## Reference Documentation
For comprehensive HTTPBook conventions, patterns, and examples, see: `ai-docs/03-context/httpbook-conventions.md`

## Core Conventions

### Variable Management and Scoping
- **HTTPyac Variable Scoping Rules:**
  1. Environment Variables (from `provideVariables` hook) - highest priority
  2. File Global Variables (defined in regions without names using `###`) - medium priority
  3. Request Variables (defined in named regions) - lowest priority

- **Global Variable Region Pattern:**
  ```http
  ###
  # Global Variables and Configuration
  @keycloak={{$dotenv KEYCLOAK_URL}}
  @keycloak=http://localhost:8080
  ```

- **Use `# @import` for importing shared variables:**
  - **Use `# @import ./common.http`** - the `#` prefix is required
  - **DO NOT use `@import common.http`** without `#` - it doesn't work
  - Import common variables from shared files
  - Use `###` without names for global variable regions

### Request Naming and Chaining
- **Use @name and @ref for request dependencies:**
  ```http
  ### Create Client
  # @name create_client
  POST {{endpoint}}
  
  ### Get Token
  # @name get_token
  # @ref create_client
  POST {{token_endpoint}}
  ```
- Use snake_case for request names
- Chain requests logically with `@ref` dependencies

### Response Handling
- **Use @response hooks for processing:**
  ```http
  POST {{endpoint}}
  
  {{@response
    if (response.statusCode === 201) {
      const credentials = extractClientCredentials(response);
      $global.client_id = credentials.client_id;
      $global.client_secret = credentials.client_secret;
    } else {
      handleError("Operation failed", response);
    }
  }}
  ```
- **Use `response.statusCode` (not `response.status`)** for HTTP status codes
- **Use `response.parsedBody || response.body`** for response data
- Store extracted values in `$global` object for cross-request access
- **Use `handleError(messagePrefix, response)` for all error handling**
- Use utility functions for common extractions

### Global Utility Functions
- **Define utility functions in `common.http` and import them:**
  ```http
  ###
  # Import shared variables and functions
  # @import ./common.http
  
  # All functions are defined in common.http:
  # - extractToken(response, tokenType)
  # - extractClientCredentials(response)
  # - handleError(messagePrefix, response)
  # - validateJWT(token)
  # - etc.
  ```
- **All utility functions should be in `common.http`** with proper exports
- Use `exports.functionName` to make functions available across requests
- **Always include `handleError` function for consistent error handling**
- Use `response.parsedBody || response.body` for response data in utility functions

### Environment Variables
- Use dotenv pattern: `@variable={{$dotenv ENV_VAR}}` followed by `@variable=fallback_value`
- Always provide fallback values
- Use snake_case naming (e.g., `keycloak`, `client_id`, `access_token`)
- Group related variables together

### Authentication
- **Always use Keycloak v26 Basic Auth format:**
  ```http
  Authorization: Basic {{client_id}}:{{client_secret}}
  Content-Type: application/x-www-form-urlencoded
  grant_type=client_credentials
  ```
- Store tokens in `$global`: `$global.access_token = extractToken(response)`
- Use Bearer format: `Authorization: Bearer {{$global.access_token}}`

### OAuth2 Integration
- **Prefer HTTPyac's built-in OAuth2 flows** over manual authorization code handling
- **Set OAuth2 variables using `@oauth2_*` naming convention** in global regions:
  ```http
  ###
  # OAuth2 variables for HTTPyac
  @oauth2_clientId={{$global.app_client_id}}
  @oauth2_clientSecret=""
  @oauth2_authorizationEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth
  @oauth2_tokenEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token
  @oauth2_redirectUri=http://localhost:3000/callback
  @oauth2_scope=openid email profile roles scope_user_power_user
  ```
- **Use `Authorization: oauth2 authorization_code`** header for authorization code flow
- **Use `oauth2Session.accessToken`** to access the token obtained by HTTPyac
- **Set `oauth2_clientSecret=""` for public clients** (empty string, not undefined)
- **HTTPyac automatically handles browser opening, callback server, and token exchange**

### Script Organization
- **File Structure:**
  ```http
  ###
  # Global Variables and Configuration
  @variable={{$dotenv ENV_VAR}}
  @variable=fallback_value
  
  {{
    // Global utility functions
    function utilityFunction() { /* ... */ }
    exports.utilityFunction = utilityFunction;
  }}
  
  ###
  ### First Operation
  # @name first_operation
  POST {{endpoint}}
  
  {{@response
    // Response handling
  }}
  
  ###
  ### Second Operation
  # @name second_operation
  # @ref first_operation
  POST {{endpoint}}
  
  {{@response
    // Response handling
  }}
  ```

### Logging & Error Handling
- **Console logging ONLY for errors** - no success/info logging
- **Use `handleError` function for all error handling:**
  ```javascript
  {{@response
    if (response.statusCode === 200) {
      $global.result = response.parsedBody || response.body;
    } else {
      handleError("Operation failed", response);
    }
  }}
  ```
- **The `handleError` function automatically includes status code and response body**
- **Use `response.statusCode` (not `response.status`)** for HTTP status codes
- **Use `response.parsedBody || response.body`** for response data
- Use utility functions for common extractions

### Variable Naming
- Use snake_case for all variables
- Standard variables: `keycloak`, `realm`, `provider_id`, `client_id`, `access_token`
- Use `$global.variable` for cross-request variables
- Be descriptive and maintain consistency

### Anti-Patterns to Avoid
- ❌ Using `@import` without `#` prefix (doesn't work)
- ❌ Old variable extraction: `@token={{response.body.access_token}}`
- ❌ Excessive logging (console.log for success/info)
- ❌ Multiple similar endpoints for same operation
- ❌ Complex validation logic or business rule checks
- ❌ Missing error handling in @response hooks
- ❌ Manual error handling instead of using `handleError` function
- ❌ Using `response.status` instead of `response.statusCode`
- ❌ Using `response.body` without checking `response.parsedBody`

### Best Practices
- Start with global variable region (`###` without name)
- Import shared variables and functions from `common.http`
- Use @name/@ref chains for request dependencies
- Use @response hooks for response processing
- Use `handleError(messagePrefix, response)` for all error handling
- Store cross-request data in `$global` object
- Define utility functions in `common.http` with proper exports
- Use `response.statusCode` and `response.parsedBody || response.body`
- Only log errors (handled automatically by `handleError`)
- Consolidate similar operations with configurable parameters
- Test incrementally with manual verification

### HTTPyac Native Features to Use
- `$global` object for cross-request variable storage
- `@name` and `@ref` for request chaining
- `@response` hooks for response processing
- `{{$dotenv VAR}}` for environment variable access
- `exports.functionName` for utility function sharing
- Global regions (`###` without names) for shared variables

### HTTPyac Response Structure
- **Use `response.statusCode`** instead of `response.status`
- **Use `response.parsedBody || response.body`** for response data
- Always check both parsedBody and body for compatibility

### Form Data Encoding
- Use single-line format: `grant_type=password&client_id=admin-cli&username=user`
- **DO NOT** use multi-line format with `&` prefixes (causes parsing errors)
