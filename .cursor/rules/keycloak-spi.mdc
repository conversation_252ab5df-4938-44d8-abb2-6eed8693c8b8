---
description: when creating/editing/interacting with keycloak SPI extensions or APIs
alwaysApply: false
---
### Keycloak SPI Implementation Patterns

#### Provider Architecture
- All providers implement appropriate SPI interfaces (RealmResourceProvider, PolicyProvider)
- Provider classes must have corresponding Factory classes
- Register SPI implementations in META-INF/services files
- Use dependency injection through KeycloakSession parameter
- Implement proper resource cleanup in close() methods

#### Resource Provider Patterns
- Use @Path, @POST, @GET, @Consumes, @Produces annotations consistently
- Separate internal method implementation from public endpoint methods
- Pattern: `public Response endpoint()` -> `Response endpointInternal()` with try-catch in public method
- Use KeycloakSession for accessing realm, users, groups, clients
- Implement proper authentication checks before processing requests

#### Environment-Aware Behavior
- Use APP_ENV environment variable to control behavior
- Support test, dev, and production environments
- Apply appropriate client ID prefixes:
  - Production: `app-`, `resource-`
  - Test: `test-app-`, `test-resource-`
- Enable/disable features based on environment (e.g., direct access grants in test)
- Use live_test query parameter for development testing

#### Client Management Patterns
- App Clients: Public clients for frontend applications
  - No client secrets
  - Standard flow enabled, direct access grants disabled
  - Simplified admin-only role structure
  - Authentication via client-bodhi-dev-console user tokens
- Resource Clients: Confidential clients for backend services
  - With client secrets and service account enabled
  - Full 4-level role hierarchy: resource_user, resource_power_user, resource_manager, resource_admin
  - Token exchange capabilities with audience validation

#### Group and Role Management
- Follow hierarchical group structure: `users-{client-id}/subgroups`
- Map roles to groups systematically with proper inheritance
- Use consistent naming patterns: users, power-users, managers, admins
- Implement proper role inheritance (admins have all lower roles)
### Keycloak SPI Implementation Patterns

#### Provider Architecture
- All providers implement appropriate SPI interfaces (RealmResourceProvider, PolicyProvider)
- Provider classes must have corresponding Factory classes
- Register SPI implementations in META-INF/services files
- Use dependency injection through KeycloakSession parameter
- Implement proper resource cleanup in close() methods

#### Resource Provider Patterns
- Use @Path, @POST, @GET, @Consumes, @Produces annotations consistently
- Separate internal method implementation from public endpoint methods
- Pattern: `public Response endpoint()` -> `Response endpointInternal()` with try-catch in public method
- Use KeycloakSession for accessing realm, users, groups, clients
- Implement proper authentication checks before processing requests

#### Environment-Aware Behavior
- Use APP_ENV environment variable to control behavior
- Support test, dev, and production environments
- Apply appropriate client ID prefixes:
  - Production: `app-`, `resource-`
  - Test: `test-app-`, `test-resource-`
- Enable/disable features based on environment (e.g., direct access grants in test)
- Use live_test query parameter for development testing

#### Client Management Patterns
- App Clients: Public clients for frontend applications
  - No client secrets
  - Standard flow enabled, direct access grants disabled
  - Simplified admin-only role structure
  - Authentication via client-bodhi-dev-console user tokens
- Resource Clients: Confidential clients for backend services
  - With client secrets and service account enabled
  - Full 4-level role hierarchy: resource_user, resource_power_user, resource_manager, resource_admin
  - Token exchange capabilities with audience validation

#### Group and Role Management
- Follow hierarchical group structure: `users-{client-id}/subgroups`
- Map roles to groups systematically with proper inheritance
- Use consistent naming patterns: users, power-users, managers, admins
- Implement proper role inheritance (admins have all lower roles)
