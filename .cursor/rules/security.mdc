---
description: handling/interacting with jwt tokens
alwaysApply: false
---
### Security Best Practices

#### Token Handling and Validation
- Validate all tokens before processing using AuthenticationManager
- Use proper token verification methods with signature validation
- Never log sensitive token information in logs
- Implement token exchange securely with audience validation
- Check token issuer and audience claims appropriately

#### Authorization Patterns
- Check permissions at method entry points before processing
- Use least-privilege principle for service accounts
- Validate user permissions before group/role assignments
- Implement proper client authentication checks:
  - `checkForServiceAccount()` for admin operations
  - `checkForDevConsoleUserToken()` for app client registration

#### Client Security Configuration
- App Clients: Public clients with no client secrets
  - Disable direct access grants in production
  - Enable only standard OAuth flows
  - Restrict to specific redirect URIs
- Resource Clients: Confidential clients with proper secrets
  - Enable service accounts for machine-to-machine auth
  - Configure token exchange permissions properly
  - Set up audience validation policies

#### Error Security
- Never expose sensitive information in error messages
- Use generic error messages for authentication failures
- Log detailed errors for debugging but return sanitized responses
- Implement proper rate limiting considerations for authentication endpoints

#### Environment Security
- Use different security configurations for test vs production
- Test environment: enhanced logging, relaxed constraints for testing
- Production environment: strict validation, minimal error details
- Never expose test-specific endpoints in production