---
description: Project context, conventions, development practices 
alwaysApply: false
---
### Project-Specific Patterns

#### Multi-Tenant Client Architecture
- Two distinct client types with different purposes:
  - App Clients: Public clients for frontend applications (no client secrets)
  - Resource Clients: Confidential clients for backend services (with client secrets)
- Environment-aware client ID prefixes:
  - Production: `app-{id}`, `resource-{id}`
  - Test: `test-app-{id}`, `test-resource-{id}`
- Client-specific group hierarchies: `users-{client-id}/subgroups`

#### Token Exchange System
- Implement audience validation for token exchange between clients
- Use AudienceMatchPolicy for authorization decisions
- Support cross-client authentication flows securely
- Convert app client tokens to resource server tokens

#### Documentation and Comments
- Use logging only for critical interactions in test
- You can use logging in test for temporarily for debugging it, and removing it later
- Document complex business logic with clear comments
- Explain environment-specific behavior in code comments
- Document API endpoints with purpose and usage patterns
- Keep code self-documenting with clear method and variable naming

#### Logging Standards
- Use SLF4J Logger with class-specific logger instances
- Log levels:
  - ERROR: System errors, unexpected exceptions
  - WARN: Business logic issues, authentication failures
  - INFO: Important state changes, successful operations
  - DEBUG: Detailed operation tracing
- Include relevant context in log messages (client IDs, user emails)
- Never log sensitive information (tokens, passwords)

#### Configuration and Environment
- Use RealmConfigGenerator for dynamic realm configuration
- Support different configurations for test vs production
- Use FreeMarker templates for configuration generation
- Environment detection: `APP_ENV` variable for behavior control
- Query parameter support: `live_test` for development testing

#### Maven and Build Patterns
- Use Java 17 as target version
- Keycloak 23.0.7 as base dependency
- Separate test dependencies from runtime dependencies
- Use proper plugin configuration for test execution
- Support Playwright browser installation via Maven

#### Code Organization
- Keep related functionality together in logical provider classes
- Use inner classes for request/response DTOs
- Implement proper encapsulation with appropriate access modifiers
- Follow single responsibility principle for methods and classes
- Organize imports: static imports first, then grouped by package