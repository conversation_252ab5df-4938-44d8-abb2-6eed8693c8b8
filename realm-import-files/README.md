# Keycloak Realm Import System

This directory contains Keycloak realm configuration files and import automation for different environments, designed to work with `keycloak-config-cli` tool.

## 🎯 Architecture Overview

The system uses keycloak-config-cli's sequential file processing capability with:

- **`common.json`** - Shared realm configuration (client scopes, basic settings)
- **Environment-specific files** - Client configurations and environment-specific overrides
- **Python import script** - Automated import tool with environment defaults
- **Makefile integration** - Simple `make` commands for each environment

## 📁 File Structure

```
realm-import-files/
├── README.md                 # This documentation
├── import_realm.py          # Python import script with environment defaults
├── common.json              # Shared realm configuration
├── test-env.json           # Test environment (allows direct grants & service accounts)
├── local-env.json          # Local development environment
├── dev-env.json            # Development environment  
├── main-env.json           # Main/staging environment
└── prod-env.json           # Production (enables email verification)
```

## 🔧 Environment Differences

| Environment | Registration | Email Verification | Direct Access Grants | Service Accounts | Default URL |
|-------------|--------------|-------------------|---------------------|------------------|-------------|
| **local**   | ✅ true      | ❌ false          | ❌ false            | ❌ false         | http://localhost:8080 |
| **test**    | ✅ true      | ❌ false          | ✅ true             | ✅ true          | https://test-id.getbodhi.app |
| **dev**     | ✅ true      | ❌ false          | ❌ false            | ❌ false         | https://dev-id.getbodhi.app |
| **main**    | ✅ true      | ❌ false          | ❌ false            | ❌ false         | https://main-id.getbodhi.app |
| **prod**    | ✅ true      | ✅ **true**       | ❌ false            | ❌ false         | https://id.getbodhi.app |

## 🚀 Usage

### Prerequisites: Dependencies and Environment Setup

**1. Install Python Dependencies:**
```bash
cd realm-import-files
pip install -r requirements.txt
```

**2. Environment File Setup:**

Before running imports, you must create environment files with your credentials:

```bash
# Copy example files and fill in actual credentials
cp httpyac-scripts/env/test.env.example httpyac-scripts/env/test.env
cp httpyac-scripts/env/dev.env.example httpyac-scripts/env/dev.env
cp httpyac-scripts/env/prod.env.example httpyac-scripts/env/prod.env

# Edit each .env file with your actual admin credentials
# The example files contain placeholder values that must be replaced
```

**Required variables in each .env file:**
- `KEYCLOAK_URL` - The Keycloak server URL
- `ADMIN_CLI_USERNAME` - Your Keycloak admin username (not the placeholder "admin")
- `ADMIN_CLI_PASSWORD` - Your Keycloak admin password (not the placeholder "admin")

### Option 1: Makefile Targets (Recommended)

```bash
# Use credentials from environment files
make import.local                                        # Uses local.env
make import.dev                                          # Uses dev.env  
make import.prod                                         # Uses prod.env

# Override URL while using credentials from env file
make import.dev KEYCLOAK_URL=http://localhost:8080       # Override dev URL

# Override all parameters (bypasses env file)
make import.prod KEYCLOAK_USER=admin KEYCLOAK_PASSWORD=secret123
```

### Option 2: Direct Python Script

```bash
# Install dependencies first (one-time setup)
cd realm-import-files && pip install -r requirements.txt

# Use credentials from environment files
python3 realm-import-files/import_realm.py local         # Uses local.env
python3 realm-import-files/import_realm.py dev           # Uses dev.env

# Override specific parameters
python3 realm-import-files/import_realm.py dev --url http://localhost:8080
python3 realm-import-files/import_realm.py prod --user admin --password secret123

# Get help
python3 realm-import-files/import_realm.py --help
```

### Option 3: Manual Multi-File Import

```bash
# Import common + environment-specific configuration
java -jar tools/keycloak-config-cli-26.1.0.jar \
  --import.files.locations=realm-import-files/common.json,realm-import-files/test-env.json \
  --keycloak.url=http://localhost:8080 \
  --keycloak.user=admin \
  --keycloak.password=admin
```

## 🐳 Testing with Docker

```bash
# Start ephemeral Keycloak server
docker run --rm -d -p 8080:8080 \
  -e KC_BOOTSTRAP_ADMIN_USERNAME=admin \
  -e KC_BOOTSTRAP_ADMIN_PASSWORD=admin \
  -e KC_HOSTNAME=http://localhost:8080 \
  --name test-keycloak \
  ghcr.io/bodhisearch/bodhi-auth-testcontainer:latest

# Test import with local environment
sleep 15
make import.local

# Cleanup
docker stop test-keycloak
```

## ⚡ Key Features

- **Environment defaults**: Each environment has pre-configured URLs
- **Multi-file support**: Uses comma-separated file paths for common + environment configs
- **Sequential processing**: Files are imported in order specified
- **Makefile integration**: Simple `make import.{env}` commands
- **Parameter overrides**: URL, user, and password can be customized
- **Error handling**: Proper exit codes for CI/CD integration

## 🛠️ Python Script Features

- **Environment file loading**: Loads credentials from `httpyac-scripts/env/{environment}.env` files
- **Automatic jar detection**: Finds keycloak-config-cli jar in tools directory
- **Environment validation**: Validates supported environments and required variables
- **File validation**: Checks for required configuration files and environment files
- **Rich output**: Progress indicators, environment file paths, and success/failure messages
- **Flexible parameters**: CLI parameters override environment file values
- **Error handling**: Clear error messages for missing files or variables

## 📝 Environment Configuration

The system loads configuration from environment files located in `httpyac-scripts/env/`:

- **local.env**: Local development configuration
- **test.env**: Test environment configuration  
- **dev.env**: Development environment configuration
- **main.env**: Main/staging environment configuration
- **prod.env**: Production environment configuration

Each environment file must contain:
- `KEYCLOAK_URL`: The Keycloak server URL
- `ADMIN_CLI_USERNAME`: Your Keycloak admin username  
- `ADMIN_CLI_PASSWORD`: Your Keycloak admin password

**Setup Process:**
1. Copy the `.env.example` file to `.env` for your environment
2. Replace placeholder values with your actual credentials
3. Never commit `.env` files to version control (they contain secrets)

## 🔗 Integration

### CI/CD Pipeline Integration
```bash
# Example deployment script
make import.${ENVIRONMENT} KEYCLOAK_USER=${KEYCLOAK_ADMIN_USER} KEYCLOAK_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD}
```

### GitHub Actions Example
```yaml
- name: Import Keycloak Configuration
  run: |
    make import.${{ matrix.environment }} \
      KEYCLOAK_USER=${{ secrets.KEYCLOAK_ADMIN_USER }} \
      KEYCLOAK_PASSWORD=${{ secrets.KEYCLOAK_ADMIN_PASSWORD }}
```

## 📝 Notes

- **Parameter change**: Uses `--import.files.locations` (not `--import.path`)
- **Sequential processing**: Files processed in order: common.json → {env}-env.json
- **Order matters**: Common configuration imported before environment-specific
- **Realm target**: All configurations target the "bodhi" realm
- **Client secrets**: Change from "change-me" in production environments
- **Exit codes**: Scripts return 0 on success, 1 on failure for CI/CD integration

## 🔗 Related Documentation

- [keycloak-config-cli Documentation](https://adorsys.github.io/keycloak-config-cli/)
- [Keycloak Realm Import/Export](https://www.keycloak.org/docs/latest/server_admin/#_export_import) 