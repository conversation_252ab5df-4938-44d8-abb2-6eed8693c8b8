#!/usr/bin/env python3
"""
Keycloak Realm Import Script

This script imports Keycloak realm configurations using keycloak-config-cli.
It supports multiple environments with environment variables loaded from .env files.
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path
from dotenv import load_dotenv

# Environment-specific default URLs (fallback if not in .env)
DEFAULT_URLS = {
    "local": "http://localhost:8080",
    "test": "https://test-id.getbodhi.app",
    "dev": "https://dev-id.getbodhi.app",
    "main": "https://main-id.getbodhi.app",
    "prod": "https://id.getbodhi.app",
}


def get_script_dir():
    """Get the directory containing this script."""
    return Path(__file__).parent.absolute()


def get_env_file_path(env):
    """Get the path to the environment file for the specified environment."""
    script_dir = get_script_dir()
    # Look for env files in httpyac-scripts/env directory
    env_dir = script_dir.parent / "httpyac-scripts" / "env"
    env_file = env_dir / f"{env}.env"
    return env_file


def load_environment_variables(env):
    """Load environment variables from the .env file for the specified environment."""
    env_file = get_env_file_path(env)

    if not env_file.exists():
        raise FileNotFoundError(
            f"Environment file not found: {env_file}\n"
            f"Please copy {env_file.parent}/{env}.env.example to {env_file} and fill in the required values."
        )

    # Load environment variables from the .env file
    load_dotenv(env_file)

    # Get required variables
    keycloak_url = os.getenv("KEYCLOAK_URL")
    admin_username = os.getenv("ADMIN_CLI_USERNAME")
    admin_password = os.getenv("ADMIN_CLI_PASSWORD")

    # Validate required variables are present
    missing_vars = []
    if not keycloak_url:
        missing_vars.append("KEYCLOAK_URL")
    if not admin_username:
        missing_vars.append("ADMIN_CLI_USERNAME")
    if not admin_password:
        missing_vars.append("ADMIN_CLI_PASSWORD")

    if missing_vars:
        raise ValueError(
            f"Missing required environment variables in {env_file}: {', '.join(missing_vars)}\n"
            f"Please ensure these variables are set in your {env}.env file."
        )

    return keycloak_url, admin_username, admin_password


def get_keycloak_config_cli_path():
    """Get the path to keycloak-config-cli jar file."""
    script_dir = get_script_dir()
    # Look for the jar in tools directory relative to project root
    tools_dir = script_dir.parent / "tools"
    jar_files = list(tools_dir.glob("keycloak-config-cli-*.jar"))

    if not jar_files:
        raise FileNotFoundError(
            f"keycloak-config-cli jar file not found in {tools_dir}. "
            "Please ensure the jar file is present in the tools directory."
        )

    # Use the first jar file found (assuming there's only one)
    return jar_files[0]


def validate_environment(env):
    """Validate that the environment is supported."""
    if env not in DEFAULT_URLS:
        raise ValueError(
            f"Unsupported environment: {env}. "
            f"Supported environments: {', '.join(DEFAULT_URLS.keys())}"
        )


def get_config_files(env):
    """Get the configuration files for the specified environment."""
    script_dir = get_script_dir()
    common_config = script_dir / "common.json"
    env_config = script_dir / f"{env}-env.json"

    if not common_config.exists():
        raise FileNotFoundError(f"Common configuration file not found: {common_config}")

    if not env_config.exists():
        raise FileNotFoundError(
            f"Environment configuration file not found: {env_config}"
        )

    return str(common_config), str(env_config)


def import_realm(env, url=None, user=None, password=None):
    """Import realm configuration for the specified environment."""
    validate_environment(env)

    # Load environment variables from .env file
    try:
        env_url, env_user, env_password = load_environment_variables(env)
    except (FileNotFoundError, ValueError) as e:
        print(f"Error loading environment variables: {e}", file=sys.stderr)
        return 1

    # Use CLI parameters if provided, otherwise use environment variables
    final_url = url if url is not None else env_url
    final_user = user if user is not None else env_user
    final_password = password if password is not None else env_password

    # Get configuration files
    common_config, env_config = get_config_files(env)

    # Get keycloak-config-cli path
    try:
        jar_path = get_keycloak_config_cli_path()
    except FileNotFoundError as e:
        print(f"Error: {e}", file=sys.stderr)
        return 1

    # Build the command
    cmd = [
        "java",
        "-jar",
        str(jar_path),
        f"--import.files.locations={common_config},{env_config}",
        f"--keycloak.url={final_url}",
        f"--keycloak.user={final_user}",
        f"--keycloak.password={final_password}",
    ]

    env_file_path = get_env_file_path(env)
    print(f"🔧 Importing Keycloak realm configuration for {env.upper()} environment...")
    print(f"   Environment file: {env_file_path}")
    print(f"   Keycloak URL: {final_url}")
    print(f"   Admin user: {final_user}")
    print(f"   Common config: {common_config}")
    print(f"   Environment config: {env_config}")
    print()

    # Execute the command
    try:
        result = subprocess.run(cmd, check=True)
        print(f"✅ {env.capitalize()} environment realm import completed successfully!")
        return 0
    except subprocess.CalledProcessError as e:
        print(
            f"❌ {env.capitalize()} environment realm import failed!", file=sys.stderr
        )
        return e.returncode
    except KeyboardInterrupt:
        print("\n⚠️ Import interrupted by user", file=sys.stderr)
        return 130


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Import Keycloak realm configuration for specific environments",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Environment Configuration:
The script loads configuration from httpyac-scripts/env/{environment}.env files.
Each environment file must contain:
  - KEYCLOAK_URL: The Keycloak server URL
  - ADMIN_CLI_USERNAME: Keycloak admin username
  - ADMIN_CLI_PASSWORD: Keycloak admin password

Setup Instructions:
1. Copy the example file: cp httpyac-scripts/env/test.env.example httpyac-scripts/env/test.env
2. Edit the .env file with your actual credentials
3. Run the import script

Examples:
  %(prog)s local                                    # Use credentials from local.env
  %(prog)s dev                                      # Use credentials from dev.env
  %(prog)s prod --url http://localhost:8080         # Override URL, use prod.env credentials
  %(prog)s test --user admin --password secret123   # Override all credentials
        """,
    )

    parser.add_argument(
        "environment",
        choices=list(DEFAULT_URLS.keys()),
        help="Target environment for realm import",
    )

    parser.add_argument(
        "--url", help="Keycloak server URL (overrides environment file)"
    )

    parser.add_argument(
        "--user", help="Keycloak admin username (overrides environment file)"
    )

    parser.add_argument(
        "--password", help="Keycloak admin password (overrides environment file)"
    )

    args = parser.parse_args()

    try:
        return import_realm(args.environment, args.url, args.user, args.password)
    except Exception as e:
        print(f"Error: {e}", file=sys.stderr)
        return 1


if __name__ == "__main__":
    sys.exit(main())
