# Makefile for Keycloak Bodhi Extension

.PHONY: clean compile test test-unit test-integration help release-server release-testcontainer check-latest-versions

# Default target
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z0-9._-]+:.*?## / {printf "  \033[36m%-25s\033[0m %s\n", $$1, $$2}' $(MAKEFILE_LIST)

clean: ## Clean the project
	@echo "Cleaning project..."
	./mvnw clean

compile: ## Compile the project
	@echo "Compiling project..."
	./mvnw compile test-compile

test: clean compile ## Clean, compile and run all tests
	@echo "Running all tests..."
	./mvnw surefire:test

test-unit: compile ## Run only unit tests (excluding integration tests)
	@echo "Running unit tests..."
	./mvnw surefire:test -Dtest="*Test"

test-integration: compile ## Run only integration tests
	@echo "Running integration tests..."
	./mvnw surefire:test -Dtest="*IntegrationTest"

ci.setup: ## Setup CI environment with dependencies and Playwright
	# Download all Maven dependencies offline
	./mvnw dependency:go-offline
	# Install system dependencies for Playwright browsers
	sudo ./mvnw -q exec:java -Dexec.mainClass=com.microsoft.playwright.CLI -Dexec.classpathScope=test -Dexec.args="install-deps"
	# Install Playwright Chromium browser binaries
	./mvnw -q exec:java -Dexec.mainClass=com.microsoft.playwright.CLI -Dexec.classpathScope=test -Dexec.args="install chromium"

ci.test: clean compile ## Run all tests in CI environment
	./mvnw surefire:test

ci.setup-obfuscation: ## Setup obfuscation environment and validate configuration
	@echo "Setting up obfuscation environment..."
	# Ensure ProGuard configuration is present
	@test -f proguard-unified.pro || (echo "ProGuard unified config not found" && exit 1)
	@test -f obfuscation-dictionary.txt || (echo "Obfuscation dictionary not found" && exit 1)
	@echo "Obfuscation setup complete"

ci.build-release: ## Build server release Docker image
	@echo "Building server release Docker image..."
	@if [ -z "$$DOCKER_REGISTRY" ] || [ -z "$$IMAGE_NAME" ] || [ -z "$$GIT_SHA" ] || [ -z "$$DOCKER_TAG" ] || [ -z "$$SHORT_SHA" ]; then \
		echo "Error: Required environment variables not set"; \
		echo "Required: DOCKER_REGISTRY, IMAGE_NAME, GIT_SHA, DOCKER_TAG, SHORT_SHA"; \
		exit 1; \
	fi
	@BUILD_DATE=$$(date -u +%Y-%m-%dT%H:%M:%SZ); \
	echo "Building server release with primary tag: $$DOCKER_TAG"; \
	echo "Building server release with SHA tag: $$SHORT_SHA"; \
	docker buildx build \
		-f Dockerfile \
		--platform linux/amd64,linux/arm64 \
		--build-arg BUILD_DATE="$$BUILD_DATE" \
		--build-arg GIT_COMMIT="$$GIT_SHA" \
		--build-arg GIT_BRANCH="$$GIT_BRANCH" \
		-t "$$DOCKER_REGISTRY/$$IMAGE_NAME:$$DOCKER_TAG" \
		-t "$$DOCKER_REGISTRY/$$IMAGE_NAME:$$SHORT_SHA" \
		-t "$$DOCKER_REGISTRY/$$IMAGE_NAME:latest" \
		--push \
		.
	@echo "Server release image built with tags:"
	@echo "  Primary: $$DOCKER_REGISTRY/$$IMAGE_NAME:$$DOCKER_TAG"
	@echo "  SHA: $$DOCKER_REGISTRY/$$IMAGE_NAME:$$SHORT_SHA"

ci.push-release: ## Push server release Docker image to registry (now integrated with build)
	@echo "Multi-platform images are pushed automatically during build with --push flag"
	@echo "Images pushed:"
	@echo "  Primary: $$DOCKER_REGISTRY/$$IMAGE_NAME:$$DOCKER_TAG"
	@echo "  SHA: $$DOCKER_REGISTRY/$$IMAGE_NAME:$$SHORT_SHA"
	@echo "  Latest: $$DOCKER_REGISTRY/$$IMAGE_NAME:latest"

ci.build-testcontainer: ## Build testcontainer Docker image
	@echo "Building testcontainer Docker image..."
	@if [ -z "$$DOCKER_REGISTRY" ] || [ -z "$$IMAGE_NAME" ] || [ -z "$$GIT_SHA" ] || [ -z "$$DOCKER_TAG" ] || [ -z "$$SHORT_SHA" ]; then \
		echo "Error: Required environment variables not set"; \
		echo "Required: DOCKER_REGISTRY, IMAGE_NAME, GIT_SHA, DOCKER_TAG, SHORT_SHA"; \
		exit 1; \
	fi
	@BUILD_DATE=$$(date -u +%Y-%m-%dT%H:%M:%SZ); \
	echo "Building with primary tag: $$DOCKER_TAG"; \
	echo "Building with SHA tag: $$SHORT_SHA"; \
	docker buildx build \
		-f Dockerfile.testcontainer \
		--platform linux/amd64,linux/arm64 \
		--build-arg BUILD_DATE="$$BUILD_DATE" \
		--build-arg GIT_COMMIT="$$GIT_SHA" \
		--build-arg GIT_BRANCH="$$GIT_BRANCH" \
		-t "$$DOCKER_REGISTRY/$$IMAGE_NAME:$$DOCKER_TAG" \
		-t "$$DOCKER_REGISTRY/$$IMAGE_NAME:$$SHORT_SHA" \
		-t "$$DOCKER_REGISTRY/$$IMAGE_NAME:latest" \
		--push \
		.
	@echo "Testcontainer image built with tags:"
	@echo "  Primary: $$DOCKER_REGISTRY/$$IMAGE_NAME:$$DOCKER_TAG"
	@echo "  SHA: $$DOCKER_REGISTRY/$$IMAGE_NAME:$$SHORT_SHA"

ci.push-testcontainer: ## Push testcontainer Docker image to registry (now integrated with build)
	@echo "Multi-platform images are pushed automatically during build with --push flag"
	@echo "Images pushed:"
	@echo "  Primary: $$DOCKER_REGISTRY/$$IMAGE_NAME:$$DOCKER_TAG"
	@echo "  SHA: $$DOCKER_REGISTRY/$$IMAGE_NAME:$$SHORT_SHA"
	@echo "  Latest: $$DOCKER_REGISTRY/$$IMAGE_NAME:latest"

ci.security-scan: ## Run security scan on Docker image
	@echo "Running security scan..."
	@if [ -z "$$IMAGE_TAG" ]; then \
		echo "Error: IMAGE_TAG environment variable not set"; \
		exit 1; \
	fi
	# Try Trivy first
	@if command -v trivy >/dev/null 2>&1; then \
		echo "Running Trivy scan..."; \
		trivy image --severity HIGH,CRITICAL "$$IMAGE_TAG" || echo "Trivy scan completed with findings"; \
	else \
		echo "Trivy not found, skipping vulnerability scan"; \
	fi
	# Try Docker Scout if available
	@if docker scout --help >/dev/null 2>&1; then \
		echo "Running Docker Scout scan..."; \
		docker scout cves "$$IMAGE_TAG" || echo "Docker Scout scan completed with findings"; \
	else \
		echo "Docker Scout not available, skipping additional scan"; \
	fi
	@echo "Security scan completed"

ci.build-info: ## Display testcontainer build information
	@echo "=== Testcontainer Build Information ==="
	@echo "Docker Registry: $${DOCKER_REGISTRY:-not set}"
	@echo "Image Name: $${IMAGE_NAME:-not set}"
	@echo "Git SHA: $${GIT_SHA:-not set}"
	@echo "Git Branch: $${GIT_BRANCH:-not set}"
	@echo "Short SHA: $$(echo "$${GIT_SHA:-unknown}" | cut -c1-7)"
	@echo "==================================="

ci.release-info: ## Display server release build information
	@echo "=== Server Release Build Information ==="
	@echo "Docker Registry: $${DOCKER_REGISTRY:-not set}"
	@echo "Image Name: $${IMAGE_NAME:-not set}"
	@echo "Git SHA: $${GIT_SHA:-not set}"
	@echo "Git Branch: $${GIT_BRANCH:-not set}"
	@echo "Short SHA: $$(echo "$${GIT_SHA:-unknown}" | cut -c1-7)"
	@echo "Docker Tag: $${DOCKER_TAG:-not set}"
	@echo "=========================================="

# Function to get current version from GHCR for a package
# Usage: $(call get_ghcr_version,package-name,version-pattern)
# package-name: bodhi-auth-server or bodhi-auth-testcontainer
# version-pattern: server (for vX.Y.Z) or testcontainer (for X.Y.Z)
define get_ghcr_version
	REPO_OWNER=BodhiSearch && \
	GHCR_RESPONSE=$$(gh api "/orgs/$$REPO_OWNER/packages/container/$(1)/versions" 2>/dev/null || echo "Package not found") && \
	if echo "$$GHCR_RESPONSE" | grep -q "Package not found"; then \
		echo "No existing $(1) package found in GHCR, starting with version 0.0.1" && \
		echo "0.0.0"; \
	else \
		if [ "$(2)" = "server" ]; then \
			echo "$$GHCR_RESPONSE" | jq -r '[.[] | select(.metadata.container.tags[]? | startswith("v") and test("^v[0-9]+\\.[0-9]+\\.[0-9]+$$"))] | sort_by(.created_at) | last | .metadata.container.tags[] | select(startswith("v") and test("^v[0-9]+\\.[0-9]+\\.[0-9]+$$"))' 2>/dev/null | sed 's/^v//' || echo "0.0.0"; \
		else \
			echo "$$GHCR_RESPONSE" | jq -r '[.[] | select(.metadata.container.tags[]? | test("^[0-9]+\\.[0-9]+\\.[0-9]+$$"))] | sort_by(.created_at) | last | .metadata.container.tags[] | select(test("^[0-9]+\\.[0-9]+\\.[0-9]+$$"))' 2>/dev/null || echo "0.0.0"; \
		fi \
	fi
endef

# Function to get latest version for display purposes
# Usage: $(call get_ghcr_version_display,package-name,version-pattern)
define get_ghcr_version_display
	REPO_OWNER=BodhiSearch && \
	GHCR_RESPONSE=$$(gh api "/orgs/$$REPO_OWNER/packages/container/$(1)/versions" 2>/dev/null || echo "Package not found") && \
	if echo "$$GHCR_RESPONSE" | grep -q "Package not found"; then \
		echo "No $(1) package found in GHCR"; \
	else \
		if [ "$(2)" = "server" ]; then \
			echo "$$GHCR_RESPONSE" | jq -r '[.[] | select(.metadata.container.tags[]? | startswith("v") and test("^v[0-9]+\\.[0-9]+\\.[0-9]+$$"))] | sort_by(.created_at) | last | .metadata.container.tags[] | select(startswith("v") and test("^v[0-9]+\\.[0-9]+\\.[0-9]+$$"))' 2>/dev/null || echo "No versioned releases found"; \
		else \
			echo "$$GHCR_RESPONSE" | jq -r '[.[] | select(.metadata.container.tags[]? | test("^[0-9]+\\.[0-9]+\\.[0-9]+$$"))] | sort_by(.created_at) | last | .metadata.container.tags[] | select(test("^[0-9]+\\.[0-9]+\\.[0-9]+$$"))' 2>/dev/null || echo "No versioned releases found"; \
		fi \
	fi
endef

# Function to increment version and create release tag
# Usage: $(call create_release_tag,package-type,current-version,tag-prefix)
# package-type: server or testcontainer
# current-version: X.Y.Z format
# tag-prefix: "release/v" or "release/testcontainer-v"
define create_release_tag
	if [ "$(2)" = "null" ] || [ -z "$(2)" ]; then \
		CURRENT_VERSION="0.0.0"; \
	else \
		CURRENT_VERSION="$(2)"; \
	fi && \
	IFS='.' read -r MAJOR MINOR PATCH <<< "$$CURRENT_VERSION" && \
	NEXT_VERSION="$$MAJOR.$$MINOR.$$((PATCH + 1))" && \
	echo "Current $(1) version (from GHCR): $$CURRENT_VERSION" && \
	echo "Next $(1) version: $$NEXT_VERSION" && \
	TAG_NAME="$(3)$$NEXT_VERSION" && \
	$(call delete_tag_if_exists,$$TAG_NAME) && \
	echo "Creating $(1) release tag $$TAG_NAME..." && \
	git tag "$$TAG_NAME" && \
	git push origin "$$TAG_NAME" && \
	echo "$(1) release tag $$TAG_NAME pushed. GitHub workflow will handle the release process."
endef

# Function to check git branch status
define check_git_branch
	@CURRENT_BRANCH=$$(git branch --show-current) && \
	if [ "$$CURRENT_BRANCH" != "main" ]; then \
		read -p "Warning: You are not on main branch (current: $$CURRENT_BRANCH). Continue? [y/N] " confirm && \
		if [ "$$confirm" != "y" ]; then \
			echo "Aborting release." && exit 1; \
		fi \
	fi && \
	echo "Fetching latest changes from remote..." && \
	git fetch origin main && \
	LOCAL_HEAD=$$(git rev-parse HEAD) && \
	REMOTE_HEAD=$$(git rev-parse origin/main) && \
	if [ "$$LOCAL_HEAD" != "$$REMOTE_HEAD" ]; then \
		echo "Warning: Your local main branch is different from origin/main" && \
		echo "Local:  $$LOCAL_HEAD" && \
		echo "Remote: $$REMOTE_HEAD" && \
		read -p "Continue anyway? [y/N] " confirm && \
		if [ "$$confirm" != "y" ]; then \
			echo "Aborting release." && exit 1; \
		fi \
	fi
endef

# Function to safely delete existing tag
define delete_tag_if_exists
	echo "Checking for existing tag $(1)..." && \
	if git rev-parse "$(1)" >/dev/null 2>&1; then \
		read -p "Tag $(1) already exists. Delete and recreate? [y/N] " confirm && \
		if [ "$$confirm" = "y" ]; then \
			echo "Deleting existing tag $(1)..." && \
			git tag -d "$(1)" 2>/dev/null || true && \
			git push --delete origin "$(1)" 2>/dev/null || true; \
		else \
			echo "Aborting release." && exit 1; \
		fi \
	fi
endef

release-server: ## Create and push tag for server release
	@echo "Preparing to release server Docker image..."
	$(call check_git_branch)
	@echo "Fetching latest server release version from GHCR..."
	@CURRENT_VERSION=$$($(call get_ghcr_version,bodhi-auth-server,server)) && \
	$(call create_release_tag,server,$$CURRENT_VERSION,release/v)

release-testcontainer: ## Create and push tag for testcontainer release
	@echo "Preparing to release testcontainer Docker image..."
	$(call check_git_branch)
	@echo "Fetching latest testcontainer release version from GHCR..."
	@CURRENT_VERSION=$$($(call get_ghcr_version,bodhi-auth-testcontainer,testcontainer)) && \
	$(call create_release_tag,testcontainer,$$CURRENT_VERSION,release/testcontainer-v)

check-latest-versions: ## Check latest versions of both server and testcontainer releases from GHCR
	@echo "=== Latest Release Versions (from GHCR) ==="
	@echo "Server releases (bodhi-auth-server):"
	@PROD_VERSION=$$($(call get_ghcr_version_display,bodhi-auth-server,server)) && \
	echo "  Latest: $$PROD_VERSION"
	@echo ""
	@echo "Testcontainer releases (bodhi-auth-testcontainer):"
	@TC_VERSION=$$($(call get_ghcr_version_display,bodhi-auth-testcontainer,testcontainer)) && \
	echo "  Latest: $$TC_VERSION"
	@echo "==============================="

# Keycloak Realm Import Targets
# Usage: 
#   make import.local                                        # Use default URL for local
#   make import.dev KEYCLOAK_URL=http://localhost:8080       # Override dev URL
#   make import.prod KEYCLOAK_USER=admin KEYCLOAK_PASSWORD=secret123  # Custom credentials

import.local: ## Import realm configuration for local environment
	@python3 realm-import-files/import_realm.py local $(if $(KEYCLOAK_URL),--url $(KEYCLOAK_URL)) $(if $(KEYCLOAK_USER),--user $(KEYCLOAK_USER)) $(if $(KEYCLOAK_PASSWORD),--password $(KEYCLOAK_PASSWORD))

import.test: ## Import realm configuration for test environment
	@python3 realm-import-files/import_realm.py test $(if $(KEYCLOAK_URL),--url $(KEYCLOAK_URL)) $(if $(KEYCLOAK_USER),--user $(KEYCLOAK_USER)) $(if $(KEYCLOAK_PASSWORD),--password $(KEYCLOAK_PASSWORD))

import.dev: ## Import realm configuration for dev environment
	@python3 realm-import-files/import_realm.py dev $(if $(KEYCLOAK_URL),--url $(KEYCLOAK_URL)) $(if $(KEYCLOAK_USER),--user $(KEYCLOAK_USER)) $(if $(KEYCLOAK_PASSWORD),--password $(KEYCLOAK_PASSWORD))

import.main: ## Import realm configuration for main environment
	@python3 realm-import-files/import_realm.py main $(if $(KEYCLOAK_URL),--url $(KEYCLOAK_URL)) $(if $(KEYCLOAK_USER),--user $(KEYCLOAK_USER)) $(if $(KEYCLOAK_PASSWORD),--password $(KEYCLOAK_PASSWORD))

import.prod: ## Import realm configuration for prod environment
	@python3 realm-import-files/import_realm.py prod $(if $(KEYCLOAK_URL),--url $(KEYCLOAK_URL)) $(if $(KEYCLOAK_USER),--user $(KEYCLOAK_USER)) $(if $(KEYCLOAK_PASSWORD),--password $(KEYCLOAK_PASSWORD))
