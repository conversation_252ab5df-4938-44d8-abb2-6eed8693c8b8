{"realm": "bodhi", "enabled": true, "registrationAllowed": true, "registrationEmailAsUsername": true, "rememberMe": true, "verifyEmail": true, "loginWithEmailAllowed": true, "resetPasswordAllowed": true, "ssoSessionIdleTimeout": 172800, "ssoSessionMaxLifespan": 2592000, "ssoSessionIdleTimeoutRememberMe": 2592000, "ssoSessionMaxLifespanRememberMe": 2592000, "roles": {"client": {"resource-abcd": [{"name": "resource_user", "composite": false, "clientRole": true}, {"name": "resource_power_user", "composite": false, "clientRole": true}, {"name": "resource_manager", "composite": false, "clientRole": true}, {"name": "resource_admin", "composite": false, "clientRole": true}], "resource-wxyz": [{"name": "resource_user", "composite": false, "clientRole": true}, {"name": "resource_power_user", "composite": false, "clientRole": true}, {"name": "resource_manager", "composite": false, "clientRole": true}, {"name": "resource_admin", "composite": false, "clientRole": true}]}}, "clients": [{"clientId": "resource-abcd", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["http://localhost/callback"], "webOrigins": ["+"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "resource-wxyz", "enabled": true, "clientAuthenticatorType": "client-secret", "secret": "change-me", "redirectUris": ["http://localhost/callback"], "webOrigins": ["+"], "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "client-lmno", "enabled": true, "clientAuthenticatorType": "client-secret", "redirectUris": ["*"], "webOrigins": ["*"], "bearerOnly": false, "consentRequired": true, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "protocol": "openid-connect", "fullScopeAllowed": false}, {"clientId": "realm-management", "name": "${client_realm-management}", "enabled": true, "authorizationSettings": {"resources": [{"name": "client.resource.$resource-abcd", "type": "Client"}, {"name": "client.resource.$resource-wxyz", "type": "Client"}], "policies": [{"name": "audience-match-policy", "type": "audience-match", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {}}, {"name": "token-exchange.permission.client.$resource-abcd", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.$resource-abcd\"]", "scopes": "[\"token-exchange\"]", "applyPolicies": "[\"audience-match-policy\"]"}}, {"name": "token-exchange.permission.client.$resource-wxyz", "type": "scope", "logic": "POSITIVE", "decisionStrategy": "UNANIMOUS", "config": {"resources": "[\"client.resource.$resource-wxyz\"]", "scopes": "[\"token-exchange\"]", "applyPolicies": "[\"audience-match-policy\"]"}}], "scopes": [{"name": "token-exchange"}]}}], "users": [{"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "New", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": []}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Test", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": ["/users-resource-abcd/users"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Power", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": ["/users-resource-abcd/power-users"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "User", "lastName": "Manager", "credentials": [{"type": "password", "value": "pass"}], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": ["/users-resource-abcd/managers"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "User", "lastName": "Admin", "credentials": [{"type": "password", "value": "pass"}], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": ["/users-resource-abcd/admins"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "User", "lastName": "Other", "credentials": [{"type": "password", "value": "pass"}], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": []}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Other", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": ["/users-resource-wxyz/users"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Other", "lastName": "Manager", "credentials": [{"type": "password", "value": "pass"}], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": ["/users-resource-wxyz/managers"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "User", "lastName": "Admin", "credentials": [{"type": "password", "value": "pass"}], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": ["/users-resource-wxyz/admins"]}, {"username": "<EMAIL>", "email": "<EMAIL>", "enabled": true, "emailVerified": true, "firstName": "Some", "lastName": "User", "credentials": [{"type": "password", "value": "pass"}], "disableableCredentialTypes": [], "requiredActions": [], "notBefore": 0, "groups": []}], "groups": [{"name": "users-resource-abcd", "path": "/users-resource-abcd", "subGroups": [{"name": "users", "path": "/users-resource-abcd/users", "clientRoles": {"resource-abcd": ["resource_user"]}}, {"name": "power-users", "path": "/users-resource-abcd/power-users", "clientRoles": {"resource-abcd": ["resource_user", "resource_power_user"]}}, {"name": "managers", "path": "/users-resource-abcd/managers", "clientRoles": {"resource-abcd": ["resource_user", "resource_power_user", "resource_manager"]}}, {"name": "admins", "path": "/users-resource-abcd/admins", "clientRoles": {"resource-abcd": ["resource_user", "resource_power_user", "resource_manager", "resource_admin"]}}]}, {"name": "users-resource-wxyz", "path": "/users-resource-wxyz", "subGroups": [{"name": "users", "path": "/users-resource-wxyz/users", "clientRoles": {"resource-wxyz": ["resource_user"]}}, {"name": "power-users", "path": "/users-resource-wxyz/power-users", "clientRoles": {"resource-wxyz": ["resource_user", "resource_power_user"]}}, {"name": "managers", "path": "/users-resource-wxyz/managers", "clientRoles": {"resource-wxyz": ["resource_user", "resource_power_user", "resource_manager"]}}, {"name": "admins", "path": "/users-resource-wxyz/admins", "clientRoles": {"resource-wxyz": ["resource_user", "resource_power_user", "resource_manager", "resource_admin"]}}]}]}