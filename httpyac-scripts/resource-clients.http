### Resource Server Client Management
### This httpbook covers the complete lifecycle of resource server clients
### Uses HTTPyac native features for variable management and response handling

###
# Import shared variables
# @import ./common.http

# Variables imported from common.http via @import

# Functions imported from common.http via @import

###
### Get Dev Console Service Account Token
# @name get_dev_console_token
# Obtains a service account token for the dev console client
# Prerequisites: client-bodhi-dev-console with serviceAccountsEnabled=true
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{dev_console_client_id}}:{{dev_console_client_secret}}

grant_type=client_credentials

{{@response
  if (response.statusCode === 200) {
    $global.dev_console_token = extractToken(response, 'access_token');
  } else {
    handleError("Dev console token request failed", response);
  }
}}

###
### Create Resource Server Client
# @name create_resource_client
# @ref get_dev_console_token
# Creates a new resource server client with full 4-level role hierarchy
# Use ?live_test=true for test prefix
# Prerequisites: Dev console service account token
POST {{bodhi_resources_endpoint}}
Authorization: Bearer {{$global.dev_console_token}}
Content-Type: {{json_content_type}}

{
  "redirect_uris": ["{{resource_redirect_uri}}"]
}

{{@response
  if (response.statusCode === 200 || response.statusCode === 201) {
    const credentials = extractClientCredentials(response);
    $global.resource_client_id = credentials.client_id;
    $global.resource_client_secret = credentials.client_secret;
  } else {
    handleError("Resource client creation failed", response);
  }
}}

###
### Get Service Account Token for Resource Client
# @name get_resource_service_token
# @ref create_resource_client
# Obtains a service account token for the resource client (Keycloak v26 format)
# Prerequisites: Resource client with service account enabled
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=client_credentials&scope=service_account

{{@response
  if (response.statusCode === 200) {
    $global.resource_service_token = extractToken(response, 'access_token');
  } else {
    handleError("Service account token request failed", response);
  }
}}

###
### Check if Resource Client Has Admin
# @name check_resource_admin
# @ref get_resource_service_token
# Checks if the resource client has any admin users
# Prerequisites: Resource client service account token
GET {{has_admin_endpoint}}
Authorization: Bearer {{$global.resource_service_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.has_resource_admin = body.has_admin;
  } else {
    handleError("Admin check failed", response);
  }
}}

###
### Make First Resource Admin
# @name make_first_resource_admin
# @ref get_resource_service_token
# Makes a user the first admin of the resource client
# Prerequisites: Resource client service account token, user exists in realm
POST {{make_admin_endpoint}}
Authorization: Bearer {{$global.resource_service_token}}
Content-Type: {{json_content_type}}

{
  "username": "{{user_resource_admin}}"
}

{{@response
  if (response.statusCode === 201) {
    // Admin user created successfully
  } else {
    handleError("Make admin failed", response);
  }
}}

###
### Get User Token for Resource Admin
# @name get_resource_admin_token
# @ref create_resource_client
# Obtains a user token for the resource admin using password grant (Keycloak v26 format)
# Prerequisites: Resource client, user exists with admin role
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=password&username={{user_resource_admin}}&password={{user_resource_admin_password}}&scope=openid email profile roles

{{@response
  if (response.statusCode === 200) {
    $global.resource_admin_token = extractToken(response, 'access_token');
    const body = response.parsedBody || response.body;
    if (body.refresh_token) {
      $global.resource_admin_refresh_token = body.refresh_token;
    }
  } else {
    handleError("Resource admin token request failed", response);
  }
}}

###
### Add User to Group
# @name add_user_to_group
# @ref get_resource_admin_token
# Adds or removes a user from a group (requires admin token)
# Prerequisites: Resource admin token, user exists in realm
# Change username, group, and add values as needed:
# - group: "users", "power-users", "managers", "admins"
# - add: true (add to group), false (remove from group)
POST {{add_to_group_endpoint}}
Authorization: Bearer {{$global.resource_admin_token}}
Content-Type: {{json_content_type}}

{
  "username": "{{user_power_user}}",
  "group": "power-users",
  "add": true
}

{{@response
  if (response.statusCode === 201) {
    // User added to group successfully
  } else if (response.statusCode === 200) {
    // User was already in group
  } else {
    handleError("Group operation failed", response);
  }
}}

###
### Get Token for User
# @name get_user_token
# @ref create_resource_client
# Obtains a token for any user to test role hierarchy (Keycloak v26 format)
# Prerequisites: Resource client, user exists in realm with appropriate group membership
# Change username and password as needed:
# - {{user_power_user}} / {{user_power_user_password}}
# - {{user_manager}} / {{user_manager_password}}
# - {{user_regular}} / {{user_regular_password}}
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=password&username={{user_power_user}}&password={{user_power_user_password}}&scope=openid email profile roles

{{@response
  if (response.statusCode === 200) {
    $global.user_token = extractToken(response, 'access_token');
    
    // Validate token claims and roles
    const userInfo = extractUserInfo($global.user_token);
    
    if (userInfo.resource_access && userInfo.resource_access[$global.resource_client_id]) {
      const roles = userInfo.resource_access[$global.resource_client_id].roles || [];
    }
  } else {
    handleError("User token request failed", response);
  }
}}

###
### Refresh Token
# @name refresh_token
# @ref create_resource_client
# @ref get_resource_admin_token
# Refreshes any token using the refresh token (Keycloak v26 format)
# Prerequisites: Resource client, refresh token from previous login
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=refresh_token&refresh_token={{$global.resource_admin_refresh_token}}

{{@response
  if (response.statusCode === 200) {
    $global.resource_admin_token = extractToken(response, 'access_token');
    const body = response.parsedBody || response.body;
    if (body.refresh_token) {
      $global.resource_admin_refresh_token = body.refresh_token;
    }
  } else {
    handleError("Token refresh failed", response);
  }
}}

###
### Logout User
# @name logout_user
# @ref create_resource_client
# @ref get_resource_admin_token
# Logs out a user
# Prerequisites: Resource client, refresh token from user login
POST {{logout_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

refresh_token={{$global.resource_admin_refresh_token}}

{{@response
  if (response.statusCode === 204) {
    delete $global.resource_admin_token;
    delete $global.resource_admin_refresh_token;
  } else {
    handleError("Logout failed", response);
  }
}}

###
### Complete Resource Client Workflow
# @name complete_workflow
# Demonstrates the complete workflow from creation to user management
{{
  async function completeWorkflow() {
    const steps = [
      "create_resource_client",
      "get_resource_service_token", 
      "check_resource_admin",
      "make_first_resource_admin",
      "get_resource_admin_token",
      "add_user_to_group",
      "get_user_token"
    ];
    
    return { workflow: "ready", steps };
  }
  
  exports.workflow_info = completeWorkflow();
}}

###
### Test Role Hierarchy
# @name test_role_hierarchy
# @ref get_resource_admin_token
# Tests different user roles and their permissions
{{
  const testUsers = [
    { username: user_resource_admin, password: user_resource_admin_password, expectedRole: "resource_admin" },
    { username: user_power_user, password: user_power_user_password, expectedRole: "resource_power_user" },
    { username: user_manager, password: user_manager_password, expectedRole: "resource_manager" },
    { username: user_regular, password: user_regular_password, expectedRole: "resource_user" }
  ];
  
  async function testRoleHierarchy() {
    const results = [];
    
    for (const user of testUsers) {
      try {
        const tokenResponse = await fetch(token_endpoint, {
          method: 'POST',
          headers: {
            'Authorization': `Basic ${btoa($global.resource_client_id + ':' + $global.resource_client_secret)}`,
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: `grant_type=password&username=${user.username}&password=${user.password}&scope=openid email profile roles`
        });
        
        if (tokenResponse.statusCode === 200) {
          const tokenData = await tokenResponse.json();
          const userInfo = extractUserInfo(tokenData.access_token);
          const clientRoles = userInfo.resource_access[$global.resource_client_id]?.roles || [];
          
          results.push({
            username: user.username,
            expectedRole: user.expectedRole,
            actualRoles: clientRoles,
            hasExpectedRole: clientRoles.includes(user.expectedRole)
          });
        } else {
          results.push({
            username: user.username,
            error: `Token request failed: ${tokenResponse.statusCode}`
          });
        }
      } catch (error) {
        results.push({
          username: user.username,
          error: error.message
        });
      }
    }
    
    return results;
  }
  
  exports.role_test_promise = testRoleHierarchy();
}} 