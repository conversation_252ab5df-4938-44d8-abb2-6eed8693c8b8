# HTTPyac Scripts

This directory contains HTTPyac scripts for testing and interacting with the Keycloak Bodhi extension.

## Environment Configuration

### HTTPyac Environment System

The project uses HTTPyac's built-in environment system following the [official HTTPyac documentation](https://httpyac.github.io/guide/environments.html). Environment variables are organized in the `env/` folder:

- **Global variables**: `env/.env` (shared across all environments)  
- **Environment-specific variables**: `env/{environment}.env` (overrides for specific environments)

### Available Environments

| Environment | Keycloak URL | Description |
|-------------|--------------|-------------|
| `local` | `http://localhost:8080` | Local development (default) |
| `dev` | `https://dev-id.getbodhi.app` | Development environment |
| `main` | `https://main-id.getbodhi.app` | Main/integration environment |
| `test` | `https://test-id.getbodhi.app` | Test environment |
| `prod` | `https://id.getbodhi.app` | Production environment |

### Environment File Structure

```
httpyac-scripts/
├── env/
│   ├── .env          # Global variables (shared across all environments)
│   ├── local.env     # Local development overrides
│   ├── dev.env       # Development environment overrides
│   ├── main.env      # Main environment overrides
│   ├── test.env      # Test environment overrides
│   └── prod.env      # Production environment overrides
└── *.http            # HTTPyac script files
```

### Using Environments

#### Command Line Usage

```bash
# Use default environment (local)
httpyac send httpyac-scripts/app-clients.http --all

# Specify environment explicitly
httpyac send httpyac-scripts/app-clients.http --env dev --all
httpyac send httpyac-scripts/token-exchange.http --env main --all
httpyac send httpyac-scripts/resource-clients.http --env prod --all

# Run specific request with environment
httpyac send httpyac-scripts/app-clients.http --env test --name create_app_client
```

#### VS Code Extension

1. Install the HTTPyac VS Code extension
2. Open any `.http` file
3. Use the environment selector in the status bar
4. Click the play button to run requests

### Environment Variables

Variables are automatically loaded by HTTPyac:

- **Core Config**: `KEYCLOAK_URL`, `REALM`, `PROVIDER_ID`
- **Admin Config**: `ADMIN_CLI_CLIENT_ID`, `ADMIN_CLI_USERNAME`, `ADMIN_CLI_PASSWORD`
- **Test Users**: All users use standardized `@email.com` format with `pass` password
- **Dev Console**: `CLIENT_BODHI_DEV_CONSOLE`, `CLIENT_BODHI_DEV_CONSOLE_SECRET`

## Files

### Core Scripts
- `common.http` - Shared variables, functions, and utility requests
- `keycloak.http` - Keycloak admin operations
- `realm-management.http` - Realm management operations

### Authentication Scripts
- `app-clients.http` - App client management operations
- `offline-tokens.http` - Offline token management
- `token-exchange.http` - Token exchange operations

### Client Management Scripts
- `resource-clients.http` - Resource client management operations

## OAuth2 Integration with HTTPyac

HTTPyac provides built-in OAuth2 flow support for app client authentication.

### Key Features

1. **Automatic OAuth2 Flow**: HTTPyac handles the entire OAuth2 flow automatically
2. **Browser Integration**: Opens browser for user authorization
3. **Local Server**: Starts a local server to receive the OAuth callback
4. **Token Management**: Automatically stores and manages access tokens

### Usage Examples

```bash
# Test app client creation in different environments
httpyac send httpyac-scripts/app-clients.http --env local --name create_app_client
httpyac send httpyac-scripts/app-clients.http --env dev --name create_app_client
httpyac send httpyac-scripts/app-clients.http --env main --name create_app_client

# Test token exchange across environments
httpyac send httpyac-scripts/token-exchange.http --env test --all
httpyac send httpyac-scripts/token-exchange.http --env prod --all
```

## Running Scripts

### Prerequisites
- HTTPyac installed (`npm install -g httpyac`)
- HTTPyac VS Code extension (recommended)
- Keycloak server running (for local environment)

### Basic Usage

#### Command Line
```bash
# Run all requests in a file with default environment (local)
httpyac send httpyac-scripts/app-clients.http --all

# Run with specific environment
httpyac send httpyac-scripts/token-exchange.http --env main --all

# Run specific request with environment
httpyac send httpyac-scripts/resource-clients.http --env dev --name create_resource_client
```

#### VS Code Extension
1. Open any `.http` file
2. Select environment from status bar
3. Click play button next to requests
4. Use Ctrl+Alt+R to run current request

### Common Patterns

1. **Sequential execution**: Use `@ref` to chain requests
2. **Error handling**: Scripts use `handleError()` function for consistent error handling
3. **Token management**: Tokens are stored in `$global` variables for reuse
4. **Standardized credentials**: All users use `@email.com` format with `pass` password

## Development

### Adding New Scripts
1. Import `common.http` for shared variables and functions: `# @import ./common.http`
2. Use consistent error handling with `handleError()`
3. Store tokens in `$global` variables
4. Use `@ref` for request dependencies
5. Follow the naming convention: `script-name.http`

### Environment Configuration
1. Add global variables to `env/.env`
2. Add environment-specific overrides to `env/{environment}.env`
3. Test across all environments before committing
4. Use standardized credentials (`@email.com` and `pass`)

### Best Practices
- Use descriptive request names with `# @name`
- Add comments explaining complex operations
- Handle both success and error cases
- Use environment variables for configuration
- Test scripts with different environments
- Keep environment-specific credentials secure

## Troubleshooting

### Common Issues

1. **Environment not found**: Check that `env/{environment}.env` exists
2. **Variables not defined**: Verify variables are in `env/.env` or environment-specific file
3. **Authentication failures**: Check credentials for the specific environment
4. **File not found**: Ensure HTTPyac is run from the scripts directory or use full paths

### Debug Mode
```bash
# Enable debug logging
httpyac send httpyac-scripts/app-clients.http --env dev --all --verbose

# Check variable resolution
httpyac send httpyac-scripts/common.http --env main --name test_utilities --verbose
```

### Environment Validation
```bash
# Test basic connectivity for each environment
httpyac send httpyac-scripts/common.http --env local --name get_admin_cli_token
httpyac send httpyac-scripts/common.http --env dev --name get_admin_cli_token
httpyac send httpyac-scripts/common.http --env main --name get_admin_cli_token
```

### Environment Testing
Use the included test script to verify all environments:
```bash
./test-environments.sh
```

This will test all five environments and confirm they're working correctly. 