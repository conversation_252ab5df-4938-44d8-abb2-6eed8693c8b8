### Dynamic Audience Management for Marketplace - Keycloak 26.2+ Solution
###
# Import shared variables
# @import ./common.http

###
### 1. Get Dev Console User Token (OAuth2 Authorization Code Flow)
# @name get_dev_console_token
@oauth2_clientId={{dev_console_client_id}}
@oauth2_clientSecret={{dev_console_client_secret}}
@oauth2_authorizationEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth
@oauth2_tokenEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token
@oauth2_redirectUri=http://localhost:3000/callback
@oauth2_scope=openid email profile roles

GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: oauth2 authorization_code

{{@response
  if (response.statusCode === 200) {
    const userInfo = response.parsedBody || response.body;
    $global.dev_console_token = oauth2Session?.accessToken;
    if (!$global.dev_console_token) {
      handleError("OAuth2 access token not available", response);
    }
    
    // Validate token claims
    const tokenUserInfo = extractUserInfo($global.dev_console_token);
    console.log("✓ Dev console token obtained successfully");
  } else {
    handleError("Dev console token request failed", response);
  }
}}

###
### Step 1: Create App Clients (Public, No Secrets)
# @name create_app_clients
# @ref get_dev_console_token
POST {{bodhi_apps_endpoint}}
Authorization: Bearer {{$global.dev_console_token}}
Content-Type: {{json_content_type}}

{
  "name": "Marketplace Frontend",
  "description": "Main marketplace application",
  "redirect_uris": ["http://localhost:3000/callback"]
}

{{@response
  if (response.statusCode === 201) {
    const body = response.parsedBody || response.body;
    $global.app1_client_id = body.client_id;
    console.log("✅ Created app client:", $global.app1_client_id);
  } else {
    handleError("Failed to create app client", response);
  }
}}

###
### Step 2: Create Resource Server Clients (Confidential, With Secrets)
# @name create_resource_clients
POST {{bodhi_resources_endpoint}}
Authorization: Bearer {{$global.dev_console_token}}
Content-Type: {{json_content_type}}

{
  "name": "Test Resource Server",
  "description": "Test resource client",
  "redirect_uris": ["http://inventory:8080/callback"]
}

{{@response
  if (response.statusCode === 201) {
    const body = response.parsedBody || response.body;
    $global.resource1_client_id = body.client_id;
    $global.resource1_client_secret = body.client_secret;
    console.log("✅ Created resource client:", $global.resource1_client_id);
  } else {
    handleError("Failed to create resource client", response);
  }
}}

###
### Step 3: Get Resource Client Service Account Token
# @name get_resource1_token
# @ref create_resource_clients
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource1_client_id}}:{{$global.resource1_client_secret}}

grant_type=client_credentials&scope=service_account

{{@response
  if (response.statusCode === 200) {
    $global.resource1_token = extractToken(response, 'access_token');
    console.log("✅ Resource client service account token obtained");
  } else {
    handleError("Resource client token request failed", response);
  }
}}

###
### Step 4: Resource Client Requests Audience Access (Dynamic On-Demand)
# @name request_audience_access
# @ref get_resource1_token
# This is the KEY innovation - resource client requests to be included in app token audience
POST {{keycloak}}/realms/{{realm}}/{{provider_id}}/resources/request-access
Authorization: Bearer {{$global.resource1_token}}
Content-Type: {{json_content_type}}

{
  "app_client_id": "{{$global.app1_client_id}}"
}

{{@response
  if (response.statusCode === 201 || response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.resource_scope_name = body.scope;
    console.log("Response status:", response.statusCode);
    console.log("✅ Resource scope name:", $global.resource_scope_name);
  } else {
    handleError("Dynamic audience request failed", response);
  }
}}

###
### Step 5: App User Authorization with Resource-Specific Consent
# @name app_user_oauth_with_resource_scope
# @ref request_audience_access
# This demonstrates the consent flow - user sees exactly which resource they're granting access to
@oauth2_clientId={{$global.app1_client_id}}
@oauth2_clientSecret=""
@oauth2_authorizationEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth
@oauth2_tokenEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token
@oauth2_redirectUri=http://localhost:3000/callback
@oauth2_scope=openid email profile roles scope_user_user {{$global.resource_scope_name}}

GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: oauth2 authorization_code

{{@response
  if (response.statusCode === 200) {
    const userInfo = response.parsedBody || response.body;
    $global.app_user_token = oauth2Session?.accessToken;
    if (!$global.app_user_token) {
      handleError("OAuth2 access token not available", response);
    }
    console.log(`Token: ${$global.app_user_token}`);
    const tokenUserInfo = extractUserInfo($global.app_user_token);
    console.log(`Token: ${JSON.stringify(tokenUserInfo, null, 2)}`);
    // Verify the resource client is in the audience
    let hasResourceAudience = false;
    if (Array.isArray(tokenUserInfo.aud)) {
      hasResourceAudience = tokenUserInfo.aud.includes($global.resource1_client_id);
    } else if (tokenUserInfo.aud === $global.resource1_client_id) {
      hasResourceAudience = true;
    }
    
    if (hasResourceAudience) {
      console.log("✅ SUCCESS: Resource client found in token audience");
      console.log("🎯 READY: Token exchange will work without 'client not in audience' error");
    } else {
      console.error("❌ ISSUE: Resource client not found in token audience");
    }
  } else {
    handleError("OAuth2 authorization failed", response);
  }
}}

###
### Step 6: Token Exchange V2 - Should Work Seamlessly
# @name token_exchange_v2_marketplace
# @ref app_user_oauth_with_resource_scope
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource1_client_id}}:{{$global.resource1_client_secret}}

subject_token={{$global.app_user_token}}&scope=openid email profile roles scope_user_user&grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token

{{@response
  console.log("=== TOKEN EXCHANGE V2 MARKETPLACE TEST ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.exchanged_token = body.access_token;
    
    console.log("✅ Token exchange successful!");
    console.log("Token type:", body.token_type);
    console.log("Expires in:", body.expires_in, "seconds");
    
    // Analyze the exchanged token
    const exchangedUserInfo = extractUserInfo($global.exchanged_token);
    console.log("--- Exchanged Token Analysis ---");
    console.log("Issued to client:", exchangedUserInfo.azp);
    console.log("Audience:", exchangedUserInfo.aud);
    console.log("Subject:", exchangedUserInfo.sub);
    console.log("Scopes:", exchangedUserInfo.scope);
    
    console.log("🎯 MARKETPLACE SUCCESS: Dynamic audience approach works!");
    
  } else {
    const body = response.parsedBody || response.body;
    console.error("❌ Token exchange failed");
    console.error("Status:", response.statusCode);
    console.error("Error:", body.error);
    console.error("Description:", body.error_description);
  }
}}

###
### Cleanup
# @name cleanup
{{
  const variablesToClear = [
    'app1_client_id', 'resource1_client_id', 'resource1_client_secret',
    'resource1_token', 'resource_scope_name', 'app_user_token', 'exchanged_token'
  ];
  
  variablesToClear.forEach(varName => {
    if ($global[varName]) {
      delete $global[varName];
    }
  });
  
  console.log("✅ Variables cleared");
}} 