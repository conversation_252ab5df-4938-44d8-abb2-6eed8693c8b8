### Keycloak Simple Client Operations
### Basic client creation and token operations for testing
### Uses HTTPyac native features for proper variable management and response handling

###
# Import shared variables and functions
# @import ./common.http

###
### Step 1: Create Client - Expect 201 status
# @name create_client
POST {{keycloak}}/realms/{{realm}}/bodhi/resources?live_test=true
Content-Type: application/json

{
  "name": "Test Resource",
  "description": "Test resource client",
  "redirect_uris": ["http://localhost:1135/ui/auth/callback"]
}

{{@response
  if (response.statusCode === 201) {
    const credentials = extractClientCredentials(response);
    $global.client_id = credentials.client_id;
    $global.client_secret = credentials.client_secret;
  } else {
    handleError("Client creation failed", response);
  }
}}


###
### Step 2: Get OAuth Token - Keycloak v26 format with Basic Auth
# @name get_service_token
# @ref create_client
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.client_id}}:{{$global.client_secret}}

grant_type=client_credentials

{{@response
  if (response.statusCode === 200) {
    $global.resource_service_token = extractToken(response, 'access_token');
    const body = response.parsedBody || response.body;
    if (body.refresh_token) {
      $global.refresh_token = body.refresh_token;
    }
  } else {
    handleError("Token request failed", response);
  }
}}