### Offline Token Management
### This httpbook focuses specifically on offline token flows and long-lived token management
### Uses HTTPyac native features for proper variable management and response handling

###
# Import shared variables and functions
# @import ./common.http

# Variables imported from common.http via @import

###
### Setup - Create Resource Client for Offline Tokens
# @name setup_offline_resource_client
# Creates a resource client for offline token testing
POST {{bodhi_resources_endpoint}}?live_test=true
Content-Type: {{json_content_type}}

{
  "redirect_uris": ["http://localhost:8090/callback"]
}

{{@response
  if (response.statusCode === 201) {
    const credentials = extractClientCredentials(response);
    $global.offline_resource_client_id = credentials.client_id;
    $global.offline_resource_client_secret = credentials.client_secret;
  } else {
    handleError("Offline resource client creation failed", response);
  }
}}

###
### Setup - Get Service Account Token for Offline Client
# @name setup_offline_service_token
# @ref setup_offline_resource_client
# Gets service account token for the offline resource client
POST {{token_endpoint}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}
Content-Type: {{form_content_type}}

grant_type=client_credentials&scope=service_account

{{@response
  if (response.statusCode === 200) {
    $global.offline_service_token = extractToken(response, 'access_token');
    
    const payload = validateJWT($global.offline_service_token);
  } else {
    handleError("Service token request failed", response);
  }
}}

###
### Setup - Make Admin User for Offline Client
# @name setup_offline_admin
# @ref setup_offline_service_token
# Makes a user the admin of the offline resource client
POST {{make_admin_endpoint}}
Authorization: Bearer {{$global.offline_service_token}}
Content-Type: {{json_content_type}}

{
  "username": "{{user_offline_username}}"
}

{{@response
  if (response.statusCode === 201) {
    // Admin user created successfully
  } else if (response.statusCode === 400) {
    const body = response.parsedBody || response.body;
    if (body.error === "resource already has a admin user") {
      // Offline client already has an admin user - this is OK
    } else {
      handleError("Make admin failed", response);
    }
  } else {
    handleError("Make admin failed", response);
  }
}}

###
### Get User Token with Offline Access
# @name get_offline_user_token
# @ref setup_offline_admin
# Gets a user token with offline_access scope for long-lived access
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}

grant_type=password&username={{user_offline_username}}&password={{user_offline_password}}&scope=openid email profile roles offline_access scope_token_power_user

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body.access_token && body.refresh_token) {
      $global.offline_user_access_token = body.access_token;
      $global.offline_user_refresh_token = body.refresh_token;
      
      // Validate the token
      const payload = validateJWT(body.access_token);
      
      // Check for offline_access scope
      if (!payload.scope || !payload.scope.includes('offline_access')) {
        console.error("Token missing offline_access scope");
      }
      
      // Check for custom token scope
      if (!payload.scope || !payload.scope.includes('scope_token_power_user')) {
        console.error("Token missing scope_token_power_user");
      }
    } else {
      console.error(`Offline tokens not found: ${JSON.stringify(body)}`);
      throw new Error(`Offline tokens not found`);
    }
  } else {
    handleError("Offline user token request failed", response);
  }
}}

###
### Exchange to Offline Token (Method 1: Direct Exchange)
# @name exchange_to_offline_token
# @ref get_offline_user_token
# Exchanges a user token to an offline token using token exchange
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.offline_user_access_token}}&requested_token_type=urn:ietf:params:oauth:token-type:refresh_token&scope=offline_access scope_token_power_user

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body.access_token && body.refresh_token) {
      $global.offline_exchanged_access_token = body.access_token;
      $global.offline_exchanged_refresh_token = body.refresh_token;
      
      // Validate the offline token
      const payload = validateJWT(body.access_token);
      
      // Verify offline token characteristics
      if (!payload.scope || !payload.scope.includes('offline_access')) {
        console.error("Offline token missing offline_access scope");
      }
      
      // Check that resource_access is missing (offline tokens don't have roles)
      if (payload.resource_access && Object.keys(payload.resource_access).length > 0) {
        console.error(`Offline token unexpectedly has resource_access: ${JSON.stringify(payload.resource_access)}`);
      }
      
      // Verify token type scope
      if (!payload.scope || !payload.scope.includes('scope_token_power_user')) {
        console.error("Offline token missing scope_token_power_user");
      }
    } else {
      console.error(`Offline tokens not found: ${JSON.stringify(body)}`);
      throw new Error(`Offline tokens not found`);
    }
  } else {
    handleError("Offline token exchange failed", response);
  }
}}

###
### Refresh Offline Token (First Refresh)
# @name refresh_offline_token_1
# @ref exchange_to_offline_token
# Refreshes the offline token to get a new access token
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}

grant_type=refresh_token&refresh_token={{$global.offline_exchanged_refresh_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body.access_token) {
      $global.offline_refreshed_1_access_token = body.access_token;
      if (body.refresh_token) {
        $global.offline_exchanged_refresh_token = body.refresh_token;
      }
      
      // Validate the refreshed offline token
      const payload = validateJWT(body.access_token);
      
      // Verify offline token characteristics are maintained
      if (!payload.scope || !payload.scope.includes('offline_access')) {
        console.error("Refreshed token missing offline_access scope");
      }
      
      if (payload.resource_access && Object.keys(payload.resource_access).length > 0) {
        console.error("Refreshed token unexpectedly has resource_access");
      }
      
      if (!payload.scope || !payload.scope.includes('scope_token_power_user')) {
        console.error("Refreshed token missing scope_token_power_user");
      }
    } else {
      console.error(`Refreshed token not found: ${JSON.stringify(body)}`);
      throw new Error(`Refreshed token not found`);
    }
  } else {
    handleError("Offline token refresh failed", response);
  }
}}

###
### Multiple Refresh Cycles Test
# @name multiple_refresh_cycles
# @ref refresh_offline_token_1
# Tests multiple refresh cycles to ensure offline tokens work long-term
{{
  // Simulate multiple refresh cycles
  const refreshCycles = 3;
  let currentRefreshToken = $global.offline_exchanged_refresh_token;
  
  if (!currentRefreshToken) {
    console.error("No refresh token available for testing");
    throw new Error("Run the previous steps to get a refresh token");
  }
  
  $global.refresh_cycles_remaining = refreshCycles;
  $global.refresh_cycle_current = 1;
}}

POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}

grant_type=refresh_token&refresh_token={{$global.offline_exchanged_refresh_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body.access_token) {
      const currentCycle = $global.refresh_cycle_current;
      const remainingCycles = $global.refresh_cycles_remaining;
      
      $global[`offline_cycle_${currentCycle}_access_token`] = body.access_token;
      if (body.refresh_token) {
        $global.offline_exchanged_refresh_token = body.refresh_token;
      }
      
      // Validate the token
      const payload = validateJWT(body.access_token);
      
      // Check if we need more cycles
      if (currentCycle < remainingCycles) {
        $global.refresh_cycle_current = currentCycle + 1;
      }
    } else {
      console.error(`Refresh cycle failed: ${JSON.stringify(body)}`);
      throw new Error(`Refresh cycle failed`);
    }
  } else {
    handleError("Refresh cycle failed", response);
  }
}}

###
### Offline Token Introspection
# @name introspect_offline_token
# @ref exchange_to_offline_token
# Introspects an offline token to check its properties
POST {{keycloak}}/realms/{{realm}}/protocol/openid-connect/token/introspect
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}

token={{$global.offline_exchanged_access_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    
    if (!body.active) {
      console.error("Offline token is inactive");
    }
    
    // Check offline token specific properties
    if (body.scope && !body.scope.includes('offline_access')) {
      console.error("Introspection missing offline_access scope");
    }
  } else {
    handleError("Offline token introspection failed", response);
  }
}}

###
### Offline Token with Different Scopes
# @name offline_token_limited_scopes
# @ref get_offline_user_token
# Creates an offline token with limited scopes
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.offline_user_access_token}}&requested_token_type=urn:ietf:params:oauth:token-type:refresh_token&scope=offline_access

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body.access_token && body.refresh_token) {
      $global.offline_limited_access_token = body.access_token;
      $global.offline_limited_refresh_token = body.refresh_token;
      
      // Validate the limited offline token
      const payload = validateJWT(body.access_token);
      
      // Verify limited scopes
      if (payload.scope) {
        const scopes = payload.scope.split(' ');
        
        if (!scopes.includes('offline_access') || scopes.length !== 1) {
          console.error("Limited offline token has unexpected scopes");
        }
      }
    } else {
      console.error(`Limited offline tokens not found: ${JSON.stringify(body)}`);
      throw new Error(`Limited offline tokens not found`);
    }
  } else {
    handleError("Limited offline token creation failed", response);
  }
}}

###
### Offline Token Expiration Test
# @name offline_token_expiration_test
# @ref exchange_to_offline_token
# Tests offline token expiration behavior
{{
  // Get current offline token
  const offlineToken = $global.offline_exchanged_access_token;
  
  if (!offlineToken) {
    console.error("No offline token available for expiration testing");
    throw new Error("Run the previous steps to get an offline token");
  }
  
  // Decode and check expiration
  const payload = validateJWT(offlineToken);
  const now = Math.floor(Date.now() / 1000);
  const timeToExpiry = payload.exp - now;
  
  if (timeToExpiry <= 0) {
    console.error("Token is expired");
  }
  
  // Check if refresh token is available
  const refreshToken = $global.offline_exchanged_refresh_token;
  if (!refreshToken) {
    console.error("No refresh token available");
  }
}}

###
### Revoke Offline Token
# @name revoke_offline_token
# @ref exchange_to_offline_token
# Revokes an offline token (logout)
POST {{keycloak}}/realms/{{realm}}/protocol/openid-connect/logout
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}

refresh_token={{$global.offline_exchanged_refresh_token}}

{{@response
  if (response.statusCode === 204) {
    // Offline token revoked successfully
    
    // Clear the revoked tokens
    $global.offline_exchanged_access_token = null;
    $global.offline_exchanged_refresh_token = null;
  } else {
    handleError("Revoke failed", response);
  }
}}

###
### Test Revoked Token Usage
# @name test_revoked_token_usage
# @ref offline_token_limited_scopes
# Tests that revoked offline tokens cannot be used
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}

grant_type=refresh_token&refresh_token={{$global.offline_limited_refresh_token}}

{{@response
  if (response.statusCode === 400) {
    // Revoked token correctly rejected
    const body = response.parsedBody || response.body;
  } else if (response.statusCode === 200) {
    console.error("Revoked token unexpectedly accepted");
  } else {
    const body = response.parsedBody || response.body;
    console.error(`Unexpected response: Status: ${response.statusCode}\n${JSON.stringify(body)}`);
  }
}}

###
### Offline Token Performance Test
# @name offline_token_performance
# @ref get_offline_user_token
# Tests the performance of offline token operations
{{
  const startTime = Date.now();
  $global.offline_perf_start_time = startTime;
}}

POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.offline_user_access_token}}&requested_token_type=urn:ietf:params:oauth:token-type:refresh_token&scope=offline_access scope_token_power_user

{{@response
  const endTime = Date.now();
  const startTime = $global.offline_perf_start_time;
  const duration = endTime - startTime;
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    
    if (duration > 2000) {
      console.error(`Performance: Slow (${duration}ms > 2s)`);
    }
    
    // Store for refresh performance test
    if (body.refresh_token) {
      $global.offline_perf_refresh_token = body.refresh_token;
    }
  } else {
    const body = response.parsedBody || response.body;
    console.error(`Offline token exchange failed in ${duration}ms: Status: ${response.statusCode}\n${JSON.stringify(body)}`);
  }
}}

###
### Refresh Token Performance Test
# @name refresh_token_performance
# @ref offline_token_performance
# Tests the performance of refresh token operations
{{
  const startTime = Date.now();
  $global.refresh_perf_start_time = startTime;
}}

POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.offline_resource_client_id}}:{{$global.offline_resource_client_secret}}

grant_type=refresh_token&refresh_token={{$global.offline_perf_refresh_token}}

{{@response
  const endTime = Date.now();
  const startTime = $global.refresh_perf_start_time;
  const duration = endTime - startTime;
  
  if (response.statusCode === 200) {
    if (duration > 1000) {
      console.error(`Performance: Slow (${duration}ms > 1s)`);
    }
  } else {
    const body = response.parsedBody || response.body;
    console.error(`Refresh token operation failed in ${duration}ms: Status: ${response.statusCode}\n${JSON.stringify(body)}`);
  }
}}

###
### Validate Offline Token Configuration
# @name validate_offline_token_setup
# Validates the offline token configuration and setup
{{
  // Check required variables
  const requiredVars = [
    'offline_resource_client_id',
    'offline_resource_client_secret',
    'offline_service_token',
    'offline_user_access_token'
  ];
  
  const missingVars = requiredVars.filter(varName => !$global[varName]);
  
  if (missingVars.length > 0) {
    console.error(`Missing required variables: ${missingVars.join(', ')}`);
    throw new Error("Please run the setup steps first");
  }
  
  // Validate resource client configuration
  const resourceClientId = $global.offline_resource_client_id;
  if (!resourceClientId.startsWith('test-resource-')) {
    console.error("Resource client does not have expected test prefix");
  }
  
  // Validate tokens are not expired
  try {
    const serviceToken = $global.offline_service_token;
    validateJWT(serviceToken);
    
    const userToken = $global.offline_user_access_token;
    validateJWT(userToken);
    
    // Check offline tokens if they exist
    const offlineToken = $global.offline_exchanged_access_token;
    if (offlineToken) {
      validateJWT(offlineToken);
    }
    
  } catch (error) {
    console.error(`Token validation failed: ${error.message}`);
  }
}}

###
### Cleanup - Clear Offline Token Variables
# @name cleanup_offline_tokens
# Clears all offline token related variables
{{
  const variablesToClear = [
    'offline_resource_client_id',
    'offline_resource_client_secret',
    'offline_service_token',
    'offline_user_access_token',
    'offline_user_refresh_token',
    'offline_exchanged_access_token',
    'offline_exchanged_refresh_token',
    'offline_refreshed_1_access_token',
    'offline_limited_access_token',
    'offline_limited_refresh_token',
    'offline_perf_refresh_token',
    'refresh_cycles_remaining',
    'refresh_cycle_current',
    'offline_perf_start_time',
    'refresh_perf_start_time'
  ];
  
  // Clear cycle tokens
  for (let i = 1; i <= 3; i++) {
    variablesToClear.push(`offline_cycle_${i}_access_token`);
  }
  
  variablesToClear.forEach(varName => {
    if ($global[varName]) {
      $global[varName] = null;
    }
  });
}} 