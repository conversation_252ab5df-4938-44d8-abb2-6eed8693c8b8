### App Client Management
### This httpbook covers the complete lifecycle of app clients (public clients)
### Uses HTTPyac native features for variable management and response handling

###
# Import shared variables
# @import ./common.http

# Variables imported from common.http via @import

# Functions imported from common.http via @import

###
### Get Dev Console User Token
# @name get_dev_console_token
# Prerequisites: client-bodhi-dev-console with directAccessGrantsEnabled=true, user with access
# Obtains a user token for the dev console client to create app clients
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{dev_console_client_id}}:{{dev_console_client_secret}}

grant_type=password&username={{user_offline_username}}&password={{user_offline_password}}&scope=openid email profile roles

{{@response
  if (response.statusCode === 200) {
    $global.dev_console_token = extractToken(response, 'access_token');
    
    // Validate token claims
    const userInfo = extractUserInfo($global.dev_console_token);
    
    // Verify it's issued for the correct client
    if (userInfo.azp !== dev_console_client_id) {
      console.error("⚠ Token not issued for dev console client");
    }
  } else {
    handleError("Dev console token request failed", response);
  }
}}

###
### Create App Client
# @name create_app_client
# @ref get_dev_console_token
# Prerequisites: Dev console service account token
# Creates a new app client (public client with no client secret)
POST {{bodhi_apps_endpoint}}?live_test=true
Authorization: Bearer {{$global.dev_console_token}}
Content-Type: {{json_content_type}}

{
  "name": "{{app_name}}",
  "description": "{{app_description}}",
  "redirect_uris": ["{{app_redirect_uri}}"]
}

{{@response
  if (response.statusCode === 201) {
    const credentials = extractClientCredentials(response);
    $global.app_client_id = credentials.client_id;
    $global.app_client_secret = credentials.client_secret;
    
    // Validate client ID has app prefix
    if (!credentials.client_id.startsWith("app-")) {
      console.error("Client ID does not have expected 'app-' prefix");
    }
  } else {
    handleError("App client creation failed", response);
  }
}}

###
### Authorization Code Flow - Step 1 (Generate Auth URL)
# @name app_auth_url
# @ref create_app_client
# Prerequisites: App client created
# Generates the authorization URL for the app client
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth?response_type=code&client_id={{$global.app_client_id}}&redirect_uri={{app_redirect_uri}}&scope=openid email profile roles&state={{generateRandomState()}}

{{@response
  console.log("=== Authorization Code Flow ===");
  console.log("Step 1: Visit this URL to authorize the app:");
  console.log(`${request.url}`);
  console.log("");
  console.log("After authorization, you'll be redirected to:");
  console.log(`${app_redirect_uri}?code=AUTHORIZATION_CODE&state=STATE`);
  console.log("");
  console.log("Extract the 'code' parameter and use it in the next step.");
}}

###
### Authorization Code Exchange
# @name app_auth_code_exchange
# @ref create_app_client
# Prerequisites: App client created, authorization code from redirect URL
# Exchanges the authorization code for tokens
# NOTE: Set the auth_code variable with the code from the redirect URL
@auth_code=0a439528-6ebe-400f-9f00-17cfe6072e9b.ca3765ec-4cfc-4571-9450-ea718295b526.17d88f3e-b46c-4932-af25-8fb69da107e4

POST {{token_endpoint}}
Content-Type: {{form_content_type}}

grant_type=authorization_code&client_id={{$global.app_client_id}}&code={{auth_code}}&redirect_uri={{app_redirect_uri}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body && body.access_token) {
      $global.app_user_token = body.access_token;
      if (body.refresh_token) {
        $global.app_refresh_token = body.refresh_token;
      }
      
      // Validate token claims
      const userInfo = extractUserInfo($global.app_user_token);
      
      // Check for admin role in app client
      if (userInfo.resource_access && userInfo.resource_access[$global.app_client_id]) {
        const roles = userInfo.resource_access[$global.app_client_id].roles || [];
        
        if (!roles.includes("admin")) {
          console.error("⚠ User does not have admin role in app client");
        }
      }
    } else {
      console.error(`Access token not found in response: ${JSON.stringify(body)}`);
      throw new Error(`Access token not found in response`);
    }
  } else {
    handleError("Authorization code exchange failed", response);
  }
}}

###
### Refresh App User Token
# @name refresh_app_token
# @ref create_app_client
# @ref app_auth_code_exchange
# Prerequisites: App client created, refresh token from previous login
# Refreshes the app user token using the refresh token
POST {{token_endpoint}}
Content-Type: {{form_content_type}}

grant_type=refresh_token&client_id={{$global.app_client_id}}&refresh_token={{$global.app_refresh_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body && body.access_token) {
      $global.app_user_token = body.access_token;
      if (body.refresh_token) {
        $global.app_refresh_token = body.refresh_token;
      }
      
      // Validate refreshed token
      const userInfo = extractUserInfo($global.app_user_token);
    } else {
      console.error(`Access token not found in response: ${JSON.stringify(body)}`);
      throw new Error(`Access token not found in response`);
    }
  } else {
    handleError("Token refresh failed", response);
  }
}}

###
### Get User Info for App User
# @name app_user_info
# @ref app_auth_code_exchange
# Prerequisites: App user token obtained
# Retrieves user information using the app user token
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: Bearer {{$global.app_user_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.app_user_info = body;
  } else {
    handleError("User info request failed", response);
  }
}}

###
### Token Introspection for App Token
# @name introspect_app_token
# @ref app_auth_code_exchange
# Prerequisites: App user token obtained
# Introspects the app user token to check its validity and claims
POST {{keycloak}}/realms/{{realm}}/protocol/openid-connect/token/introspect
Content-Type: {{form_content_type}}

token={{$global.app_user_token}}&client_id={{$global.app_client_id}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    
    if (!body.active) {
      console.error("⚠ Token is inactive");
    }
  } else {
    handleError("Token introspection failed", response);
  }
}}

###
### PKCE Flow - Step 1 (Generate Code Challenge)
# @name pkce_generate_challenge
# Generates PKCE code verifier and challenge for secure authorization
{{
  // Generate PKCE parameters
  const crypto = require('crypto');
  
  // Generate code verifier (43-128 characters)
  const codeVerifier = crypto.randomBytes(32).toString('base64url');
  
  // Generate code challenge (SHA256 hash of verifier, base64url encoded)
  const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url');
  
  $global.pkce_code_verifier = codeVerifier;
  $global.pkce_code_challenge = codeChallenge;
  
  // Generate authorization URL with PKCE
  const state = generateRandomState();
  $global.pkce_state = state;
  
  const authUrl = `${keycloak}/realms/${realm}/protocol/openid-connect/auth?response_type=code&client_id=${$global.app_client_id}&redirect_uri=${encodeURIComponent(app_redirect_uri)}&scope=openid%20email%20profile%20roles&state=${state}&code_challenge=${codeChallenge}&code_challenge_method=S256`;
}}

###
### PKCE Flow - Step 2 (Exchange Code with Verifier)
# @name pkce_code_exchange
# @ref create_app_client
# @ref pkce_generate_challenge
# Prerequisites: App client created, PKCE challenge generated, authorization code from redirect URL
# Exchanges the authorization code using PKCE code verifier
# NOTE: Set the pkce_auth_code variable with the code from the redirect URL
@pkce_auth_code=REPLACE_WITH_ACTUAL_CODE

POST {{token_endpoint}}
Content-Type: {{form_content_type}}

grant_type=authorization_code&client_id={{$global.app_client_id}}&code={{pkce_auth_code}}&redirect_uri={{app_redirect_uri}}&code_verifier={{$global.pkce_code_verifier}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body && body.access_token) {
      $global.pkce_user_token = body.access_token;
      if (body.refresh_token) {
        $global.pkce_refresh_token = body.refresh_token;
      }
      
      // Validate token claims
      const userInfo = extractUserInfo($global.pkce_user_token);
      
      // Verify state matches
      if (userInfo.state && userInfo.state !== $global.pkce_state) {
        console.error("⚠ State parameter mismatch or missing");
      }
    } else {
      console.error(`Access token not found in response: ${JSON.stringify(body)}`);
      throw new Error(`Access token not found in response`);
    }
  } else {
    handleError("PKCE code exchange failed", response);
  }
}}

###
### App Client Logout
# @name app_logout
# @ref create_app_client
# @ref app_auth_code_exchange
# Prerequisites: App client created, refresh token from user login
# Logs out the app user (revokes refresh token)
POST {{keycloak}}/realms/{{realm}}/protocol/openid-connect/logout
Content-Type: {{form_content_type}}

client_id={{$global.app_client_id}}&refresh_token={{$global.app_refresh_token}}

{{@response
  if (response.statusCode === 204) {
    delete $global.app_user_token;
    delete $global.app_refresh_token;
  } else {
    const body = response.parsedBody || response.body;
    console.error(`Logout failed: ${response.statusCode} - ${JSON.stringify(body)}`);
  }
}}

###
### App Client with Consent Flow
# @name app_consent_flow
# @ref create_app_client
# Prerequisites: App client created
# Demonstrates the consent screen flow for app clients
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth?response_type=code&client_id={{$global.app_client_id}}&redirect_uri={{app_redirect_uri}}&scope=openid email profile roles offline_access&state={{generateRandomState()}}&prompt=consent

{{@response
  console.log("=== Consent Flow ===");
  console.log("This URL will show the consent screen:");
  console.log(`${request.url}`);
  console.log("");
  console.log("The user will see what permissions the app is requesting:");
  console.log("- Access to user profile");
  console.log("- Access to email address");
  console.log("- Access to user roles");
  console.log("- Offline access (refresh token)");
  console.log("");
  console.log("After consent, extract the 'code' parameter from the redirect URL");
}}

###
### Validate App Client Configuration
# @name validate_app_client
# @ref create_app_client
# Prerequisites: App client created
# Validates the app client configuration and setup
{{
  // Check if we have the necessary variables
  const requiredVars = [
    'app_client_id',
    'dev_console_token'
  ];
  
  const missingVars = requiredVars.filter(varName => !$global[varName]);
  
  if (missingVars.length > 0) {
    console.error(`Missing required variables: ${missingVars.join(', ')}`);
    throw new Error("Please run the previous steps to set up the app client");
  }
  
  // Validate client ID format
  const clientId = $global.app_client_id;
  if (!clientId.startsWith('app-') && !clientId.startsWith('test-app-')) {
    console.error("⚠ Client ID does not have expected prefix");
  }
  
  // Validate dev console token
  try {
    const devToken = $global.dev_console_token;
    const userInfo = extractUserInfo(devToken);
    
    if (userInfo.azp !== dev_console_client_id) {
      console.error("⚠ Dev console token not issued for correct client");
    }
  } catch (error) {
    console.error(`⚠ Dev console token validation failed: ${error.message}`);
  }
  
  // Check if user token exists
  const userToken = $global.app_user_token;
  if (userToken) {
    try {
      extractUserInfo(userToken);
    } catch (error) {
      console.error(`⚠ App user token validation failed: ${error.message}`);
    }
  }
}}

###
### Test App Client Scopes
# @name test_app_scopes
# @ref create_app_client
# Prerequisites: App client created
# Tests different scope combinations for app clients
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth?response_type=code&client_id={{$global.app_client_id}}&redirect_uri={{app_redirect_uri}}&scope=openid email profile roles scope_user_user&state={{generateRandomState()}}

{{@response
  console.log("=== Scope Testing ===");
  console.log("Testing app client with custom scopes:");
  console.log("- openid: OpenID Connect identity");
  console.log("- email: Email address");
  console.log("- profile: User profile information");
  console.log("- roles: User roles");
  console.log("- scope_user_user: Custom user scope");
  console.log("");
  console.log("Authorization URL:");
  console.log(`${request.url}`);
  console.log("");
  console.log("After authorization, the token will contain these scopes");
}}

###
### App Client Token with Custom Scopes
# @name app_token_custom_scopes
# @ref create_app_client
# Prerequisites: App client created, authorization code from redirect URL
# Exchanges code for token with custom scopes
# NOTE: Set the custom_scope_code variable with the code from the redirect URL
@custom_scope_code=REPLACE_WITH_ACTUAL_CODE

POST {{token_endpoint}}
Content-Type: {{form_content_type}}

grant_type=authorization_code&client_id={{$global.app_client_id}}&code={{custom_scope_code}}&redirect_uri={{app_redirect_uri}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body && body.access_token) {
      $global.app_custom_token = body.access_token;
      
      // Validate token claims and scopes
      const userInfo = extractUserInfo($global.app_custom_token);
      
      // Check for custom scopes
      if (userInfo.scope) {
        const scopes = userInfo.scope.split(' ');
        
        if (!scopes.includes('scope_user_user')) {
          console.error("⚠ Custom scope 'scope_user_user' missing");
        }
      }
    } else {
      console.error(`Access token not found in response: ${JSON.stringify(body)}`);
      throw new Error(`Access token not found in response`);
    }
  } else {
    handleError("Custom scope token exchange failed", response);
  }
}}

###
### Cleanup - Clear App Client Variables
# @name cleanup_app_client
# Clears all app client related variables
{{
  const variablesToClear = [
    'app_client_id',
    'test_app_client_id',
    'app_user_token',
    'app_refresh_token',
    'app_user_info',
    'pkce_code_verifier',
    'pkce_code_challenge',
    'pkce_state',
    'pkce_user_token',
    'pkce_refresh_token',
    'app_custom_token'
  ];
  
  variablesToClear.forEach(varName => {
    if ($global[varName]) {
      delete $global[varName];
    }
  });
}} 