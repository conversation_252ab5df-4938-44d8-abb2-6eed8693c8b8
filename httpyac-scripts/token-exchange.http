### Token Exchange Testing
### This httpbook tests the token exchange functionality between app and resource clients
### Uses HTTPyac native features for proper variable management and response handling

###
# Import shared variables
# @import ./common.http

# Variables imported from common.http via @import

# Functions imported from common.http via @import

###
### Get Dev Console User Token
# @name get_dev_console_token
# Prerequisites: client-bodhi-dev-console with directAccessGrantsEnabled=true, <EMAIL> exists
# Obtains a user token for the dev console client to create app clients
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{dev_console_client_id}}:{{dev_console_client_secret}}

grant_type=password&username={{user_regular}}&password={{user_regular_password}}&scope=openid email profile roles

{{@response
  if (response.statusCode === 200) {
    $global.dev_console_token = extractToken(response, 'access_token');
    
    // Validate token claims
    const userInfo = extractUserInfo($global.dev_console_token);
  } else {
    handleError("Dev console token request failed", response);
  }
}}

###
### Create App Client
# @name create_app_client
# @ref get_dev_console_token
# Prerequisites: Dev console user token
# Creates a new app client (public client)
POST {{bodhi_apps_endpoint}}?live_test=true
Authorization: Bearer {{$global.dev_console_token}}
Content-Type: {{json_content_type}}

{
  "name": "test-app-client",
  "description": "Test app client",
  "redirect_uris": ["{{app_redirect_uri}}"]
}

{{@response
  if (response.statusCode === 201) {
    const credentials = extractClientCredentials(response);
    $global.app_client_id = credentials.client_id;
    $global.app_client_secret = credentials.client_secret;
  } else {
    handleError("App client creation failed", response);
  }
}}

###
### Get App User Token via OAuth2 Authorization Code Flow
# @name get_app_user_token
# @ref create_app_client
# HTTPyac will automatically:
# 1. Open browser to authorization endpoint
# 2. User grants permission  
# 3. HTTPyac receives callback with authorization code
# 4. Automatically exchanges code for access token
# 5. Stores token for subsequent requests
# OAuth2 variables for HTTPyac (set after app creation)
@oauth2_clientId={{$global.app_client_id}}
@oauth2_clientSecret=""
@oauth2_authorizationEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth
@oauth2_tokenEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token
@oauth2_redirectUri={{app_redirect_uri}}
@oauth2_scope=openid email profile roles scope_user_power_user
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: oauth2 authorization_code

{{@response
  if (response.statusCode === 200) {
    const userInfo = response.parsedBody || response.body;
    $global.app_user_token = oauth2Session?.accessToken;
    if (!$global.app_user_token) {
      handleError("OAuth2 access token not available", response);
    }
    const tokenUserInfo = extractUserInfo($global.app_user_token);
    if (tokenUserInfo.resource_access && tokenUserInfo.resource_access[$global.app_client_id]) {
      const roles = tokenUserInfo.resource_access[$global.app_client_id].roles || [];
      if (!roles.includes("admin")) {
        console.error("⚠ User does not have admin role in app client");
      }
    }
  } else {
    handleError("OAuth2 authorization code flow failed", response);
  }
}}

###
### Create Resource Client
# @name create_resource_client
# Prerequisites: Dev console service account token
# Creates a new resource server client
POST {{bodhi_resources_endpoint}}?live_test=true
Content-Type: {{json_content_type}}

{
  "name": "Test Resource Server",
  "description": "Test resource client",
  "redirect_uris": ["{{resource_redirect_uri}}"]
}

{{@response
  if (response.statusCode === 201) {
    const credentials = extractClientCredentials(response);
    $global.resource_client_id = credentials.client_id;
    $global.resource_client_secret = credentials.client_secret;
  } else {
    handleError("Resource client creation failed", response);
  }
}}

###
### Get Resource Service Token
# @name get_resource_service_token
# @ref create_resource_client
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=client_credentials&scope=service_account

{{@response
  if (response.statusCode === 200) {
    $global.resource_service_token = extractToken(response, 'access_token');
  } else {
    handleError("Resource service token request failed", response);
  }
}}

###
### Token Exchange
# @name token_exchange
# @ref get_app_user_token
# @ref get_resource_service_token
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Bearer {{$global.resource_service_token}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&client_id={{$global.app_client_id}}&audience={{$global.resource_client_id}}&scope=openid email profile roles scope_user_power_user

{{@response
  if (response.statusCode === 200) {
    $global.exchanged_token = extractToken(response, 'access_token');
    
    // Validate exchanged token
    const userInfo = extractUserInfo($global.exchanged_token);
    
    if (Array.isArray(userInfo.aud) && !userInfo.aud.includes($global.resource_client_id)) {
      console.error("⚠ Token exchange failed - incorrect audience");
    } else if (userInfo.aud !== $global.resource_client_id) {
      console.error("⚠ Token exchange failed - incorrect audience");
    }
  } else {
    handleError("Token exchange failed", response);
  }
}}

###
### Token Introspection
# @name token_introspection
# @ref token_exchange
POST {{token_endpoint}}/introspect
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

token={{$global.exchanged_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    
    if (!body.active || body.client_id !== $global.app_client_id) {
      console.error("⚠ Token introspection failed");
    }
  } else {
    handleError("Token introspection failed", response);
  }
}}

###
### Test Invalid Audience
# @name invalid_audience
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Bearer {{$global.resource_service_token}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&client_id={{$global.app_client_id}}&audience=invalid-client-id

{{@response
  if (response.statusCode === 400 || response.statusCode === 401) {
    // Invalid audience correctly rejected
  } else if (response.statusCode === 200) {
    console.error("⚠ Invalid audience was accepted - this should not happen");
  } else {
    const body = response.parsedBody || response.body;
    console.error(`Unexpected response: Status: ${response.statusCode}\n${JSON.stringify(body)}`);
  }
}}

###
### Complete Token Exchange Workflow
# @name complete_workflow
# Demonstrates the complete token exchange workflow
{{
  async function completeWorkflow() {
    const steps = [
      "get_dev_console_token",
      "create_app_client",
      "get_dev_console_service_token",
      "create_resource_client", 
      "get_app_user_token",
      "get_resource_service_token",
      "test_token_exchange",
      "test_invalid_audience",
      "test_token_introspection"
    ];
    
    return { workflow: "ready", steps };
  }
  
  exports.workflow_info = completeWorkflow();
}}

###
### Test Error Scenarios
# @name test_error_scenarios
# @ref get_app_user_token
# Prerequisites: App user token obtained
# Tests various error conditions
{{
  const errorTests = [
    {
      name: "Missing subject token",
      params: "subject_token_type=urn:ietf:params:oauth:token-type:access_token&audience=" + $global.resource_client_id
    },
    {
      name: "Invalid subject token type",
      params: "subject_token=" + $global.app_user_token + "&subject_token_type=invalid-type&audience=" + $global.resource_client_id
    },
    {
      name: "Missing audience",
      params: "subject_token=" + $global.app_user_token + "&subject_token_type=urn:ietf:params:oauth:token-type:access_token"
    }
  ];
  
  async function testErrorScenarios() {
    const results = [];
    
    for (const test of errorTests) {
      try {
        const response = await fetch(token_endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: test.params
        });
        
        results.push({
          test: test.name,
          status: response.statusCode,
          success: response.statusCode >= 400 // Error scenarios should return 4xx
        });
      } catch (error) {
        results.push({
          test: test.name,
          error: error.message
        });
      }
    }
    
    return results;
  }
  
  exports.error_test_promise = testErrorScenarios();
}}

###
### Cleanup - Clear Token Exchange Variables
# @name cleanup_token_exchange
# Clears all token exchange related variables
{{
  console.log("=== Cleanup Token Exchange Variables ===");
  
  const variablesToClear = [
    'dev_console_token',
    'dev_console_service_token',
    'app_client_id',
    'app_client_secret',
    'resource_client_id',
    'resource_client_secret',
    'app_user_token',
    'resource_service_token',
    'exchanged_token'
  ];
  
  variablesToClear.forEach(varName => {
    if ($global[varName]) {
      delete $global[varName];
    }
  });
  
  console.log("Token exchange variables cleared");
}} 