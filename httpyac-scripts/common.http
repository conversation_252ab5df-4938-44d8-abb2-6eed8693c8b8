###
# Global Variables and Configuration
# HTTPyac environment system:
# - Global variables are in env/.env (shared across all environments)
# - Environment-specific variables are in env/{environment}.env
# Usage: httpyac send script.http --env dev (loads env/dev.env + env/.env)
#        httpyac send script.http --env main (loads env/main.env + env/.env)
#        httpyac send script.http (loads env/local.env + env/.env by default)

# Core Configuration - loaded from environment files (required)
@keycloak={{$dotenv KEYCLOAK_URL}}
@realm={{$dotenv REALM}}
@provider_id={{$dotenv PROVIDER_ID}}

# Admin CLI Configuration - loaded from environment files (required)
@admin_cli_client_id={{$dotenv ADMIN_CLI_CLIENT_ID}}
@admin_cli_username={{$dotenv ADMIN_CLI_USERNAME}}
@admin_cli_password={{$dotenv ADMIN_CLI_PASSWORD}}

# Dev Console Client - loaded from environment files (required)
@dev_console_client_id={{$dotenv CLIENT_BODHI_DEV_CONSOLE}}
@dev_console_client_secret={{$dotenv CLIENT_BODHI_DEV_CONSOLE_SECRET}}

# Test Users - loaded from environment files (required)
@user_resource_admin={{$dotenv USER_RESOURCE_ADMIN}}
@user_resource_admin_password={{$dotenv USER_RESOURCE_ADMIN_PASSWORD}}
@user_power_user={{$dotenv USER_POWER_USER}}
@user_power_user_password={{$dotenv USER_POWER_USER_PASSWORD}}
@user_manager={{$dotenv USER_MANAGER}}
@user_manager_password={{$dotenv USER_MANAGER_PASSWORD}}
@user_regular={{$dotenv USER_REGULAR}}
@user_regular_password={{$dotenv USER_REGULAR_PASSWORD}}

# App Client Test User - loaded from environment files (required)
@user_app_client={{$dotenv USER_APP_CLIENT}}
@user_app_client_password={{$dotenv USER_APP_CLIENT_PASSWORD}}

# Common Endpoints - dynamically constructed from base variables
@token_endpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token
@admin_endpoint={{keycloak}}/admin/realms/{{realm}}
@bodhi_clients_endpoint={{keycloak}}/realms/{{realm}}/{{provider_id}}/clients
@bodhi_resources_endpoint={{keycloak}}/realms/{{realm}}/{{provider_id}}/resources
@make_admin_endpoint={{keycloak}}/realms/{{realm}}/{{provider_id}}/resources/make-resource-admin
@has_admin_endpoint={{keycloak}}/realms/{{realm}}/{{provider_id}}/resources/has-resource-admin
@add_to_group_endpoint={{keycloak}}/realms/{{realm}}/{{provider_id}}/resources/add-user-to-group
@bodhi_apps_endpoint={{keycloak}}/realms/{{realm}}/{{provider_id}}/apps
@logout_endpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/logout

# Master realm admin endpoints
@master_token_endpoint={{keycloak}}/realms/master/protocol/openid-connect/token
@admin_users_endpoint={{keycloak}}/admin/realms/{{realm}}/users
@admin_groups_endpoint={{keycloak}}/admin/realms/{{realm}}/groups
@admin_clients_endpoint={{keycloak}}/admin/realms/{{realm}}/clients

# Common Content Types
@json_content_type=application/json
@form_content_type=application/x-www-form-urlencoded

# Common redirect URIs
@app_redirect_uri=http://localhost:3000/callback
@resource_redirect_uri=http://localhost:8090/callback

# Master realm admin credentials (using same as admin_cli for simplicity)
@master_admin_username={{admin_cli_username}}
@master_admin_password={{admin_cli_password}}

# Offline Token Variables - loaded from environment files (required)
@user_offline_username={{$dotenv USER_OFFLINE_USERNAME}}
@user_offline_password={{$dotenv USER_OFFLINE_PASSWORD}}

# App Client Variables - loaded from environment files (required)
@app_name={{$dotenv APP_NAME}}
@app_description={{$dotenv APP_DESCRIPTION}}

{{
  // Global utility functions
  function extractToken(response, tokenType = 'access_token') {
    const body = response.parsedBody || response.body;
    if (!body || !body[tokenType]) {
      throw new Error(`${tokenType} not found in response: ${JSON.stringify(body)}`);
    }
    return body[tokenType];
  }
  
  function handleError(messagePrefix, response) {
    const body = response.parsedBody || response.body;
    const err = `${messagePrefix}: Status: ${response.statusCode}\n${JSON.stringify(body)}`;
    console.error(err);
    throw new Error(err);
  }
  
  function extractClientCredentials(response) {
    const body = response.parsedBody || response.body;
    if (!body || !body.client_id) {
      throw new Error(`Client credentials not found in response: ${JSON.stringify(body)}`);
    }
    return {
      client_id: body.client_id,
      client_secret: body.client_secret || null
    };
  }
  
  function extractUserInfo(token) {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }
      return JSON.parse(atob(parts[1]));
    } catch (error) {
      throw new Error(`JWT parsing failed: ${error.message}`);
    }
  }
  
  function handleResponse(response, successStatus = 200, operation = 'Operation') {
    const statusCode = response.statusCode || response.status;
    const body = response.parsedBody || response.body;
    
    if (statusCode === successStatus || (Array.isArray(successStatus) && successStatus.includes(statusCode))) {
      return body;
    } else {
      console.error(`${operation} failed: ${statusCode} - ${JSON.stringify(body)}`);
      throw new Error(`${operation} failed: ${statusCode}`);
    }
  }
  
  // UUID generation functions
  function generateUUID() {
    const crypto = require('crypto');
    return crypto.randomUUID();
  }
  
  function generateRandomState() {
    const crypto = require('crypto');
    return crypto.randomUUID();
  }
  
  // JWT validation function (for offline-tokens.http compatibility)
  function validateJWT(token) {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }
      
      const payload = JSON.parse(atob(parts[1]));
      return payload;
    } catch (error) {
      throw new Error(`JWT validation failed: ${error.message}`);
    }
  }
  
  // Realm-specific utility functions
  function extractUserId(response) {
    const body = response.parsedBody || response.body;
    if (Array.isArray(body) && body.length > 0) {
      return body[0].id;
    }
    throw new Error(`User ID not found in response: ${JSON.stringify(body)}`);
  }
  
  function extractGroupId(response) {
    const body = response.parsedBody || response.body;
    if (Array.isArray(body) && body.length > 0) {
      return body[0].id;
    }
    throw new Error(`Group ID not found in response: ${JSON.stringify(body)}`);
  }
  
  // Global exports
  exports.extractToken = extractToken;
  exports.extractClientCredentials = extractClientCredentials;
  exports.extractUserInfo = extractUserInfo;
  exports.handleResponse = handleResponse;
  exports.generateUUID = generateUUID;
  exports.generateRandomState = generateRandomState;
  exports.validateJWT = validateJWT;
  exports.extractUserId = extractUserId;
  exports.extractGroupId = extractGroupId;
  exports.handleError = handleError;
}}

###
### Get Admin CLI Token
# @name get_admin_cli_token
# Gets an admin token using admin-cli client
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{admin_cli_client_id}}:{{admin_cli_client_secret}}

grant_type=client_credentials

{{@response
  if (response.statusCode === 200) {
    $global.admin_cli_token = extractToken(response, 'access_token');
    const body = response.parsedBody || response.body;
    if (body.refresh_token) {
      $global.admin_cli_refresh_token = body.refresh_token;
    }
  } else {
    handleError("Admin CLI token request failed", response);
  }
}}

###
### Get Dev Console Token
# @name get_dev_console_token
# Gets a service account token for the dev console client
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{dev_console_client_id}}:{{dev_console_client_secret}}

grant_type=client_credentials

{{@response
  if (response.statusCode === 200) {
    $global.dev_console_token = extractToken(response, 'access_token');
  } else {
    handleError("Dev console token request failed", response);
  }
}}

###
### Test Token Validation
# @name test_token_validation
# @ref get_admin_cli_token
# Tests JWT token parsing and validation
{{
  if ($global.admin_cli_token) {
    try {
      const userInfo = extractUserInfo($global.admin_cli_token);
      // Token validation successful - no logging needed for success
    } catch (error) {
      console.error(`Token validation failed: ${error.message}`);
    }
  } else {
    console.error("No admin token available for validation");
  }
}}

###
### Environment Test
# @name test_utilities
# Outputs environment variables and tests admin token


# Test admin token request
POST {{token_endpoint}}
Content-Type: {{form_content_type}}

grant_type=password&client_id={{admin_cli_client_id}}&username={{admin_cli_username}}&password={{admin_cli_password}}

{{@response
  // Output the resolved template values by checking the actual request
  const actualUrl = request.url;
  const actualBody = request.body;
  
  // Extract keycloak URL from the request URL
  const keycloakUrl = actualUrl.match(/^https?:\/\/[^\/]+/)?.[0] || 'NOT_SET';
  
  // Extract username from request body
  const usernameMatch = actualBody.match(/username=([^&]+)/);
  const adminUsername = usernameMatch ? decodeURIComponent(usernameMatch[1]) : 'NOT_SET';
  
  console.log(`KEYCLOAK_URL=${keycloakUrl}`);
  console.log(`ADMIN_USERNAME=${adminUsername}`);
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    console.log(`ADMIN_TOKEN_SUCCESS=true`);
    $global.test_admin_token = body.access_token;
  } else {
    console.log(`ADMIN_TOKEN_SUCCESS=false`);
    console.log(`ADMIN_TOKEN_ERROR=${response.statusCode}`);
  }
}} 
