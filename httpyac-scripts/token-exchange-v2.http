### Token Exchange V2 vs V1 Comparison
### This httpbook demonstrates the new Keycloak v26.2+ token exchange v2 mechanism
### and compares it with the legacy v1 implementation
### Uses HTTPyac native features for proper variable management and response handling

###
# Import shared variables
# @import ./common.http

# Variables imported from common.http via @import

# Functions imported from common.http via @import

###
### OVERVIEW: Token Exchange V1 vs V2 Differences
###
### Standard Token Exchange V2 (officially supported in Keycloak 26.2+):
### - Fully RFC 8693 compliant
### - Enabled by default (`token-exchange-standard:v2`)
### - Only supports internal-to-internal token exchange
### - Requires "Standard token exchange" switch enabled on confidential clients
### - Uses standard OAuth scope parameter behavior (optional scopes of requester client)
### - Supports multiple audience values for downscoping
### - No Fine-grained admin permissions required
### - Integrates with Client Policies
### - Supports revocation chains for refresh tokens
### - Subject token must have requester client in audience claim
###
### Legacy Token Exchange V1 (preview feature):
### - Loose RFC 8693 implementation
### - Disabled by default, requires `--features=preview` or `--features=token-exchange`
### - Supports 4 use cases: internal-to-internal, internal-to-external, external-to-internal, impersonation
### - Requires Fine-grained admin permissions v1
### - Different scope parameter behavior (based on target client scopes)
### - Single audience value support
### - Limited public client support
### - No revocation chain support

###
### 1. Get Dev Console User Token
# @name get_dev_console_token
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{dev_console_client_id}}:{{dev_console_client_secret}}

grant_type=password&username={{user_regular}}&password={{user_regular_password}}&scope=openid email profile roles

{{@response
  if (response.statusCode === 200) {
    $global.dev_console_token = extractToken(response, 'access_token');
    
    // Validate token claims
    const userInfo = extractUserInfo($global.dev_console_token);
    console.log("✓ Dev console token obtained successfully");
  } else {
    handleError("Dev console token request failed", response);
  }
}}

###
### 2. Create App Client (Subject Token Source)
# @name create_app_client
# @ref get_dev_console_token
POST {{bodhi_apps_endpoint}}?live_test=true
Authorization: Bearer {{$global.dev_console_token}}
Content-Type: {{json_content_type}}

{
  "name": "Token Exchange V2 Demo App",
  "description": "Demo app client for token exchange v2 testing",
  "redirect_uris": ["{{app_redirect_uri}}"]
}

{{@response
  if (response.statusCode === 201) {
    const credentials = extractClientCredentials(response);
    $global.app_client_id = credentials.client_id;
    $global.app_client_secret = credentials.client_secret;
    
    console.log("✓ App client created:", credentials.client_id);
  } else {
    handleError("App client creation failed", response);
  }
}}

###
### Get App User Token (Subject Token)
# @name get_app_user_token
# @ref create_app_client
@oauth2_clientId={{$global.app_client_id}}
@oauth2_clientSecret=""
@oauth2_authorizationEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth
@oauth2_tokenEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token
@oauth2_redirectUri={{app_redirect_uri}}
@oauth2_scope=openid email profile roles scope_user_power_user
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: oauth2 authorization_code

{{@response
  if (response.statusCode === 200) {
    const userInfo = response.parsedBody || response.body;
    $global.app_user_token = oauth2Session?.accessToken;
    if (!$global.app_user_token) {
      handleError("OAuth2 access token not available", response);
    }
    const tokenUserInfo = extractUserInfo($global.app_user_token);
    if (tokenUserInfo.resource_access && tokenUserInfo.resource_access[$global.app_client_id]) {
      const roles = tokenUserInfo.resource_access[$global.app_client_id].roles || [];
      if (!roles.includes("admin")) {
        console.error("⚠ User does not have admin role in app client");
      }
    }
  } else {
    handleError("OAuth2 authorization code flow failed", response);
  }
}}

###
### Create Resource Client (Target Client)
# @name create_resource_client
# Prerequisites: Dev console user token
# Creates a new resource server client (confidential client) - this will be our target client
POST {{bodhi_resources_endpoint}}?live_test=true
Content-Type: {{json_content_type}}

{
  "redirect_uris": ["{{resource_redirect_uri}}"]
}

{{@response
  if (response.statusCode === 201) {
    const credentials = extractClientCredentials(response);
    $global.resource_client_id = credentials.client_id;
    $global.resource_client_secret = credentials.client_secret;
    
    console.log("✓ Resource client created:", credentials.client_id);
  } else {
    handleError("Resource client creation failed", response);
  }
}}

###
### 4. Get Service Account Token for Resource Client
# @name get_resource_service_token
# @ref create_resource_client
# Prerequisites: Resource client created
# Gets service account token for the resource client (this will be the requester client)
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=client_credentials&scope=service_account

{{@response
  if (response.statusCode === 200) {
    $global.resource_service_token = extractToken(response, 'access_token');
    
    // Validate token claims
    const userInfo = extractUserInfo($global.resource_service_token);
    console.log("✓ Resource service token obtained for client:", userInfo.azp);
  } else {
    handleError("Resource service token request failed", response);
  }
}}

###
### 6. TOKEN EXCHANGE V2 - Basic Internal-to-Internal Exchange
# @name token_exchange_v2_basic
# @ref get_resource_service_token
# @ref get_app_user_token
# Prerequisites: Resource service token (requester), App user token (subject)
# Demonstrates the new V2 token exchange mechanism (RFC 8693 compliant)
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token&audience={{$global.resource_client_id}}

{{@response
  console.log("=== TOKEN EXCHANGE V2 - Basic Exchange ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.exchanged_token_v2 = body.access_token;
    
    console.log("✓ V2 Token exchange successful");
    console.log("Response keys:", Object.keys(body));
    console.log("Token type:", body.token_type);
    console.log("Issued token type:", body.issued_token_type);
    console.log("Expires in:", body.expires_in);
    
    // Validate exchanged token
    const userInfo = extractUserInfo($global.exchanged_token_v2);
    console.log("Exchanged token issued to client:", userInfo.azp);
    console.log("Exchanged token audience:", userInfo.aud);
    console.log("Exchanged token scopes:", userInfo.scope);
    
  } else {
    const body = response.parsedBody || response.body;
    console.error("✗ V2 Token exchange failed");
    console.error("Status:", response.statusCode);
    console.error("Error:", body.error);
    console.error("Description:", body.error_description);
  }
}}

###
### 7. TOKEN EXCHANGE V2 - With Audience Parameter (Downscoping)
# @name token_exchange_v2_audience
# @ref get_resource_service_token
# @ref get_app_user_token
# Prerequisites: Resource service token (requester), App user token (subject)
# Demonstrates V2 token exchange with audience parameter for downscoping
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token&audience={{$global.resource_client_id}}

{{@response
  console.log("=== TOKEN EXCHANGE V2 - With Audience Parameter ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.exchanged_token_v2_audience = body.access_token;
    
    console.log("✓ V2 Token exchange with audience successful");
    
    // Validate exchanged token
    const userInfo = extractUserInfo($global.exchanged_token_v2_audience);
    console.log("Exchanged token issued to client:", userInfo.azp);
    console.log("Exchanged token audience:", userInfo.aud);
    console.log("Exchanged token scopes:", userInfo.scope);
    
    // Compare with basic exchange
    const basicUserInfo = extractUserInfo($global.exchanged_token_v2);
    console.log("Comparison - Basic audience:", basicUserInfo.aud);
    console.log("Comparison - Audience filtered:", userInfo.aud);
    
  } else {
    const body = response.parsedBody || response.body;
    console.error("✗ V2 Token exchange with audience failed");
    console.error("Status:", response.statusCode);
    console.error("Error:", body.error);
    console.error("Description:", body.error_description);
  }
}}

###
### 8. TOKEN EXCHANGE V2 - Request ID Token
# @name token_exchange_v2_id_token
# @ref get_resource_service_token
# @ref get_app_user_token
# Prerequisites: Resource service token (requester), App user token (subject)
# Demonstrates V2 token exchange requesting an ID token instead of access token
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:id_token

{{@response
  console.log("=== TOKEN EXCHANGE V2 - ID Token Request ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.exchanged_id_token_v2 = body.access_token; // Note: Contains ID token per spec
    
    console.log("✓ V2 Token exchange for ID token successful");
    console.log("Token type:", body.token_type);
    console.log("Issued token type:", body.issued_token_type);
    
    // Validate ID token
    const userInfo = extractUserInfo($global.exchanged_id_token_v2);
    console.log("ID token issued to client:", userInfo.azp);
    console.log("ID token audience:", userInfo.aud);
    console.log("ID token subject:", userInfo.sub);
    
  } else {
    const body = response.parsedBody || response.body;
    console.error("✗ V2 Token exchange for ID token failed");
    console.error("Status:", response.statusCode);
    console.error("Error:", body.error);
    console.error("Description:", body.error_description);
  }
}}

###
### 9. TOKEN EXCHANGE V2 - Multiple Audiences
# @name token_exchange_v2_multiple_audiences
# @ref get_resource_service_token
# @ref get_app_user_token
# Prerequisites: Resource service token (requester), App user token (subject)
# Demonstrates V2 token exchange with multiple audience values
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token&audience={{$global.resource_client_id}}&audience={{$global.app_client_id}}

{{@response
  console.log("=== TOKEN EXCHANGE V2 - Multiple Audiences ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.exchanged_token_v2_multi_aud = body.access_token;
    
    console.log("✓ V2 Token exchange with multiple audiences successful");
    
    // Validate exchanged token
    const userInfo = extractUserInfo($global.exchanged_token_v2_multi_aud);
    console.log("Exchanged token issued to client:", userInfo.azp);
    console.log("Exchanged token audiences:", userInfo.aud);
    console.log("Exchanged token scopes:", userInfo.scope);
    
    // Check if multiple audiences are present
    if (Array.isArray(userInfo.aud)) {
      console.log("Multiple audiences found:", userInfo.aud.length);
    } else {
      console.log("Single audience:", userInfo.aud);
    }
    
  } else {
    const body = response.parsedBody || response.body;
    console.error("✗ V2 Token exchange with multiple audiences failed");
    console.error("Status:", response.statusCode);
    console.error("Error:", body.error);
    console.error("Description:", body.error_description);
  }
}}

###
### 10. TOKEN EXCHANGE V2 - Error Scenarios
# @name token_exchange_v2_errors
# @ref get_resource_service_token
# Prerequisites: Resource service token (requester)
# Tests various V2 error conditions
{{
  console.log("=== TOKEN EXCHANGE V2 - Error Scenarios ===");
  
  const errorTests = [
    {
      name: "Missing subject token",
      params: "grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token"
    },
    {
      name: "Invalid subject token type",
      params: "grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token=" + $global.app_user_token + "&subject_token_type=invalid-type&requested_token_type=urn:ietf:params:oauth:token-type:access_token"
    },
    {
      name: "Invalid subject token",
      params: "grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token=invalid-token&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token"
    },
    {
      name: "Invalid audience",
      params: "grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token=" + $global.app_user_token + "&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token&audience=invalid-client-id"
    }
  ];
  
  $global.v2_error_tests = errorTests;
  console.log("Prepared", errorTests.length, "error test scenarios");
}}

###
### 11. Execute V2 Error Test 1 - Missing Subject Token
# @name v2_error_test_1
# @ref get_resource_service_token
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token

{{@response
  console.log("V2 Error Test 1 - Missing Subject Token:");
  
  if (response.statusCode >= 400) {
    const body = response.parsedBody || response.body;
    console.log("✓ Correctly rejected - Status:", response.statusCode);
    console.log("Error:", body.error);
    console.log("Description:", body.error_description);
  } else {
    console.error("✗ Should have been rejected but got status:", response.statusCode);
  }
}}

###
### 12. Execute V2 Error Test 2 - Invalid Subject Token Type
# @name v2_error_test_2
# @ref get_resource_service_token
# @ref get_app_user_token
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&subject_token_type=invalid-type&requested_token_type=urn:ietf:params:oauth:token-type:access_token

{{@response
  console.log("V2 Error Test 2 - Invalid Subject Token Type:");
  
  if (response.statusCode >= 400) {
    const body = response.parsedBody || response.body;
    console.log("✓ Correctly rejected - Status:", response.statusCode);
    console.log("Error:", body.error);
    console.log("Description:", body.error_description);
  } else {
    console.error("✗ Should have been rejected but got status:", response.statusCode);
  }
}}

###
### 13. Execute V2 Error Test 3 - Invalid Audience
# @name v2_error_test_3
# @ref get_resource_service_token
# @ref get_app_user_token
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token&audience=invalid-client-id

{{@response
  console.log("V2 Error Test 3 - Invalid Audience:");
  
  if (response.statusCode >= 400) {
    const body = response.parsedBody || response.body;
    console.log("✓ Correctly rejected - Status:", response.statusCode);
    console.log("Error:", body.error);
    console.log("Description:", body.error_description);
  } else {
    console.error("✗ Should have been rejected but got status:", response.statusCode);
  }
}}

###
### 14. TOKEN EXCHANGE V1 - Legacy Comparison (if enabled)
# @name token_exchange_v1_legacy
# @ref get_resource_service_token
# @ref get_app_user_token
# Prerequisites: Resource service token (requester), App user token (subject)
# Demonstrates the legacy V1 token exchange for comparison
# NOTE: This requires --features=preview or --features=token-exchange and FGAP v1
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&audience={{$global.resource_client_id}}

{{@response
  console.log("=== TOKEN EXCHANGE V1 - Legacy Comparison ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.exchanged_token_v1 = body.access_token;
    
    console.log("✓ V1 Token exchange successful");
    console.log("Response keys:", Object.keys(body));
    
    // Validate exchanged token
    const userInfo = extractUserInfo($global.exchanged_token_v1);
    console.log("V1 exchanged token issued to client:", userInfo.azp);
    console.log("V1 exchanged token audience:", userInfo.aud);
    console.log("V1 exchanged token scopes:", userInfo.scope);
    
    // Compare with V2 if available
    if ($global.exchanged_token_v2) {
      const v2UserInfo = extractUserInfo($global.exchanged_token_v2);
      console.log("--- V1 vs V2 Comparison ---");
      console.log("V1 audience:", userInfo.aud);
      console.log("V2 audience:", v2UserInfo.aud);
      console.log("V1 scopes:", userInfo.scope);
      console.log("V2 scopes:", v2UserInfo.scope);
    }
    
  } else {
    const body = response.parsedBody || response.body;
    console.error("✗ V1 Token exchange failed (may not be enabled)");
    console.error("Status:", response.statusCode);
    console.error("Error:", body.error);
    console.error("Description:", body.error_description);
    console.log("Note: V1 requires --features=preview or --features=token-exchange");
  }
}}

###
### 15. Token Introspection - V2 Token
# @name introspect_v2_token
# @ref token_exchange_v2_basic
# Prerequisites: V2 exchanged token
# Introspects the V2 exchanged token to verify its properties
POST {{token_endpoint}}/introspect
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

token={{$global.exchanged_token_v2}}

{{@response
  console.log("=== TOKEN INTROSPECTION - V2 Token ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    
    console.log("✓ V2 token introspection successful");
    console.log("Active:", body.active);
    console.log("Client ID:", body.client_id);
    console.log("Username:", body.username);
    console.log("Token type:", body.token_type);
    console.log("Scope:", body.scope);
    console.log("Audience:", body.aud);
    console.log("Expires at:", new Date(body.exp * 1000).toISOString());
    
    if (!body.active) {
      console.error("⚠ V2 token is inactive");
    }
    
    if (body.client_id !== $global.resource_client_id) {
      console.error("⚠ V2 token client_id mismatch");
    }
    
  } else {
    handleError("V2 token introspection failed", response);
  }
}}

###
### 16. Well-Known Configuration Check
# @name check_wellknown_v2
# Checks if token exchange is advertised in the well-known configuration
GET {{keycloak}}/realms/{{realm}}/.well-known/openid_configuration

{{@response
  console.log("=== WELL-KNOWN CONFIGURATION CHECK ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    
    console.log("✓ Well-known configuration retrieved");
    
    // Check supported grant types
    const grantTypes = body.grant_types_supported || [];
    const tokenExchangeSupported = grantTypes.includes('urn:ietf:params:oauth:grant-type:token-exchange');
    
    console.log("Supported grant types:", grantTypes.length);
    console.log("Token exchange supported:", tokenExchangeSupported);
    
    if (tokenExchangeSupported) {
      console.log("✓ Token exchange is advertised in well-known configuration");
    } else {
      console.log("⚠ Token exchange not found in grant_types_supported");
    }
    
    // Check other relevant fields
    console.log("Token endpoint:", body.token_endpoint);
    console.log("Introspection endpoint:", body.introspection_endpoint);
    
  } else {
    handleError("Well-known configuration check failed", response);
  }
}}

###
### 17. Performance Comparison
# @name performance_comparison
# @ref get_resource_service_token
# @ref get_app_user_token
# Prerequisites: Resource service token (requester), App user token (subject)
# Compares performance between V1 and V2 token exchange
{{
  console.log("=== PERFORMANCE COMPARISON ===");
  
  const performanceTests = [];
  
  // Test V2 performance
  const v2StartTime = Date.now();
  $global.perf_v2_start = v2StartTime;
  
  console.log("Starting V2 performance test...");
}}

POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token

{{@response
  const v2EndTime = Date.now();
  const v2Duration = v2EndTime - $global.perf_v2_start;
  
  console.log("V2 Performance Results:");
  console.log("Duration:", v2Duration + "ms");
  console.log("Status:", response.statusCode);
  
  if (response.statusCode === 200) {
    console.log("✓ V2 exchange completed successfully");
    $global.perf_v2_duration = v2Duration;
  } else {
    console.error("✗ V2 exchange failed");
  }
}}

###
### 18. Summary Report
# @name summary_report
# Generates a comprehensive summary of V2 vs V1 token exchange
{{
  console.log("=== TOKEN EXCHANGE V2 SUMMARY REPORT ===");
  console.log("");
  
  console.log("🔄 KEYCLOAK TOKEN EXCHANGE V2 DEMONSTRATION");
  console.log("===========================================");
  console.log("");
  
  console.log("📋 FEATURE COMPARISON:");
  console.log("• V2 (Standard): RFC 8693 compliant, officially supported");
  console.log("• V1 (Legacy): Preview feature, loose RFC implementation");
  console.log("");
  
  console.log("🎯 SUPPORTED USE CASES:");
  console.log("• V2: Internal-to-internal token exchange only");
  console.log("• V1: Internal-to-internal, internal-to-external, external-to-internal, impersonation");
  console.log("");
  
  console.log("🔧 CONFIGURATION:");
  console.log("• V2: Enabled by default, requires 'Standard token exchange' switch on client");
  console.log("• V1: Requires --features=preview, needs Fine-grained admin permissions v1");
  console.log("");
  
  console.log("🎛️ PARAMETER BEHAVIOR:");
  console.log("• V2: Standard OAuth scope behavior, multiple audience support");
  console.log("• V1: Target client scope behavior, single audience only");
  console.log("");
  
  console.log("🔐 SECURITY:");
  console.log("• V2: Client policies integration, audience validation required");
  console.log("• V1: Fine-grained admin permissions, more permissive");
  console.log("");
  
  console.log("🔄 REVOCATION:");
  console.log("• V2: Supports revocation chains for refresh tokens");
  console.log("• V1: No revocation chain support");
  console.log("");
  
  console.log("📊 TEST RESULTS:");
  if ($global.exchanged_token_v2) {
    console.log("✓ V2 Basic exchange: SUCCESS");
  } else {
    console.log("✗ V2 Basic exchange: FAILED");
  }
  
  if ($global.exchanged_token_v2_audience) {
    console.log("✓ V2 Audience filtering: SUCCESS");
  } else {
    console.log("✗ V2 Audience filtering: FAILED");
  }
  
  if ($global.exchanged_id_token_v2) {
    console.log("✓ V2 ID token exchange: SUCCESS");
  } else {
    console.log("✗ V2 ID token exchange: FAILED");
  }
  
  if ($global.exchanged_token_v1) {
    console.log("✓ V1 Legacy exchange: SUCCESS");
  } else {
    console.log("⚠ V1 Legacy exchange: NOT AVAILABLE (requires preview features)");
  }
  
  console.log("");
  console.log("💡 RECOMMENDATIONS:");
  console.log("• Use V2 for new implementations (officially supported)");
  console.log("• V1 still useful for external token exchange and impersonation");
  console.log("• Both can be enabled simultaneously for different use cases");
  console.log("• Always use audience parameter for downscoping in V2");
  console.log("");
  
  console.log("📖 DOCUMENTATION:");
  console.log("• V2 Guide: https://www.keycloak.org/securing-apps/token-exchange");
  console.log("• RFC 8693: https://datatracker.ietf.org/doc/html/rfc8693");
  console.log("");
  
  console.log("=== END OF REPORT ===");
}}

###
### 19. Cleanup - Clear Token Exchange Variables
# @name cleanup_token_exchange_v2
# Clears all token exchange related variables
{{
  console.log("=== CLEANUP TOKEN EXCHANGE V2 VARIABLES ===");
  
  const variablesToClear = [
    'dev_console_token',
    'app_client_id',
    'app_client_secret',
    'resource_client_id',
    'resource_client_secret',
    'app_user_token',
    'resource_service_token',
    'exchanged_token_v2',
    'exchanged_token_v2_audience',
    'exchanged_id_token_v2',
    'exchanged_token_v2_multi_aud',
    'exchanged_token_v1',
    'v2_error_tests',
    'perf_v2_start',
    'perf_v2_duration'
  ];
  
  variablesToClear.forEach(varName => {
    if ($global[varName]) {
      delete $global[varName];
    }
  });
  
  console.log("✓ Token exchange V2 variables cleared");
}}
