### Token Exchange V2 vs V1 Comparison
###
# Import shared variables
# @import ./common.http

@app_client_id=test-app-39b29ccf-346d-4e84-bb4d-db48f80f7f68
@resource_client_id=test-resource-ffa995de-ccc9-46d4-ad40-2a3c98246d68
@resource_client_secret=DDZZ2HuwFQCfzvGT1XgHIVuj84x84OdB

###
### Get App User Token (Subject Token)
# @name get_app_user_token
@oauth2_clientId={{app_client_id}}
@oauth2_clientSecret=""
@oauth2_authorizationEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth
@oauth2_tokenEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token
@oauth2_redirectUri={{app_redirect_uri}}
@oauth2_scope=openid email profile roles scope_user_power_user 
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: oauth2 authorization_code

{{@response
  if (response.statusCode === 200) {
    const userInfo = response.parsedBody || response.body;
    $global.app_user_token = oauth2Session?.accessToken;
    if (!$global.app_user_token) {
      handleError("OAuth2 access token not available", response);
    }
  } else {
    handleError("OAuth2 authorization code flow failed", response);
  }
}}

###
### TOKEN EXCHANGE V2 - Basic Internal-to-Internal Exchange
# @name token_exchange_v2_basic
# @ref get_app_user_token
# Prerequisites: Resource service token (requester), App user token (subject)
# Demonstrates the new V2 token exchange mechanism (RFC 8693 compliant)
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{resource_client_id}}:{{resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token&scope=openid email profile roles scope_user_power_user&audience={{resource_client_id}}
