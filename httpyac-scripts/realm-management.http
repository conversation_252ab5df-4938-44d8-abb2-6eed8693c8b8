### Realm Management
### This httpbook manages users, groups, and clients in the Keycloak bodhi realm
### Uses HTTPyac native features for proper variable management and response handling

###
# Import shared variables and functions
# @import ./common.http

# Variables imported from common.http via @import

# Functions imported from common.http via @import

###
### Get Master Realm Admin Token
# @name get_master_admin_token
# Gets an admin token from the master realm for realm management
POST {{master_token_endpoint}}
Content-Type: {{form_content_type}}

grant_type=password&client_id=admin-cli&username={{master_admin_username}}&password={{master_admin_password}}

{{@response
  if (response.statusCode === 200) {
    $global.master_admin_token = extractToken(response, 'access_token');
  } else {
    handleError("Master admin token request failed", response);
  }
}}

###
### Create or Update User
# @name create_user
# @ref get_master_admin_token
# Creates a user in the bodhi realm (change username and email as needed)
POST {{admin_users_endpoint}}
Authorization: Bearer {{$global.master_admin_token}}
Content-Type: {{json_content_type}}

{
  "username": "{{user_resource_admin}}",
  "email": "{{user_resource_admin}}",
  "enabled": true,
  "emailVerified": true,
  "credentials": [
    {
      "type": "password",
      "value": "{{user_resource_admin_password}}",
      "temporary": false
    }
  ]
}

{{@response
  if (response.statusCode === 201) {
    // User created successfully
  } else if (response.statusCode === 409) {
    // User already exists - this is acceptable
  } else {
    handleError("User creation failed", response);
  }
}}

###
### Find User by Username
# @name find_user
# @ref get_master_admin_token
# Finds a user by username to get their ID
GET {{admin_users_endpoint}}?username={{user_resource_admin}}
Authorization: Bearer {{$global.master_admin_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body && body.length > 0) {
      $global.user_id = body[0].id;
      // User found successfully
    } else {
      console.error("User not found");
      throw new Error("User not found");
    }
  } else {
    handleError("User search failed", response);
  }
}}

###
### Create Group
# @name create_group
# @ref get_master_admin_token
# Creates a group in the bodhi realm
POST {{admin_groups_endpoint}}
Authorization: Bearer {{$global.master_admin_token}}
Content-Type: {{json_content_type}}

{
  "name": "test-group",
  "path": "/test-group"
}

{{@response
  if (response.statusCode === 201) {
    // Group created successfully
  } else if (response.statusCode === 409) {
    // Group already exists
  } else {
    handleError("Group creation failed", response);
  }
}}

###
### Find Group by Name
# @name find_group
# @ref get_master_admin_token
# Finds a group by name to get its ID
GET {{admin_groups_endpoint}}?search=test-group
Authorization: Bearer {{$global.master_admin_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body && body.length > 0) {
      $global.group_id = body[0].id;
    } else {
      console.error("Group not found");
      throw new Error("Group not found");
    }
  } else {
    handleError("Group search failed", response);
  }
}}

###
### Add User to Group
# @name add_user_to_group
# @ref find_user
# @ref find_group
# Adds a user to a group
PUT {{admin_users_endpoint}}/{{$global.user_id}}/groups/{{$global.group_id}}
Authorization: Bearer {{$global.master_admin_token}}

{{@response
  if (response.statusCode === 204) {
    // User added to group successfully
  } else {
    handleError("Add user to group failed", response);
  }
}}

###
### List All Users
# @name list_users
# @ref get_master_admin_token
# Lists all users in the bodhi realm
GET {{admin_users_endpoint}}
Authorization: Bearer {{$global.master_admin_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    // Users listed successfully
  } else {
    handleError("List users failed", response);
  }
}}

###
### List All Groups
# @name list_groups
# @ref get_master_admin_token
# Lists all groups in the bodhi realm
GET {{admin_groups_endpoint}}
Authorization: Bearer {{$global.master_admin_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    // Groups listed successfully
  } else {
    handleError("List groups failed", response);
  }
}}

###
### Create Multiple Test Users
# @name create_test_users
# @ref get_master_admin_token
# Creates all test users defined in the environment
{{
  const testUsers = [
    { firstName: "Admin", lastName: "User", username: user_resource_admin, password: user_resource_admin_password },
    { firstName: "Power", lastName: "User", username: user_power_user, password: user_power_user_password },
    { firstName: "Manager", lastName: "User", username: user_manager, password: user_manager_password },
    { firstName: "Regular", lastName: "User", username: user_regular, password: user_regular_password },
    { firstName: "Offline", lastName: "User", username: user_offline_username, password: user_offline_password }
  ];
  
  async function createTestUsers() {
    const results = [];
    
    for (const user of testUsers) {
      try {
        const response = await fetch(admin_users_endpoint, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${$global.master_admin_token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.username,
            enabled: true,
            emailVerified: true,
            credentials: [
              {
                type: "password",
                value: user.password,
                temporary: false
              }
            ]
          })
        });
        
        if (response.statusCode === 201) {
          results.push({ username: user.username, status: 'created' });
        } else if (response.statusCode === 409) {
          results.push({ username: user.username, status: 'exists' });
        } else {
          results.push({ username: user.username, status: 'failed', error: response.statusCode });
        }
      } catch (error) {
        results.push({ username: user.username, status: 'error', error: error.message });
      }
    }
    
    return results;
  }
  
  exports.create_users_promise = createTestUsers();
}}

###
### Complete Realm Setup
# @name complete_realm_setup
# Demonstrates the complete realm setup workflow
{{
  async function completeRealmSetup() {
    const steps = [
      "get_master_admin_token",
      "create_test_users",
      "list_users",
      "list_groups"
    ];
    
    return { workflow: "ready", steps };
  }
  
  exports.setup_info = completeRealmSetup();
}} 