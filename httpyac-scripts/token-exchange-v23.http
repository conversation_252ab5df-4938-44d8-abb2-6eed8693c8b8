### Keycloak v23 token exchange flow 
### This httpyac script demonstrates the token exchange flow for version 23
###
# Import shared variables and functions
# @import ./common.http
# Client IDs will be generated dynamically

###
### Step 1: Get Admin CLI Token
# @name get_admin_cli_token_v23
POST {{master_token_endpoint}}
Content-Type: {{form_content_type}}

grant_type=password&client_id=admin-cli&username={{master_admin_username}}&password={{master_admin_password}}

{{@response
  if (response.statusCode === 200) {
    $global.admin_cli_token = extractToken(response, 'access_token');
    console.log("✅ Admin CLI token obtained");
  } else {
    handleError("Admin CLI token request failed", response);
  }
}}

###
### Step 2: Create Resource Client using Bodhi API
# @name create_resource_client_v23
# @ref get_admin_cli_token_v23
POST {{bodhi_clients_endpoint}}?live_test=true
Authorization: Bearer {{$global.admin_cli_token}}
Content-Type: {{json_content_type}}

{
  "redirect_uris": ["http://localhost:8090/callback"]
}

{{@response
  if (response.statusCode === 201) {
    const credentials = extractClientCredentials(response);
    $global.resource_client_id = credentials.client_id;
    $global.resource_client_secret = credentials.client_secret;
    console.log("✅ Resource client created:", $global.resource_client_id);
  } else {
    handleError("Failed to create resource client", response);
  }
}}

###
### Step 3: Create App Client (Public Client) via Admin CLI
# @name create_app_client_v23
POST {{admin_clients_endpoint}}
Authorization: Bearer {{$global.admin_cli_token}}
Content-Type: {{json_content_type}}

{
  "name": "Test App Client v23",
  "description": "Test app client for v23 token exchange",
  "enabled": true,
  "publicClient": true,
  "protocol": "openid-connect",
  "redirectUris": ["http://localhost:3000/callback"],
  "webOrigins": ["http://localhost:3000"],
  "standardFlowEnabled": true,
  "implicitFlowEnabled": false,
  "directAccessGrantsEnabled": false,
  "serviceAccountsEnabled": false,
  "authorizationServicesEnabled": false,
  "frontchannelLogout": true,
  "fullScopeAllowed": false
}

{{@response
  if (response.statusCode === 201) {
    // Extract client ID from Location header
    const locationHeader = response.headers.location || response.headers.Location;
    if (locationHeader) {
      // Extract client ID from URL like: https://dev-id.getbodhi.app/admin/realms/bodhi/clients/7b5f5b4d-d62b-42e5-ba59-4914732da5f5
      const clientIdMatch = locationHeader.match(/\/clients\/([^\/]+)$/);
      if (clientIdMatch) {
        $global.app_client_id = clientIdMatch[1];
        console.log("✅ App client created:", $global.app_client_id);
      } else {
        handleError("Could not extract client ID from Location header", response);
      }
    } else {
      handleError("Location header not found in response", response);
    }
  } else {
    handleError("Failed to create app client", response);
  }
}}

###
### Step 4: Get Resource Client Service Account Token
# @name get_resource_service_token_v23
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=client_credentials

{{@response
  if (response.statusCode === 200) {
    $global.resource_service_token = extractToken(response, 'access_token');
    console.log("✅ Resource service account token obtained");
  } else {
    handleError("Resource service token request failed", response);
  }
}}

###
### Step 5: Make <EMAIL> a Resource Admin
# @name make_resource_admin_v23
# @ref get_resource_service_token_v23
@make_client_admin_endpoint={{keycloak}}/realms/{{realm}}/{{provider_id}}/clients/make-resource-admin
POST {{make_client_admin_endpoint}}
Authorization: Bearer {{$global.resource_service_token}}
Content-Type: {{json_content_type}}

{
  "username": "<EMAIL>"
}

{{@response
  if (response.statusCode === 201) {
    console.log("✅ <EMAIL> made resource admin");
  } else {
    handleError("Failed to make resource admin", response);
  }
}}

###
### Step 6: Get Admin User Token via Direct Access Grant
# @name get_admin_user_token_v23
# @ref make_resource_admin_v23
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=password&username=<EMAIL>&password=pass&scope=openid email profile roles

{{@response
  if (response.statusCode === 200) {
    $global.admin_user_token = extractToken(response, 'access_token');
    console.log("✅ Admin user token obtained via direct access grant");
  } else {
    handleError("Admin user token request failed", response);
  }
}}

###
### Step 7: Add <EMAIL> to Power Users Group
# @name add_power_user_to_group_v23
# @ref get_admin_user_token_v23
@v23_add_to_group_endpoint={{keycloak}}/realms/{{realm}}/{{provider_id}}/clients/add-user-to-group
POST {{v23_add_to_group_endpoint}}
Authorization: Bearer {{$global.admin_user_token}}
Content-Type: {{json_content_type}}

{
  "username": "<EMAIL>",
  "group": "power-users",
  "add": true
}

{{@response
  if (response.statusCode === 201) {
    console.log("✅ <EMAIL> added to power-users group");
  } else {
    handleError("Failed to add user to power-users group", response);
  }
}}

###
### Step 8: Get Power User Token via OAuth2 Authorization Code Flow (App Client)
# @name get_power_user_token_v23
# OAuth2 variables for HTTPyac (app client - public client)
@oauth2_clientId={{$global.app_client_id}}
@oauth2_clientSecret=""
@oauth2_authorizationEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth
@oauth2_tokenEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token
@oauth2_redirectUri=http://localhost:3000/callback
@oauth2_scope=openid email profile roles scope_user_user
@oauth2_usePkce=true

GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: oauth2 authorization_code

{{@response
  if (response.statusCode === 200) {
    $global.power_user_app_token = oauth2Session?.accessToken;
    if (!$global.power_user_app_token) {
      handleError("OAuth2 access token not available", response);
    }
    
    // Analyze the app token
    const tokenUserInfo = extractUserInfo($global.power_user_app_token);
    console.log("✅ Power user app token obtained");
    console.log("Token issued to client:", tokenUserInfo.azp);
    console.log("Token scopes:", tokenUserInfo.scope);
    
    // Extract the scope_user_* scope for token exchange
    const scopeUserScope = tokenUserInfo.scope.split(' ')
      .find(scope => scope.startsWith('scope_user_'));
    
    if (scopeUserScope) {
      $global.user_scope = scopeUserScope;
      console.log("User scope found:", $global.user_scope);
    } else {
      console.error("No scope_user_* scope found in token");
    }
  } else {
    handleError("Power user OAuth2 authorization failed", response);
  }
}}

###
### Step 9: Token Exchange v23 - Preview Implementation
# @name token_exchange_v23
# @ref get_power_user_token_v23
# This uses the v23 preview token exchange format with audience parameter
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Bearer {{$global.resource_service_token}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.power_user_app_token}}&client_id={{$global.app_client_id}}&audience={{$global.resource_client_id}}&scope=openid email profile roles {{$global.user_scope}}

{{@response
  console.log("=== TOKEN EXCHANGE V23 TEST ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.exchanged_token = body.access_token;
    
    console.log("✅ Token exchange successful!");
    console.log("Token type:", body.token_type);
    console.log("Expires in:", body.expires_in, "seconds");
    
    // Analyze the exchanged token
    const exchangedUserInfo = extractUserInfo($global.exchanged_token);
    console.log("--- Exchanged Token Analysis ---");
    console.log("Issued to client (azp):", exchangedUserInfo.azp);
    console.log("Audience:", exchangedUserInfo.aud);
    console.log("Subject:", exchangedUserInfo.sub);
    console.log("Scopes:", exchangedUserInfo.scope);
    
    // Verify resource access roles
    if (exchangedUserInfo.resource_access && exchangedUserInfo.resource_access[$global.resource_client_id]) {
      const resourceRoles = exchangedUserInfo.resource_access[$global.resource_client_id].roles;
      console.log("Resource roles:", resourceRoles);
      
      if (resourceRoles.includes("resource_power_user") && resourceRoles.includes("resource_user")) {
        console.log("🎯 SUCCESS: Power user roles found in exchanged token");
      } else {
        console.error("❌ ISSUE: Expected power user roles not found");
      }
    } else {
      console.error("❌ ISSUE: No resource access found in exchanged token");
    }
    
    console.log("🎯 V23 TOKEN EXCHANGE SUCCESS: Preview implementation working!");
    
  } else {
    const body = response.parsedBody || response.body;
    console.error("❌ Token exchange failed");
    console.error("Status:", response.statusCode);
    console.error("Error:", body.error);
    console.error("Description:", body.error_description);
  }
}}

###
### Cleanup v23 Test
# @name cleanup_v23
{{
  const variablesToClear = [
    'admin_cli_token', 'resource_client_id', 'resource_client_secret', 
    'resource_service_token', 'admin_user_token', 'app_client_id',
    'power_user_app_token', 'user_scope', 'exchanged_token'
  ];
  
  variablesToClear.forEach(varName => {
    if ($global[varName]) {
      delete $global[varName];
    }
  });
  
  console.log("✅ V23 test variables cleared");
}}
