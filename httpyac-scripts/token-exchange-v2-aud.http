### Token Exchange V2 with Audience Mapping - HTTPyac Native OAuth2
###
# Import shared variables
# @import ./common.http

# Test client configuration - replace with your actual client IDs
@app_client_id=test-app-39b29ccf-346d-4e84-bb4d-db48f80f7f68
@resource_client_id=test-resource-ffa995de-ccc9-46d4-ad40-2a3c98246d68
@resource_client_secret=DDZZ2HuwFQCfzvGT1XgHIVuj84x84OdB

# OAuth2 configuration for HTTPyac built-in flows
@oauth2_clientId={{app_client_id}}
@oauth2_clientSecret=""
@oauth2_authorizationEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/auth
@oauth2_tokenEndpoint={{keycloak}}/realms/{{realm}}/protocol/openid-connect/token
@oauth2_redirectUri={{app_redirect_uri}}
@oauth2_scope=openid email profile roles scope_user_power_user

###
### Step 1: Get Admin Token
# @name get_admin_token
POST {{master_token_endpoint}}
Content-Type: {{form_content_type}}

grant_type=password&client_id=admin-cli&username={{master_admin_username}}&password={{master_admin_password}}

{{@response
  if (response.statusCode === 200) {
    $global.admin_token = extractToken(response, 'access_token');
    console.log("✅ Admin token obtained");
  } else {
    handleError("Admin token request failed", response);
  }
}}

###
### Step 2: Get App Client UUID
# @name get_app_client_uuid
# @ref get_admin_token
GET {{admin_clients_endpoint}}?clientId={{app_client_id}}
Authorization: Bearer {{$global.admin_token}}

{{@response
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    if (body && body.length > 0) {
      $global.app_client_uuid = body[0].id;
      console.log("✅ App client UUID obtained:", $global.app_client_uuid);
    } else {
      handleError("App client not found", response);
    }
  } else {
    handleError("Failed to get app client UUID", response);
  }
}}

###
### Step 3: Configure Audience Mapper for App Client
# @name configure_audience_mapper
# @ref get_app_client_uuid
POST {{keycloak}}/admin/realms/{{realm}}/clients/{{$global.app_client_uuid}}/protocol-mappers/models
Authorization: Bearer {{$global.admin_token}}
Content-Type: application/json

{
  "name": "resource-audience-mapper",
  "protocol": "openid-connect",
  "protocolMapper": "oidc-audience-mapper",
  "config": {
    "included.client.audience": "{{resource_client_id}}",
    "id.token.claim": "false",
    "access.token.claim": "true"
  }
}

{{@response
  if (response.statusCode === 201) {
    console.log("✅ Audience mapper configured successfully");
    console.log("The app client tokens will now include resource client in audience");
  } else if (response.statusCode === 409) {
    console.log("⚠️  Audience mapper already exists (409 Conflict)");
  } else {
    handleError("Failed to configure audience mapper", response);
  }
}}

###
### Step 4: Get App User Token Using HTTPyac OAuth2 Flow
# @name get_app_user_token
# @ref configure_audience_mapper
# This uses HTTPyac's built-in OAuth2 authorization code flow
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: oauth2 authorization_code

{{@response
  if (response.statusCode === 200) {
    const userInfo = response.parsedBody || response.body;
    $global.app_user_token = oauth2Session?.accessToken;
    
    if (!$global.app_user_token) {
      handleError("OAuth2 access token not available", response);
    }
    
    console.log("✅ App user token obtained via OAuth2 flow");
    console.log("User info:", userInfo.preferred_username || userInfo.sub);
    
    // Decode and verify the token has the resource client in audience
    const tokenUserInfo = extractUserInfo($global.app_user_token);
    console.log("Token issued to client:", tokenUserInfo.azp);
    console.log("Token audience:", tokenUserInfo.aud);
    console.log("Token scope:", tokenUserInfo.scope);
    
    // Verify audience includes resource client
    let hasResourceAudience = false;
    if (Array.isArray(tokenUserInfo.aud)) {
      hasResourceAudience = tokenUserInfo.aud.includes(resource_client_id);
    } else if (tokenUserInfo.aud === resource_client_id) {
      hasResourceAudience = true;
    }
    
    if (hasResourceAudience) {
      console.log("✅ Resource client found in token audience - token exchange should work");
    } else {
      console.error("❌ Resource client NOT found in token audience");
      console.error("This will cause 'client not in audience' error during token exchange");
    }
    
  } else {
    handleError("OAuth2 authorization code flow failed", response);
  }
}}

###
### Step 5: Token Exchange V2 - Standard RFC 8693 Exchange
# @name token_exchange_v2
# @ref get_app_user_token
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{resource_client_id}}:{{resource_client_secret}}

grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token={{$global.app_user_token}}&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token&audience={{resource_client_id}}

{{@response
  console.log("=== TOKEN EXCHANGE V2 RESULT ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    $global.exchanged_token = body.access_token;
    
    console.log("✅ Token exchange successful!");
    console.log("Token type:", body.token_type);
    console.log("Issued token type:", body.issued_token_type);
    console.log("Expires in:", body.expires_in, "seconds");
    
    // Decode and analyze the exchanged token
    const exchangedUserInfo = extractUserInfo($global.exchanged_token);
    console.log("--- Exchanged Token Analysis ---");
    console.log("Issued to client:", exchangedUserInfo.azp);
    console.log("Audience:", exchangedUserInfo.aud);
    console.log("Subject:", exchangedUserInfo.sub);
    console.log("Scope:", exchangedUserInfo.scope);
    
    // Compare original vs exchanged token
    const originalUserInfo = extractUserInfo($global.app_user_token);
    console.log("--- Token Comparison ---");
    console.log("Original client:", originalUserInfo.azp);
    console.log("Exchanged client:", exchangedUserInfo.azp);
    console.log("Original audience:", originalUserInfo.aud);
    console.log("Exchanged audience:", exchangedUserInfo.aud);
    
  } else {
    const body = response.parsedBody || response.body;
    console.error("❌ Token exchange failed");
    console.error("Status:", response.statusCode);
    console.error("Error:", body.error);
    console.error("Description:", body.error_description);
    
    if (body.error === "invalid_request" && body.error_description?.includes("audience")) {
      console.error("💡 This error indicates the subject token doesn't have the requester client in its audience");
      console.error("💡 Make sure the audience mapper is configured correctly on the app client");
    }
  }
}}

###
### Step 6: Test Exchanged Token - Call Resource Server
# @name test_exchanged_token
# @ref token_exchange_v2
# Test the exchanged token by calling a resource server endpoint
GET {{keycloak}}/realms/{{realm}}/protocol/openid-connect/userinfo
Authorization: Bearer {{$global.exchanged_token}}

{{@response
  if (response.statusCode === 200) {
    const userInfo = response.parsedBody || response.body;
    console.log("✅ Exchanged token is valid and working");
    console.log("User info from exchanged token:", userInfo.preferred_username || userInfo.sub);
  } else {
    console.error("❌ Exchanged token failed validation");
    handleError("Exchanged token validation failed", response);
  }
}}

###
### Step 7: Token Introspection - Verify Token Properties
# @name introspect_exchanged_token
# @ref token_exchange_v2
POST {{keycloak}}/realms/{{realm}}/protocol/openid-connect/token/introspect
Content-Type: {{form_content_type}}
Authorization: Basic {{resource_client_id}}:{{resource_client_secret}}

token={{$global.exchanged_token}}

{{@response
  console.log("=== TOKEN INTROSPECTION RESULT ===");
  
  if (response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    
    console.log("✅ Token introspection successful");
    console.log("Active:", body.active);
    console.log("Client ID:", body.client_id);
    console.log("Username:", body.username);
    console.log("Token type:", body.token_type);
    console.log("Scope:", body.scope);
    console.log("Audience:", body.aud);
    console.log("Expires at:", new Date(body.exp * 1000).toISOString());
    
    if (!body.active) {
      console.error("⚠️  Token is not active");
    }
    
    if (body.client_id !== resource_client_id) {
      console.error("⚠️  Token client_id doesn't match resource client");
    }
    
  } else {
    handleError("Token introspection failed", response);
  }
}}

###
### Step 8: Summary Report
# @name summary_report
# @ref introspect_exchanged_token
{{
  console.log("=== AUDIENCE MAPPING DEMO SUMMARY ===");
  console.log("");
  console.log("🎯 DEMONSTRATION COMPLETED");
  console.log("======================");
  console.log("");
  
  console.log("✅ Steps completed:");
  console.log("1. Admin token obtained");
  console.log("2. App client UUID retrieved");
  console.log("3. Audience mapper configured");
  console.log("4. App user token obtained via OAuth2");
  console.log("5. Token exchange V2 performed");
  console.log("6. Exchanged token validated");
  console.log("7. Token introspection completed");
  console.log("");
  
  console.log("🔑 KEY INSIGHT:");
  console.log("The audience mapper is CRITICAL for token exchange v2 to work.");
  console.log("Without it, you get 'client not in audience' error.");
  console.log("");
  
  console.log("🔧 AUDIENCE MAPPER CONFIGURATION:");
  console.log("• Protocol: openid-connect");
  console.log("• Mapper: oidc-audience-mapper");
  console.log("• Config: included.client.audience = resource_client_id");
  console.log("• Target: access.token.claim = true");
  console.log("");
  
  console.log("🎛️  HTTPYAC OAUTH2 FEATURES USED:");
  console.log("• oauth2 authorization_code flow");
  console.log("• oauth2Session.accessToken access");
  console.log("• Automatic browser opening and callback handling");
  console.log("• No manual PKCE or state management needed");
  console.log("");
  
  console.log("📋 NEXT STEPS:");
  console.log("• Use this pattern for other client pairs");
  console.log("• Configure audience mappers for production clients");
  console.log("• Test with different scopes and audiences");
  console.log("");
  
  console.log("=== END OF DEMO ===");
}}

###
### Cleanup - Clear Variables
# @name cleanup
{{
  const variablesToClear = [
    'admin_token',
    'app_client_uuid',
    'app_user_token',
    'exchanged_token'
  ];
  
  variablesToClear.forEach(varName => {
    if ($global[varName]) {
      delete $global[varName];
    }
  });
  
  console.log("✅ Variables cleared");
}} 