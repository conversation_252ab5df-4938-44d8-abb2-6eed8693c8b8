### Unit Test Environment - Direct Access Grant Flow
###
# Import shared variables
# @import ./common.http

###
### Get Dev Console User Token (Direct Access Grant)
# @name get_dev_console_user_token
# Uses direct access grant enabled in test-env.json for client-bodhi-dev-console
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{dev_console_client_id}}:{{dev_console_client_secret}}

grant_type=password&username={{user_regular}}&password={{user_regular_password}}

{{@response
  if (response.statusCode === 200) {
    $global.dev_console_user_token = extractToken(response, 'access_token');
    console.log("✅ Dev console user token obtained via direct access grant");
    
    // Validate token claims
    const tokenUserInfo = extractUserInfo($global.dev_console_user_token);
    console.log("Token issued for client:", tokenUserInfo.azp);
    console.log("Token subject:", tokenUserInfo.sub);
  } else {
    handleError("Dev console user token request failed", response);
  }
}}

###
### Create App Client (Public, No Secrets)
# @name create_app_client
# @ref get_dev_console_user_token
POST {{bodhi_apps_endpoint}}
Authorization: Bearer {{$global.dev_console_user_token}}
Content-Type: {{json_content_type}}

{
  "name": "Unit Test App Client",
  "description": "App client for unit testing direct access grant flows",
  "redirect_uris": ["http://localhost:3000/callback"]
}

{{@response
  if (response.statusCode === 201) {
    const body = response.parsedBody || response.body;
    $global.app_client_id = body.client_id;
    console.log("✅ Created app client:", $global.app_client_id);
  } else {
    handleError("Failed to create app client", response);
  }
}}

###
### Create Resource Server Client (Confidential, With Secrets)
# @name create_resource_client
# @ref get_dev_console_user_token
POST {{bodhi_resources_endpoint}}
Content-Type: {{json_content_type}}

{
  "name": "Unit Test Resource Server",
  "description": "Resource client for unit testing token exchange",
  "redirect_uris": ["http://localhost:8080/callback"]
}

{{@response
  if (response.statusCode === 201) {
    const body = response.parsedBody || response.body;
    $global.resource_client_id = body.client_id;
    $global.resource_client_secret = body.client_secret;
    $global.resource_scope_name = body.scope;
    console.log("✅ Created resource client:", $global.resource_client_id);
    console.log("✅ Resource scope name:", $global.resource_scope_name);
  } else {
    handleError("Failed to create resource client", response);
  }
}}

###
### Get Resource Client Service Account Token
# @name get_resource_service_token
# @ref create_resource_client
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

grant_type=client_credentials&scope=service_account

{{@response
  if (response.statusCode === 200) {
    $global.resource_service_token = extractToken(response, 'access_token');
    console.log("✅ Resource client service account token obtained");
  } else {
    handleError("Resource client service token request failed", response);
  }
}}

###
### Make First Resource Admin
# @name make_first_resource_admin
# @ref get_resource_service_token
# Makes a user the first admin of the resource client
POST {{make_admin_endpoint}}
Authorization: Bearer {{$global.resource_service_token}}
Content-Type: {{json_content_type}}

{
  "username": "{{user_regular}}"
}

{{@response
  if (response.statusCode === 201) {
  } else {
    handleError("Make admin failed", response);
  }
}}

###
### Resource Client Requests Audience Access (Dynamic On-Demand)
# @name request_audience_access
# @ref get_resource_service_token
POST {{keycloak}}/realms/{{realm}}/{{provider_id}}/resources/request-access
Authorization: Bearer {{$global.resource_service_token}}
Content-Type: {{json_content_type}}

{
  "app_client_id": "{{$global.app_client_id}}"
}

{{@response
  if (response.statusCode === 201 || response.statusCode === 200) {
    const body = response.parsedBody || response.body;
    // Update scope name if returned (might be different from creation response)
    if (body.scope) {
      $global.resource_scope_name = body.scope;
    }
    console.log("Response status:", response.statusCode);
    console.log("✅ Resource scope name:", $global.resource_scope_name);
  } else {
    handleError("Dynamic audience request failed", response);
  }
}}

###
### App User Token via Direct Access Grant (with Resource Scope)
# @name get_app_user_token_with_scope
# @ref request_audience_access
# CRITICAL: This step tests if the app client now has the resource scope available
# and the user token includes the resource client in audience
POST {{token_endpoint}}
Content-Type: {{form_content_type}}

grant_type=password&client_id={{$global.app_client_id}}&username={{user_regular}}&password={{user_regular_password}}&scope=openid email profile roles scope_user_user {{$global.resource_scope_name}}

{{@response
  if (response.statusCode === 200) {
    $global.app_user_token = extractToken(response, 'access_token');
    console.log("✅ App user token obtained with resource scope");
    
    // Analyze the token for audience validation
    const tokenUserInfo = extractUserInfo($global.app_user_token);
    console.log("--- App User Token Analysis ---");
    console.log("Token issued for client:", tokenUserInfo.azp);
    console.log("Token audience:", tokenUserInfo.aud);
    console.log("Token subject:", tokenUserInfo.sub);
    console.log("Token scopes:", tokenUserInfo.scope);
    
    // Verify the resource client is in the audience
    let hasResourceAudience = false;
    if (Array.isArray(tokenUserInfo.aud)) {
      hasResourceAudience = tokenUserInfo.aud.includes($global.resource_client_id);
    } else if (tokenUserInfo.aud === $global.resource_client_id) {
      hasResourceAudience = true;
    }
    
    if (hasResourceAudience) {
      console.log("✅ SUCCESS: Resource client found in token audience");
      console.log("🎯 READY: Token exchange should work without 'client not in audience' error");
    } else {
      console.log("❌ ISSUE: Resource client not found in token audience");
      console.log("🔍 DEBUG: This may indicate the scope was not properly added to the app client");
    }
  } else {
    handleError("App user token with scope request failed", response);
  }
}}

###
### Token Exchange V2 - Unit Test Validation
# @name token_exchange_unit_test
# @ref get_app_user_token_with_scope
POST {{token_endpoint}}
Content-Type: {{form_content_type}}
Authorization: Basic {{$global.resource_client_id}}:{{$global.resource_client_secret}}

subject_token={{$global.app_user_token}}&scope=openid email profile roles scope_user_user&grant_type=urn:ietf:params:oauth:grant-type:token-exchange&subject_token_type=urn:ietf:params:oauth:token-type:access_token&requested_token_type=urn:ietf:params:oauth:token-type:access_token

{{@response
  if (response.statusCode !== 200) {
    const body = response.parsedBody || response.body;
    throw new Error(`Token exchange failed: ${body.error} - ${body.error_description}`);
  }

  const body = response.parsedBody || response.body;
  $global.exchanged_token = body.access_token;
  
  // Validate exchanged token
  const tokenInfo = extractUserInfo($global.exchanged_token);
  const expectedIssuer = `${keycloak}/realms/${realm}`;
  
  // Assert: Token issued by correct issuer
  if (tokenInfo.iss !== expectedIssuer) {
    throw new Error(`Token issuer mismatch. Expected: ${expectedIssuer}, Got: ${tokenInfo.iss}`);
  }
  
  // Assert: Token issued to resource client
  if (tokenInfo.azp !== $global.resource_client_id) {
    throw new Error(`Token azp mismatch. Expected: ${$global.resource_client_id}, Got: ${tokenInfo.azp}`);
  }
  
  // Assert: Token has email claim
  if (!tokenInfo.email) {
    throw new Error('Token missing email claim');
  }
  
  // Assert: Token has scope_user_user scope
  const tokenScopes = tokenInfo.scope?.split(' ') || [];
  if (!tokenScopes.includes('scope_user_user')) {
    throw new Error(`Token missing scope_user_user. Available scopes: ${tokenInfo.scope}`);
  }
  
  console.log("✅ Token exchange validation passed");
}}

###
### Test Environment Configuration Report
# @name test_environment_report
{{
  console.log("\n=== TEST ENVIRONMENT CONFIGURATION REPORT ===");
  console.log("Environment:", process.env.NODE_ENV || 'test');
  console.log("Keycloak URL:", keycloak);
  console.log("Realm:", realm);
  console.log("Provider ID:", provider_id);
  
  console.log("\n--- Client Configuration ---");
  console.log("Dev Console Client:", dev_console_client_id);
  console.log("App Client ID:", $global.app_client_id || 'NOT_CREATED');
  console.log("Resource Client ID:", $global.resource_client_id || 'NOT_CREATED');
  console.log("Resource Scope Name:", $global.resource_scope_name || 'NOT_AVAILABLE');
  
  console.log("\n--- Test User Configuration ---");
  console.log("App Client User:", user_regular);
  
  console.log("\n--- Token Status ---");
  console.log("Dev Console User Token:", $global.dev_console_user_token ? 'AVAILABLE' : 'MISSING');
  console.log("Resource Service Token:", $global.resource_service_token ? 'AVAILABLE' : 'MISSING');
  console.log("App User Token:", $global.app_user_token ? 'AVAILABLE' : 'MISSING');
  console.log("Exchanged Token:", $global.exchanged_token ? 'AVAILABLE' : 'MISSING');
}}

###
### Cleanup
# @name cleanup
{{
  const variablesToClear = [
    'dev_console_user_token', 'app_client_id', 'resource_client_id', 'resource_client_secret',
    'resource_service_token', 'resource_scope_name', 'app_user_token', 'exchanged_token'
  ];
  
  variablesToClear.forEach(varName => {
    if ($global[varName]) {
      delete $global[varName];
    }
  });
  
  console.log("✅ Variables cleared");
}}
