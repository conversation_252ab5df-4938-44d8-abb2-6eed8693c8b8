#!/bin/bash

# Test script to verify HTTPyac environment switching
# Environment files are organized in the env/ folder following HTTPyac conventions
echo "🌍 HTTPyac Environment Testing"
echo "=============================="
echo "Environment files structure:"
echo "  env/.env          - Global variables (shared)"
echo "  env/local.env     - Local development"
echo "  env/dev.env       - Development environment"
echo "  env/main.env      - Main/integration environment"
echo "  env/test.env      - Test environment"
echo "  env/prod.env      - Production environment"
echo ""

# Function to test environment
test_environment() {
  local env_name="$1"
  local expected_url="$2"
  local env_flag="$3"
  
  echo -e "📍 ${env_name} Environment:"
  echo "Expected: ${expected_url}"
  
  # Run HTTPyac and capture output
  local output
  if [ -n "$env_flag" ]; then
    output=$(httpyac send httpyac-scripts/common.http --env "$env_flag" --name test_utilities 2>&1)
  else
    output=$(httpyac send httpyac-scripts/common.http --name test_utilities 2>&1)
  fi
  
  # Extract KEYCLOAK_URL and admin token success from output
  local actual_url=$(echo "$output" | grep "KEYCLOAK_URL=" | cut -d'=' -f2)
  local admin_token_success=$(echo "$output" | grep "ADMIN_TOKEN_SUCCESS=" | cut -d'=' -f2)
  local admin_username=$(echo "$output" | grep "ADMIN_USERNAME=" | cut -d'=' -f2)
  
  echo "  URL: ${actual_url}"
  echo "  Admin Username: ${admin_username}"
  echo "  Admin Token: ${admin_token_success}"
  
  local url_ok=false
  local admin_ok=false
  
  if [ "$actual_url" = "$expected_url" ]; then
    echo "  ✅ Environment URL correct"
    url_ok=true
  else
    echo "  ❌ Environment URL failed - Expected: ${expected_url}"
  fi
  
  if [ "$admin_token_success" = "true" ]; then
    echo "  ✅ Admin credentials working"
    admin_ok=true
  else
    echo "  ❌ Admin credentials failed (expected for test environments without local Keycloak)"
  fi
  
  if [ "$url_ok" = true ]; then
    echo "✅ ${env_name} environment configuration correct"
  else
    echo "❌ ${env_name} environment configuration failed"
  fi
  echo ""
}

# Test all environments
test_environment "Local (default)" "http://localhost:8080" ""
test_environment "Dev" "https://dev-id.getbodhi.app" "dev"
test_environment "Main" "https://main-id.getbodhi.app" "main"
test_environment "Test" "https://test-id.getbodhi.app" "test"
test_environment "Prod" "https://id.getbodhi.app" "prod"

echo "🎯 Environment testing complete!"
echo "Features:"
echo "  ✅ Global variables in env/.env (shared across all environments)"
echo "  ✅ Environment-specific overrides in env/{environment}.env"
echo "  ✅ Standardized user credentials (@email.com with 'pass' password)"
echo "  ✅ Organized file structure in env/ folder"
echo ""
echo "Usage examples:"
echo "  httpyac send httpyac-scripts/app-clients.http --env dev --all"
echo "  httpyac send httpyac-scripts/token-exchange.http --env main --all"
echo "  httpyac send httpyac-scripts/resource-clients.http --env prod --all" 