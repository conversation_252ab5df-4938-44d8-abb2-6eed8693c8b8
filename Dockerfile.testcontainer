# Testcontainer Dockerfile - Simplified without obfuscation for faster testing
# Optimized for fast container startup AND good application performance

# Import common base stages - support multi-platform builds
FROM --platform=$BUILDPLATFORM openjdk:21-jdk-slim AS deps

WORKDIR /app

# Copy Maven wrapper and configuration
COPY mvnw .
COPY .mvn .mvn/
COPY pom.production.xml pom.xml

# Make Maven wrapper executable
RUN chmod +x ./mvnw

# Download dependencies
RUN ./mvnw dependency:resolve-sources dependency:resolve dependency:go-offline -B

FROM deps AS builder

# Copy source code for building
COPY src/main/ src/main/

# Build the extension (without obfuscation for faster testing)
RUN ./mvnw clean package -o

# Verify JAR exists
RUN ls -la target/ && test -f target/keycloak-bodhi-ext.jar

# Keycloak build stage - only build-time options here
FROM quay.io/keycloak/keycloak:26.2.5 AS optimizer

# Copy our custom extension (no obfuscation)
COPY --from=builder /app/target/keycloak-bodhi-ext.jar /opt/keycloak/providers/

# Build Keycloak with build-time options optimized for testcontainers
RUN /opt/keycloak/bin/kc.sh build \
    --db=dev-mem \
    --http-relative-path=/ \
    --features=token-exchange \
    --health-enabled=true \
    --metrics-enabled=false \
    --transaction-xa-enabled=false

# Final runtime stage
FROM quay.io/keycloak/keycloak:26.2.5

# Copy optimized Keycloak
COPY --from=optimizer /opt/keycloak/ /opt/keycloak/

# Set container memory for good performance (4GB total, 2.8GB heap)
ENV JAVA_OPTS_KC_HEAP="-XX:InitialRAMPercentage=50 -XX:MaxRAMPercentage=70 -XX:MinHeapFreeRatio=10 -XX:MaxHeapFreeRatio=20"

# JVM optimizations for fast startup and good API performance
ENV JAVA_OPTS_APPEND="-server -XX:+UseG1GC -XX:+UseStringDeduplication -XX:+OptimizeStringConcat -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -Djava.security.egd=file:/dev/./urandom -Djava.awt.headless=true -Dfile.encoding=UTF-8 -Duser.timezone=UTC"

# Testcontainer-specific environment
ENV APP_ENV=dev

# Set working directory
WORKDIR /opt/keycloak

# Use ENTRYPOINT for proper signal handling - allows users to pass custom options
# Default optimized configuration for testcontainers (can be overridden)
ENTRYPOINT ["/opt/keycloak/bin/kc.sh"]
CMD ["start", "--optimized", \
     "--hostname=localhost", \
     "--hostname-strict=false", \
     "--hostname-backchannel-dynamic=false", \
     "--http-enabled=true", \
     "--log-level=INFO", \
     "--log-console-format=%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{1}] %s%e%n", \
     "--log-console-color=false"] 