<?xml version="1.0" encoding="UTF-8"?>
<infinispan
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="urn:infinispan:config:13.0 http://www.infinispan.org/schemas/infinispan-config-13.0.xsd"
  xmlns="urn:infinispan:config:13.0">

  <jgroups>
    <stack name="jdbc-ping" extends="udp">
      <JDBC_PING connection_driver="org.postgresql.Driver"
        connection_url="${env.KC_DB_URL}"
        connection_username="${env.KC_DB_USERNAME}"
        connection_password="${env.KC_DB_PASSWORD}"
        initialize_sql="CREATE TABLE IF NOT EXISTS JGROUPSPING (own_addr varchar(200) NOT NULL, cluster_name varchar(200) NOT NULL, ping_data bytea, constraint PK_JGROUPSPING PRIMARY KEY (own_addr, cluster_name))" />
    </stack>
  </jgroups>

  <cache-container name="keycloak">
    <transport stack="jdbc-ping" lock-timeout="60000" />
    <local-cache name="realms">
      <encoding>
        <key media-type="application/x-java-object" />
        <value media-type="application/x-java-object" />
      </encoding>
      <memory max-count="10000" />
    </local-cache>
    <local-cache name="users">
      <encoding>
        <key media-type="application/x-java-object" />
        <value media-type="application/x-java-object" />
      </encoding>
      <memory max-count="10000" />
    </local-cache>
    <distributed-cache name="sessions" owners="2">
      <persistence>
        <jdbc-store xmlns="urn:infinispan:config:store:jdbc:13.0" db="postgresql"
          fetch-state="false" read-only="false" purge="false">
          <data-source jndi-url="java:jboss/datasources/KeycloakDS" />
          <string-keyed-table prefix="ISPN_STRING_TABLE" create-on-start="true">
            <id-column name="ID_COLUMN" type="VARCHAR(255)" />
            <data-column name="DATA_COLUMN" type="BYTEA" />
            <timestamp-column name="TIMESTAMP_COLUMN" type="BIGINT" />
          </string-keyed-table>
        </jdbc-store>
      </persistence>
    </distributed-cache>
  </cache-container>
</infinispan>